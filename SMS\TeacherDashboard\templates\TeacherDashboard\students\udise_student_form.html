{% extends 'TeacherDashboard/base.html' %}
{% load widget_tweaks %}
{% load static %}

{% block title %}
  {% if object %}
    Update Student - UDISE+ Format
  {% else %}
    Add New Student - UDISE+ Format
  {% endif %}
{% endblock title %}

{% block extrastyle %}
<!-- Make sure Bootstrap CSS is loaded -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<style>
  /* UDISE+ Form Styling */
  body {
    background-color: #f5f7fa;
  }

  .content-wrapper {
    background-color: #f5f7fa;
  }

  .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
  }

  .card-header {
    background-color: #ffffff;
    border-bottom: 1px solid #eaedf2;
    padding: 1.25rem 1.5rem;
  }

  .card-body {
    padding: 1.5rem;
  }

  .udise-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
  }

  .udise-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
  }

  .udise-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .udise-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
  }

  .session-info {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
  }

  .udise-sections-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .udise-section {
    display: none;
  }

  .udise-section.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .udise-field-group {
    margin-bottom: 1.5rem;
    position: relative;
  }

  .udise-field-label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
  }

  .udise-field-number {
    display: inline-block;
    background: #3498db;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    text-align: center;
    line-height: 24px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-right: 0.5rem;
  }

  .udise-rule-reference {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-style: italic;
  }

  .udise-field-sublabel {
    font-size: 0.8rem;
    color: #e74c3c;
    margin-bottom: 0.5rem;
  }

  .field-validation-indicator {
    position: absolute;
    top: -8px;
    right: 0;
    background: #e74c3c;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
  }

  .field-validation-indicator.field-required {
    background: #e74c3c;
  }

  .field-validation-indicator.field-optional {
    background: #95a5a6;
  }

  .form-control, .form-select {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
  }

  .form-control:focus, .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
  }

  .form-control.is-valid, .form-select.is-valid {
    border-color: #27ae60;
  }

  .form-control.is-invalid, .form-select.is-invalid {
    border-color: #e74c3c;
  }

  .section-navigation {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
  }

  .section-nav-btn {
    background: white;
    border: 2px solid #dee2e6;
    color: #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    margin: 0.25rem;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    font-weight: 500;
  }

  .section-nav-btn:hover {
    border-color: #3498db;
    color: #3498db;
    transform: translateY(-1px);
  }

  .section-nav-btn.active {
    background: #3498db;
    border-color: #3498db;
    color: white;
  }

  .section-nav-btn.completed {
    background: #27ae60;
    border-color: #27ae60;
    color: white;
  }

  .progress-container {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #ecf0f1;
  }

  .progress-bar {
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  .btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f4e79);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
  }

  .btn-secondary {
    background: #95a5a6;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-1px);
  }

  .college-header {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    margin-bottom: 2rem;
  }

  .college-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .college-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .udise-header {
      padding: 1.5rem;
      text-align: center;
    }

    .udise-title {
      font-size: 1.5rem;
    }

    .udise-subtitle {
      font-size: 1rem;
    }

    .session-info {
      margin-top: 1rem;
      text-align: center;
    }

    .udise-sections-container {
      padding: 1rem;
    }

    .section-nav-btn {
      font-size: 0.8rem;
      padding: 0.4rem 0.8rem;
    }

    .college-header h1 {
      font-size: 2rem;
    }

    .college-header p {
      font-size: 1rem;
    }
  }

  /* Print Styles */
  @media print {
    .section-navigation,
    .progress-container,
    .btn,
    .udise-header .session-info {
      display: none !important;
    }

    .udise-section {
      display: block !important;
      page-break-inside: avoid;
    }

    .card {
      box-shadow: none;
      border: 1px solid #ddd;
    }
  }
</style>
{% endblock extrastyle %}

{% block content %}
<!-- Edit Student Details Card -->
<div class="card mb-4 border-0 shadow-sm">
  <div class="card-header bg-white d-flex align-items-center justify-content-between">
    <h5 class="mb-0 fw-bold">
      {% if object %}
        <i class="fas fa-user-edit text-primary me-2"></i> Edit Student Details
      {% else %}
        <i class="fas fa-user-plus text-primary me-2"></i> Add New Student
      {% endif %}
    </h5>
    <div>
      <a href="{% url 'teacher_students_list' %}" class="btn btn-sm btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to List
      </a>
    </div>
  </div>
  <div class="card-body p-0">

    <form method="post" enctype="multipart/form-data" id="udiseForm" class="needs-validation" novalidate>
      {% csrf_token %}

      <!-- UDISE+ Header -->
      <div class="udise-header d-flex align-items-center mb-4">
        <div class="d-flex flex-column">
          <h2 class="udise-title mb-0">{{ profile.college_name | default:"MySchool" }}</h2>
          <p class="udise-subtitle">{{ profile.college_address | default:"College Address" }}</p>
        </div>

        <div class="ms-auto session-info">
          <span id="current-time">12:00 PM</span> | Academic Year: 2023-24
        </div>
      </div>

      <!-- Progress Container -->
      <div class="progress-container">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span class="text-muted small">Form Completion Progress</span>
          <span class="badge bg-primary" id="progressPercentage">0%</span>
        </div>
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: 0%;" 
               aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" id="formProgress"></div>
        </div>
      </div>

      <!-- Section Navigation -->
      <div class="section-navigation">
        <div class="d-flex flex-wrap justify-content-center" id="sectionNavigation">
          <button type="button" class="section-nav-btn active" data-section="general-profile">
            <i class="fas fa-user me-1"></i> General Profile
          </button>
          <button type="button" class="section-nav-btn" data-section="academic-info">
            <i class="fas fa-graduation-cap me-1"></i> Academic Info
          </button>
          <button type="button" class="section-nav-btn" data-section="contact-details">
            <i class="fas fa-address-book me-1"></i> Contact Details
          </button>
          <button type="button" class="section-nav-btn" data-section="family-info">
            <i class="fas fa-users me-1"></i> Family Info
          </button>
          <button type="button" class="section-nav-btn" data-section="additional-info">
            <i class="fas fa-info-circle me-1"></i> Additional Info
          </button>
        </div>
      </div>

      <!-- Sections Container -->
      <div class="udise-sections-container position-relative" style="min-height: 500px;">
        <!-- Section 1: General Profile -->
        <div id="general-profile" class="udise-section active">
          <div class="row">
          <!-- Student's Name -->
          <div class="col-md-4 mb-4">
            <div class="udise-field-group">
              <span class="field-validation-indicator field-required">Required</span>
              <label class="udise-field-label">
                <span class="udise-field-number">1</span> Student's Name
                <span class="udise-rule-reference">(as Per School record/School Admission Register)</span>
              </label>
              <div class="udise-field-sublabel">
                <span class="text-danger">*</span> <span class="udise-rule-reference"></span>
              </div>
              {{ form.fullname|add_class:"form-control" }}
              {% if form.fullname.errors %}
                <div class="text-danger small mt-1">{{ form.fullname.errors.0 }}</div>
              {% endif %}
            </div>
          </div>
        </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between mt-4">
          <a href="{% url 'teacher_students_list' %}" class="btn btn-secondary">
            <i class="fas fa-times me-1"></i> Cancel
          </a>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i> 
            {% if object %}Update Student{% else %}Save Student{% endif %}
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
{% endblock content %}

{% block extrajs %}
<script>
// Update current time
function updateTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  });
  document.getElementById('current-time').textContent = timeString;
}

// Update time every minute
setInterval(updateTime, 60000);
updateTime(); // Initial call

// Section navigation
document.querySelectorAll('.section-nav-btn').forEach(btn => {
  btn.addEventListener('click', function() {
    const targetSection = this.dataset.section;
    
    // Update navigation buttons
    document.querySelectorAll('.section-nav-btn').forEach(b => b.classList.remove('active'));
    this.classList.add('active');
    
    // Update sections
    document.querySelectorAll('.udise-section').forEach(section => {
      section.classList.remove('active');
    });
    document.getElementById(targetSection).classList.add('active');
  });
});

// Form validation and progress
function updateFormProgress() {
  const form = document.getElementById('udiseForm');
  const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
  let filledInputs = 0;
  
  inputs.forEach(input => {
    if (input.value.trim() !== '') {
      filledInputs++;
    }
  });
  
  const progress = Math.round((filledInputs / inputs.length) * 100);
  document.getElementById('formProgress').style.width = progress + '%';
  document.getElementById('progressPercentage').textContent = progress + '%';
}

// Update progress on input change
document.addEventListener('input', updateFormProgress);
document.addEventListener('change', updateFormProgress);

// Initial progress update
updateFormProgress();
</script>
{% endblock extrajs %}
