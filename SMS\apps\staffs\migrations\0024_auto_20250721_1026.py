# Generated by Django 5.1.3 on 2025-07-21 04:56

from django.db import migrations
from django.contrib.auth.models import User


def create_user_accounts_for_staff(apps, schema_editor):
    Staff = apps.get_model('staffs', 'Staff')
    User = apps.get_model('auth', 'User')

    for staff in Staff.objects.filter(user__isnull=True):
        if staff.staff_login_id and staff.staff_password:
            # Create user account
            try:
                user = User.objects.create_user(
                    username=staff.staff_login_id,
                    password=staff.staff_password,
                    first_name=staff.fullname.split()[0] if staff.fullname else '',
                    last_name=' '.join(staff.fullname.split()[1:]) if len(staff.fullname.split()) > 1 else '',
                    is_staff=True,
                    is_active=True
                )
                staff.user = user
                staff.save()
            except Exception as e:
                print(f"Error creating user for staff {staff.fullname}: {e}")


def reverse_create_user_accounts(apps, schema_editor):
    # This is a data migration, so we don't need to reverse it
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('staffs', '0023_staff_user'),
    ]

    operations = [
        migrations.RunPython(create_user_accounts_for_staff, reverse_create_user_accounts),
    ]
