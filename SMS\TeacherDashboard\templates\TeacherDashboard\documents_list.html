{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-folder-open"></i> Documents
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-folder-open{% endblock title-icon %}
{% block title %}Documents{% endblock title %}
{% block subtitle %}Access and manage your documents{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="fas fa-folder-open me-2"></i>My Documents</h5>
        </div>
        <div class="card-body">
          {% if documents %}
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead class="table-dark">
                  <tr>
                    <th>Document Name</th>
                    <th>Type</th>
                    <th>Upload Date</th>
                    <th>Size</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for document in documents %}
                  <tr>
                    <td>{{ document.title }}</td>
                    <td>{{ document.document_type|default:"N/A" }}</td>
                    <td>{{ document.created_at|date:"d M Y"|default:"N/A" }}</td>
                    <td>{{ document.file.size|filesizeformat|default:"N/A" }}</td>
                    <td>
                      <a href="{{ document.file.url }}" class="btn btn-sm btn-primary" target="_blank">
                        <i class="fas fa-download"></i> Download
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Documents</h5>
              <p class="text-muted">No documents are currently available.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
