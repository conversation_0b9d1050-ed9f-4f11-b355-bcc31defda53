# Generated by Django 5.2 on 2025-04-21 05:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("corecode", "0013_delete_themesettings"),
        ("staffs", "0017_rename_fullnaame_staff_fullname"),
    ]

    operations = [
        migrations.AddField(
            model_name="subject",
            name="code",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="subject",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name="subject",
            name="department",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="subject",
            name="description",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="subject",
            name="is_active",
            field=models.<PERSON>olean<PERSON>ield(default=True),
        ),
        migrations.AddField(
            model_name="subject",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.CreateModel(
            name="ClassSubject",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("section", models.CharField(blank=True, max_length=10, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "student_class",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="class_subjects",
                        to="corecode.studentclass",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="class_subjects",
                        to="corecode.subject",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_subjects",
                        to="staffs.staff",
                    ),
                ),
            ],
            options={
                "verbose_name": "Class Subject",
                "verbose_name_plural": "Class Subjects",
                "ordering": ["student_class__name", "subject__name"],
                "unique_together": {("subject", "student_class", "section")},
            },
        ),
    ]
