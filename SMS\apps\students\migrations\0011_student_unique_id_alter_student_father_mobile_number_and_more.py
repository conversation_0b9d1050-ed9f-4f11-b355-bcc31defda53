# Generated by Django 5.1.6 on 2025-03-05 05:35

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('students', '0010_remove_student_registration_number'),
    ]

    operations = [
        migrations.AddField(
            model_name='student',
            name='unique_id',
            field=models.CharField(blank=True, max_length=20, unique=True),
        ),
        migrations.AlterField(
            model_name='student',
            name='Father_mobile_number',
            field=models.CharField(default='0000000000', max_length=15, validators=[django.core.validators.RegexValidator(message="Entered mobile number isn't in the correct format!", regex='^[0-9]{10,15}$')]),
        ),
        migrations.AlterField(
            model_name='student',
            name='mobile_number',
            field=models.CharField(blank=True, max_length=13, validators=[django.core.validators.RegexValidator(message="Entered mobile number isn't in the correct format!", regex='^[0-9]{10,15}$')]),
        ),
    ]
