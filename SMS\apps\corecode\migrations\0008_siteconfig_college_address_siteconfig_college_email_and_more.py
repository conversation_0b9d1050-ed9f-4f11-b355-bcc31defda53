# Generated by Django 4.1.2 on 2025-03-21 08:04

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("corecode", "0007_remove_siteconfig_description"),
    ]

    operations = [
        migrations.AddField(
            model_name="siteconfig",
            name="college_address",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="siteconfig",
            name="college_email",
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name="siteconfig",
            name="college_logo",
            field=models.ImageField(blank=True, null=True, upload_to="logos/"),
        ),
        migrations.AddField(
            model_name="siteconfig",
            name="college_name",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="siteconfig",
            name="college_phone",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=15, null=True),
        ),
        migrations.AddField(
            model_name="siteconfig",
            name="college_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Government", "Government"),
                    ("Private", "Private"),
                    ("Semi-Government", "Semi-Government"),
                ],
                max_length=20,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="siteconfig",
            name="established_year",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="siteconfig",
            name="principal_name",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
    ]
