# Generated by Django 5.1.6 on 2025-03-05 10:56

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('staffs', '0014_rename_subject_specification_staff_subject_specification_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='staff',
            name='aadhar',
            field=models.CharField(blank=True, max_length=12, validators=[django.core.validators.RegexValidator(code='invalid_aadhar', message='Aadhaar number must be exactly 12 digits.', regex='^\\d{12}$')]),
        ),
    ]
