{% extends 'base.html' %}
{% load static %}

{% block title %}{% if object %}Edit Document Type{% else %}New Document Type{% endif %}{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:type_list' %}">Document Types</a></li>
        <li class="breadcrumb-item active" aria-current="page">{% if object %}Edit Document Type{% else %}New Document Type{% endif %}</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-md-8 mx-auto">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h4 class="mb-0">{% if object %}Edit Document Type{% else %}New Document Type{% endif %}</h4>
        </div>
        <div class="card-body">
          <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="mb-3">
              <label for="id_name" class="form-label">Type Name <span class="text-danger">*</span></label>
              {{ form.name.errors }}
              <input type="text" class="form-control {% if form.name.errors %}is-invalid{% endif %}" id="id_name" name="name" value="{{ form.name.value|default:'' }}" required>
            </div>
            
            <div class="mb-3">
              <label for="id_entity_type" class="form-label">Entity Type <span class="text-danger">*</span></label>
              {{ form.entity_type.errors }}
              <select class="form-select {% if form.entity_type.errors %}is-invalid{% endif %}" id="id_entity_type" name="entity_type" required>
                {% for value, text in form.entity_type.field.choices %}
                <option value="{{ value }}" {% if form.entity_type.value == value %}selected{% endif %}>{{ text }}</option>
                {% endfor %}
              </select>
            </div>
            
            <div class="mb-3">
              <label for="id_category" class="form-label">Category <span class="text-danger">*</span></label>
              {{ form.category.errors }}
              <select class="form-select {% if form.category.errors %}is-invalid{% endif %}" id="id_category" name="category" required>
                <option value="">Select Category</option>
                {% for choice in form.category.field.queryset %}
                <option value="{{ choice.id }}" {% if form.category.value|stringformat:'s' == choice.id|stringformat:'s' %}selected{% endif %}>{{ choice.name }}</option>
                {% endfor %}
              </select>
              <div class="form-text">
                <a href="{% url 'documents:category_create' %}" target="_blank">
                  <i class="fas fa-plus-circle me-1"></i> Add New Category
                </a>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="id_description" class="form-label">Description</label>
              {{ form.description.errors }}
              <textarea class="form-control {% if form.description.errors %}is-invalid{% endif %}" id="id_description" name="description" rows="3">{{ form.description.value|default:'' }}</textarea>
            </div>
            
            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="id_required" name="required" {% if form.required.value %}checked{% endif %}>
                <label class="form-check-label" for="id_required">
                  Required Document
                </label>
                <div class="form-text">Check this if this document type is mandatory</div>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="id_template" class="form-label">Template File (Optional)</label>
              {{ form.template.errors }}
              <input type="file" class="form-control {% if form.template.errors %}is-invalid{% endif %}" id="id_template" name="template">
              <div class="form-text">Upload a template file for this document type (PDF, DOC, DOCX)</div>
              {% if object and object.template %}
              <div class="mt-2">
                <strong>Current template:</strong> 
                <a href="{{ object.template.url }}" target="_blank">{{ object.template.name|slice:"20:" }}</a>
              </div>
              {% endif %}
            </div>
            
            <div class="d-flex justify-content-end">
              <a href="{% url 'documents:type_list' %}" class="btn btn-outline-secondary me-2">Cancel</a>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i> {% if object %}Update{% else %}Save{% endif %} Document Type
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
