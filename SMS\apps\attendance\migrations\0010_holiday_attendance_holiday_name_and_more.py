# Generated by Django 5.2 on 2025-04-15 09:43

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("attendance", "0009_attendance_comment_alter_attendance_date_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Holiday",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(unique=True)),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.AddField(
            model_name="attendance",
            name="holiday_name",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="attendance",
            name="is_holiday",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="attendance",
            name="status",
            field=models.Char<PERSON>ield(
                choices=[
                    ("Present", "Present"),
                    ("Absent", "Absent"),
                    ("Leave", "Leave"),
                    ("Holiday", "Holiday"),
                    ("Sunday", "Sunday"),
                ],
                default="Absent",
                max_length=10,
            ),
        ),
    ]
