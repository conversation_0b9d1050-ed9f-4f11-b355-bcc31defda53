# Generated by Django 5.1.6 on 2025-03-06 05:46

import django.core.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='nonteachingStaff',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_status', models.Char<PERSON>ield(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', max_length=10)),
                ('surname', models.<PERSON>r<PERSON><PERSON>(max_length=200)),
                ('firstname', models.Char<PERSON><PERSON>(max_length=200)),
                ('other_name', models.Char<PERSON>ield(blank=True, max_length=200)),
                ('gender', models.Char<PERSON>ield(choices=[('male', 'Male'), ('female', 'Female')], default='male', max_length=10)),
                ('date_of_birth', models.Date<PERSON>ield(default=django.utils.timezone.now)),
                ('date_of_registration', models.DateField(default=django.utils.timezone.now)),
                ('mobile_number', models.Char<PERSON>ield(blank=True, max_length=13, validators=[django.core.validators.RegexValidator(message="Entered mobile number isn't in a right format!", regex='^[0-9]{10,15}$')])),
                ('address', models.TextField(blank=True)),
                ('others', models.TextField(blank=True)),
            ],
        ),
    ]
