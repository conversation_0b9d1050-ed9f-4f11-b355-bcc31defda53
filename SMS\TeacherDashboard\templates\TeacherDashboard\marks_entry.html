{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_exams_list' %}" class="text-decoration-none">
          <i class="fas fa-graduation-cap"></i> Examinations
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-clipboard-check"></i> Marks Entry
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-clipboard-check{% endblock title-icon %}
{% block title %}Marks Entry{% endblock title %}
{% block subtitle %}Enter marks for examinations{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Marks Entry</h5>
        </div>
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-md-4">
              <label for="exam_select" class="form-label">Exam:</label>
              <select class="form-select" id="exam_select">
                <option value="">Select Exam</option>
                {% for exam in exams %}
                <option value="{{ exam.id }}">{{ exam.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-4">
              <label for="class_select" class="form-label">Class:</label>
              <select class="form-select" id="class_select">
                <option value="">Select Class</option>
                <option value="1">Class 1</option>
                <option value="2">Class 2</option>
                <!-- Add more classes as needed -->
              </select>
            </div>
            <div class="col-md-4">
              <label for="subject_select" class="form-label">Subject:</label>
              <select class="form-select" id="subject_select">
                <option value="">Select Subject</option>
                <option value="math">Mathematics</option>
                <option value="english">English</option>
                <!-- Add more subjects as needed -->
              </select>
            </div>
          </div>
          
          {% if students %}
            <form method="post" action="#">
              {% csrf_token %}
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead class="table-dark">
                    <tr>
                      <th>Roll No.</th>
                      <th>Student Name</th>
                      <th>Marks Obtained</th>
                      <th>Total Marks</th>
                      <th>Grade</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for student in students %}
                    <tr>
                      <td>{{ student.registration_number|default:"N/A" }}</td>
                      <td>{{ student.fullname }}</td>
                      <td>
                        <input type="number" class="form-control" name="marks_{{ student.id }}" min="0" max="100" placeholder="0">
                      </td>
                      <td>
                        <input type="number" class="form-control" name="total_marks_{{ student.id }}" value="100" readonly>
                      </td>
                      <td>
                        <span class="badge bg-secondary" id="grade_{{ student.id }}">-</span>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
              
              <div class="row mt-4">
                <div class="col-12">
                  <button type="submit" class="btn btn-success me-2">
                    <i class="fas fa-save"></i> Save Marks
                  </button>
                  <a href="{% url 'teacher_exams_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Exams
                  </a>
                </div>
              </div>
            </form>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Students Found</h5>
              <p class="text-muted">Please select exam, class, and subject to enter marks.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Auto-calculate grade based on marks
document.addEventListener('DOMContentLoaded', function() {
  const marksInputs = document.querySelectorAll('input[name^="marks_"]');
  
  marksInputs.forEach(input => {
    input.addEventListener('input', function() {
      const studentId = this.name.split('_')[1];
      const marks = parseInt(this.value) || 0;
      const gradeSpan = document.getElementById(`grade_${studentId}`);
      
      let grade = 'F';
      let gradeClass = 'bg-danger';
      
      if (marks >= 90) {
        grade = 'A+';
        gradeClass = 'bg-success';
      } else if (marks >= 80) {
        grade = 'A';
        gradeClass = 'bg-success';
      } else if (marks >= 70) {
        grade = 'B';
        gradeClass = 'bg-info';
      } else if (marks >= 60) {
        grade = 'C';
        gradeClass = 'bg-warning';
      } else if (marks >= 50) {
        grade = 'D';
        gradeClass = 'bg-warning';
      }
      
      gradeSpan.textContent = grade;
      gradeSpan.className = `badge ${gradeClass}`;
    });
  });
});
</script>
{% endblock content %}
