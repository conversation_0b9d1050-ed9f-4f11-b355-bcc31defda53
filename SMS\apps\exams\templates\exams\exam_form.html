{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-graduation-cap"></i> Examination
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:exam_list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-calendar-alt"></i> Exams
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-{% if object %}edit{% else %}plus{% endif %}"></i> 
        {% if object %}Edit{% else %}Create{% endif %} Exam
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-{% if object %}edit{% else %}plus{% endif %}{% endblock title-icon %}

{% block title %}{% if object %}Edit{% else %}Create{% endif %} Exam{% endblock title %}

{% block subtitle %}{% if object %}Update existing{% else %}Create new{% endif %} examination{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-md-10 mx-auto">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">
            <i class="fas fa-{% if object %}edit{% else %}plus{% endif %} me-2"></i>
            {% if object %}Edit{% else %}Create{% endif %} Exam
          </h5>
        </div>
        <div class="card-body">
          <form method="post">
            {% csrf_token %}
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.name.id_for_label }}" class="form-label">Exam Name <span class="text-danger">*</span></label>
                {{ form.name|add_class:"form-control"|attr:"placeholder:Enter exam name" }}
                {% if form.name.errors %}
                  <div class="text-danger">
                    {% for error in form.name.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-6">
                <label for="{{ form.exam_type.id_for_label }}" class="form-label">Exam Type <span class="text-danger">*</span></label>
                {{ form.exam_type|add_class:"form-select" }}
                {% if form.exam_type.errors %}
                  <div class="text-danger">
                    {% for error in form.exam_type.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
                <small class="text-muted">
                  <a href="{% url 'exams:exam_type_create' %}" target="_blank">
                    <i class="fas fa-plus-circle"></i> Add New Exam Type
                  </a>
                </small>
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.session.id_for_label }}" class="form-label">Academic Session <span class="text-danger">*</span></label>
                {{ form.session|add_class:"form-select" }}
                {% if form.session.errors %}
                  <div class="text-danger">
                    {% for error in form.session.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-6">
                <label for="{{ form.term.id_for_label }}" class="form-label">Term <span class="text-danger">*</span></label>
                {{ form.term|add_class:"form-select" }}
                {% if form.term.errors %}
                  <div class="text-danger">
                    {% for error in form.term.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date <span class="text-danger">*</span></label>
                {{ form.start_date|add_class:"form-control" }}
                {% if form.start_date.errors %}
                  <div class="text-danger">
                    {% for error in form.start_date.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-6">
                <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date <span class="text-danger">*</span></label>
                {{ form.end_date|add_class:"form-control" }}
                {% if form.end_date.errors %}
                  <div class="text-danger">
                    {% for error in form.end_date.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.status.id_for_label }}" class="form-label">Status <span class="text-danger">*</span></label>
                {{ form.status|add_class:"form-select" }}
                {% if form.status.errors %}
                  <div class="text-danger">
                    {% for error in form.status.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-6">
                <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                {{ form.description|add_class:"form-control"|attr:"placeholder:Enter description (optional)" }}
                {% if form.description.errors %}
                  <div class="text-danger">
                    {% for error in form.description.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
            
            <div class="d-flex justify-content-between">
              <a href="{% url 'exams:exam_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to List
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i> {% if object %}Update{% else %}Save{% endif %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block morejs %}
<script>
  $(document).ready(function() {
    // Add date validation
    $('#{{ form.start_date.id_for_label }}').on('change', function() {
      var startDate = $(this).val();
      $('#{{ form.end_date.id_for_label }}').attr('min', startDate);
    });
    
    $('#{{ form.end_date.id_for_label }}').on('change', function() {
      var endDate = $(this).val();
      $('#{{ form.start_date.id_for_label }}').attr('max', endDate);
    });
  });
</script>
{% endblock morejs %}
