{% extends 'corecode/system_settings_base.html' %}
{% load static %}

{% block settings-icon %}fas fa-database{% endblock settings-icon %}
{% block settings-title %}Database Management{% endblock settings-title %}
{% block settings-icon-title %}fas fa-database{% endblock settings-icon-title %}
{% block settings-page-title %}Database Management{% endblock settings-page-title %}
{% block settings-subtitle %}Manage database operations and maintenance{% endblock settings-subtitle %}

{% block content-icon %}fas fa-database{% endblock content-icon %}
{% block content-title %}Database Tools{% endblock content-title %}

{% block settings-content %}
<div class="row mb-4">
  <div class="col-md-12">
    <div class="alert alert-warning d-flex align-items-center" role="alert">
      <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
      <div>
        <h5 class="alert-heading">Database Management</h5>
        <p class="mb-0">These tools perform operations directly on your database. Use with caution as some actions cannot be undone.</p>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- Database Information -->
  <div class="col-md-12 mb-4">
    <div class="card settings-card">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Database Information</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label fw-bold">Database Type</label>
              <div class="form-control bg-light">SQLite</div>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Database Size</label>
              <div class="form-control bg-light">{{ db_size|default:"12.4 MB" }}</div>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Last Optimized</label>
              <div class="form-control bg-light">{{ last_optimized|default:"2023-04-10 09:15 AM" }}</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label fw-bold">Total Tables</label>
              <div class="form-control bg-light">{{ total_tables|default:"32" }}</div>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Total Records</label>
              <div class="form-control bg-light">{{ total_records|default:"5,842" }}</div>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Database Status</label>
              <div class="form-control bg-light text-success">Healthy</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Database Operations -->
  <div class="col-md-6 mb-4">
    <div class="card settings-card h-100">
      <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-tools me-2"></i> Database Operations</h5>
      </div>
      <div class="card-body">
        <div class="list-group">
          <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="optimizeBtn">
            <div>
              <h6 class="mb-1"><i class="fas fa-bolt text-warning me-2"></i> Optimize Database</h6>
              <p class="mb-0 text-muted small">Improve performance by optimizing database structure</p>
            </div>
            <span class="badge bg-primary rounded-pill">Run</span>
          </button>
          <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="vacuumBtn">
            <div>
              <h6 class="mb-1"><i class="fas fa-broom text-success me-2"></i> Vacuum Database</h6>
              <p class="mb-0 text-muted small">Reclaim unused space and defragment the database</p>
            </div>
            <span class="badge bg-primary rounded-pill">Run</span>
          </button>
          <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="checkBtn">
            <div>
              <h6 class="mb-1"><i class="fas fa-check-circle text-info me-2"></i> Check Integrity</h6>
              <p class="mb-0 text-muted small">Verify database integrity and check for corruption</p>
            </div>
            <span class="badge bg-primary rounded-pill">Run</span>
          </button>
          <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="repairBtn">
            <div>
              <h6 class="mb-1"><i class="fas fa-wrench text-danger me-2"></i> Repair Database</h6>
              <p class="mb-0 text-muted small">Attempt to repair database issues (if any)</p>
            </div>
            <span class="badge bg-primary rounded-pill">Run</span>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Data Management -->
  <div class="col-md-6 mb-4">
    <div class="card settings-card h-100">
      <div class="card-header bg-danger text-white">
        <h5 class="mb-0"><i class="fas fa-exclamation-circle me-2"></i> Data Management</h5>
      </div>
      <div class="card-body">
        <div class="alert alert-danger">
          <i class="fas fa-exclamation-triangle me-2"></i> These operations permanently modify data and cannot be undone. Use with extreme caution.
        </div>
        
        <div class="list-group mt-3">
          <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="clearLogsBtn">
            <div>
              <h6 class="mb-1"><i class="fas fa-eraser text-warning me-2"></i> Clear System Logs</h6>
              <p class="mb-0 text-muted small">Remove all system logs to free up space</p>
            </div>
            <span class="badge bg-danger rounded-pill">Clear</span>
          </button>
          <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="purgeOldDataBtn">
            <div>
              <h6 class="mb-1"><i class="fas fa-calendar-times text-danger me-2"></i> Purge Old Data</h6>
              <p class="mb-0 text-muted small">Remove data older than a specified date</p>
            </div>
            <span class="badge bg-danger rounded-pill">Purge</span>
          </button>
          <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="resetDemoBtn">
            <div>
              <h6 class="mb-1"><i class="fas fa-redo-alt text-info me-2"></i> Reset Demo Data</h6>
              <p class="mb-0 text-muted small">Reset the database to demo state with sample data</p>
            </div>
            <span class="badge bg-danger rounded-pill">Reset</span>
          </button>
          <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="factoryResetBtn">
            <div>
              <h6 class="mb-1"><i class="fas fa-trash-alt text-danger me-2"></i> Factory Reset</h6>
              <p class="mb-0 text-muted small">Reset the entire database to factory defaults</p>
            </div>
            <span class="badge bg-danger rounded-pill">Reset</span>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Database Statistics -->
  <div class="col-md-12 mb-4">
    <div class="card settings-card">
      <div class="card-header bg-success text-white">
        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i> Database Statistics</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <canvas id="tableRecordsChart" width="400" height="250"></canvas>
          </div>
          <div class="col-md-6">
            <canvas id="databaseGrowthChart" width="400" height="250"></canvas>
          </div>
        </div>
        
        <div class="table-responsive mt-4">
          <table class="table table-striped table-hover">
            <thead class="table-light">
              <tr>
                <th>Table Name</th>
                <th>Records</th>
                <th>Size</th>
                <th>Last Updated</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>students</td>
                <td>1,245</td>
                <td>3.2 MB</td>
                <td>2023-04-16</td>
              </tr>
              <tr>
                <td>staffs</td>
                <td>87</td>
                <td>1.5 MB</td>
                <td>2023-04-15</td>
              </tr>
              <tr>
                <td>classes</td>
                <td>24</td>
                <td>0.3 MB</td>
                <td>2023-04-10</td>
              </tr>
              <tr>
                <td>subjects</td>
                <td>56</td>
                <td>0.5 MB</td>
                <td>2023-04-12</td>
              </tr>
              <tr>
                <td>results</td>
                <td>3,245</td>
                <td>5.8 MB</td>
                <td>2023-04-16</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Form Navigation -->
  <div class="col-md-12">
    <div class="d-flex justify-content-between">
      <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='{% url 'system_settings_dashboard' %}'">
        <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
      </button>
      <button type="button" class="btn btn-primary" id="refreshStatsBtn">
        <i class="fas fa-sync-alt me-2"></i> Refresh Statistics
      </button>
    </div>
  </div>
</div>

<!-- Purge Data Modal -->
<div class="modal fade" id="purgeDataModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i> Purge Old Data</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-circle me-2"></i> This action will permanently delete data older than the specified date. This cannot be undone.
        </div>
        
        <form id="purgeDataForm">
          <div class="mb-3">
            <label class="form-label fw-bold">Purge Data Older Than</label>
            <input type="date" class="form-control" id="purgeDate" required>
          </div>
          
          <div class="mb-3">
            <label class="form-label fw-bold">Select Data Types to Purge</label>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="purgeAttendance" checked>
              <label class="form-check-label" for="purgeAttendance">Attendance Records</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="purgeLogs" checked>
              <label class="form-check-label" for="purgeLogs">System Logs</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="purgeResults">
              <label class="form-check-label" for="purgeResults">Exam Results</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="purgePayments">
              <label class="form-check-label" for="purgePayments">Payment Records</label>
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label fw-bold">Confirmation</label>
            <div class="input-group">
              <span class="input-group-text">Type "CONFIRM"</span>
              <input type="text" class="form-control" id="purgeConfirm" required>
            </div>
            <div class="form-text text-danger">This action cannot be undone.</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmPurgeBtn">Purge Data</button>
      </div>
    </div>
  </div>
</div>

<!-- Factory Reset Modal -->
<div class="modal fade" id="factoryResetModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i> Factory Reset</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-danger">
          <i class="fas fa-skull-crossbones me-2"></i> <strong>WARNING:</strong> This action will permanently delete ALL data and reset the system to factory defaults. This cannot be undone.
        </div>
        
        <form id="factoryResetForm">
          <div class="mb-3">
            <label class="form-label fw-bold">Administrator Password</label>
            <input type="password" class="form-control" id="adminPassword" required>
          </div>
          
          <div class="mb-3">
            <label class="form-label fw-bold">Confirmation</label>
            <div class="input-group">
              <span class="input-group-text">Type "FACTORY RESET"</span>
              <input type="text" class="form-control" id="factoryResetConfirm" required>
            </div>
            <div class="form-text text-danger">All data will be permanently deleted.</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmFactoryResetBtn">Reset System</button>
      </div>
    </div>
  </div>
</div>
{% endblock settings-content %}

{% block settings-js %}
<script>
  $(document).ready(function() {
    // Initialize modals
    var purgeDataModal = new bootstrap.Modal(document.getElementById('purgeDataModal'));
    var factoryResetModal = new bootstrap.Modal(document.getElementById('factoryResetModal'));
    
    // Database Operations
    $('#optimizeBtn').on('click', function() {
      $(this).html('<i class="fas fa-spinner fa-spin me-2"></i> Optimizing...');
      
      // Simulate operation
      setTimeout(function() {
        $('#optimizeBtn').html('<div><h6 class="mb-1"><i class="fas fa-bolt text-warning me-2"></i> Optimize Database</h6><p class="mb-0 text-muted small">Improve performance by optimizing database structure</p></div><span class="badge bg-primary rounded-pill">Run</span>');
        toastr.success('Database optimized successfully');
      }, 2000);
    });
    
    $('#vacuumBtn').on('click', function() {
      $(this).html('<i class="fas fa-spinner fa-spin me-2"></i> Vacuuming...');
      
      // Simulate operation
      setTimeout(function() {
        $('#vacuumBtn').html('<div><h6 class="mb-1"><i class="fas fa-broom text-success me-2"></i> Vacuum Database</h6><p class="mb-0 text-muted small">Reclaim unused space and defragment the database</p></div><span class="badge bg-primary rounded-pill">Run</span>');
        toastr.success('Database vacuumed successfully');
      }, 2000);
    });
    
    $('#checkBtn').on('click', function() {
      $(this).html('<i class="fas fa-spinner fa-spin me-2"></i> Checking...');
      
      // Simulate operation
      setTimeout(function() {
        $('#checkBtn').html('<div><h6 class="mb-1"><i class="fas fa-check-circle text-info me-2"></i> Check Integrity</h6><p class="mb-0 text-muted small">Verify database integrity and check for corruption</p></div><span class="badge bg-primary rounded-pill">Run</span>');
        toastr.success('Database integrity check completed. No issues found.');
      }, 2000);
    });
    
    $('#repairBtn').on('click', function() {
      $(this).html('<i class="fas fa-spinner fa-spin me-2"></i> Repairing...');
      
      // Simulate operation
      setTimeout(function() {
        $('#repairBtn').html('<div><h6 class="mb-1"><i class="fas fa-wrench text-danger me-2"></i> Repair Database</h6><p class="mb-0 text-muted small">Attempt to repair database issues (if any)</p></div><span class="badge bg-primary rounded-pill">Run</span>');
        toastr.info('Database repair completed. No repairs needed.');
      }, 2000);
    });
    
    // Data Management
    $('#clearLogsBtn').on('click', function() {
      if (confirm('Are you sure you want to clear all system logs? This action cannot be undone.')) {
        $(this).html('<i class="fas fa-spinner fa-spin me-2"></i> Clearing...');
        
        // Simulate operation
        setTimeout(function() {
          $('#clearLogsBtn').html('<div><h6 class="mb-1"><i class="fas fa-eraser text-warning me-2"></i> Clear System Logs</h6><p class="mb-0 text-muted small">Remove all system logs to free up space</p></div><span class="badge bg-danger rounded-pill">Clear</span>');
          toastr.success('System logs cleared successfully');
        }, 2000);
      }
    });
    
    $('#purgeOldDataBtn').on('click', function() {
      purgeDataModal.show();
    });
    
    $('#resetDemoBtn').on('click', function() {
      if (confirm('Are you sure you want to reset to demo data? All current data will be replaced with sample data.')) {
        $(this).html('<i class="fas fa-spinner fa-spin me-2"></i> Resetting...');
        
        // Simulate operation
        setTimeout(function() {
          $('#resetDemoBtn').html('<div><h6 class="mb-1"><i class="fas fa-redo-alt text-info me-2"></i> Reset Demo Data</h6><p class="mb-0 text-muted small">Reset the database to demo state with sample data</p></div><span class="badge bg-danger rounded-pill">Reset</span>');
          toastr.success('Database reset to demo state successfully');
        }, 3000);
      }
    });
    
    $('#factoryResetBtn').on('click', function() {
      factoryResetModal.show();
    });
    
    // Confirm Purge Data
    $('#confirmPurgeBtn').on('click', function() {
      const purgeDate = $('#purgeDate').val();
      const confirmText = $('#purgeConfirm').val();
      
      if (!purgeDate) {
        toastr.error('Please select a date');
        return;
      }
      
      if (confirmText !== 'CONFIRM') {
        toastr.error('Please type "CONFIRM" to proceed');
        return;
      }
      
      // Simulate operation
      $(this).html('<i class="fas fa-spinner fa-spin me-2"></i> Purging...');
      
      setTimeout(function() {
        $('#confirmPurgeBtn').html('Purge Data');
        purgeDataModal.hide();
        toastr.success('Old data purged successfully');
      }, 3000);
    });
    
    // Confirm Factory Reset
    $('#confirmFactoryResetBtn').on('click', function() {
      const adminPassword = $('#adminPassword').val();
      const confirmText = $('#factoryResetConfirm').val();
      
      if (!adminPassword) {
        toastr.error('Please enter administrator password');
        return;
      }
      
      if (confirmText !== 'FACTORY RESET') {
        toastr.error('Please type "FACTORY RESET" to proceed');
        return;
      }
      
      // Simulate operation
      $(this).html('<i class="fas fa-spinner fa-spin me-2"></i> Resetting...');
      
      setTimeout(function() {
        $('#confirmFactoryResetBtn').html('Reset System');
        factoryResetModal.hide();
        toastr.success('System reset to factory defaults successfully');
      }, 4000);
    });
    
    // Refresh Statistics
    $('#refreshStatsBtn').on('click', function() {
      $(this).html('<i class="fas fa-spinner fa-spin me-2"></i> Refreshing...');
      
      // Simulate operation
      setTimeout(function() {
        $('#refreshStatsBtn').html('<i class="fas fa-sync-alt me-2"></i> Refresh Statistics');
        toastr.success('Statistics refreshed successfully');
        
        // Update charts
        tableRecordsChart.update();
        databaseGrowthChart.update();
      }, 2000);
    });
    
    // Initialize Charts
    var tableRecordsCtx = document.getElementById('tableRecordsChart').getContext('2d');
    var tableRecordsChart = new Chart(tableRecordsCtx, {
      type: 'bar',
      data: {
        labels: ['Students', 'Staffs', 'Classes', 'Subjects', 'Results'],
        datasets: [{
          label: 'Number of Records',
          data: [1245, 87, 24, 56, 3245],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 99, 132, 0.6)'
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 99, 132, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: 'Records by Table'
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
    
    var databaseGrowthCtx = document.getElementById('databaseGrowthChart').getContext('2d');
    var databaseGrowthChart = new Chart(databaseGrowthCtx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: 'Database Size (MB)',
          data: [8.2, 9.1, 10.5, 11.8, 12.4, 12.4],
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        plugins: {
          title: {
            display: true,
            text: 'Database Growth Over Time'
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Size (MB)'
            }
          }
        }
      }
    });
  });
</script>
{% endblock settings-js %}
