{% extends 'base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<li class="breadcrumb-item active" aria-current="page">Dashboard</li>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-tachometer-alt{% endblock title-icon %}

{% block title %}Dashboard{% endblock title %}

{% block subtitle %}Welcome to the school management system dashboard{% endblock subtitle %}

{% block page-actions %}
<a href="{% url 'current-session' %}" class="btn btn-sm btn-outline-primary">
  <i class="fas fa-calendar-alt"></i> Current Session: {{ current_session }}
</a>
{% endblock page-actions %}

{% block content %}
<div class="container mt-4">
  <div class="row mb-4">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="fas fa-user-cog me-2"></i> Account Overview</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for info in account_info %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ info.label }}
                <span class="badge bg-primary">{{ info.value }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No account info available.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="fas fa-history me-2"></i> Recent Activity</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for activity in account_activities %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ activity.action }}
                <span class="badge bg-secondary">{{ activity.date }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No recent activity.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0"><i class="fas fa-cogs me-2"></i> Settings</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for setting in account_settings %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ setting.label }}
                <span class="badge bg-info">{{ setting.value }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No settings found.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i> Personal Info</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for info in personal_info %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ info.label }}
                <span class="badge bg-success">{{ info.value }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No personal info found.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}