{% extends 'base.html' %}
{% load widget_tweaks %}

{% block extrastyle %}
<style>
  /* Form section styling */
  .section-card {
    transition: all 0.3s ease;
    border-left: 0px solid #3949ab !important;
  }

  .section-card.active-section {
    border-left: 5px solid #3949ab !important;
    box-shadow: 0 5px 15px rgba(57, 73, 171, 0.1) !important;
  }

  .section-card .card-header {
    transition: all 0.3s ease;
  }

  .section-card.active-section .card-header {
    background: linear-gradient(to right, rgba(57, 73, 171, 0.1), rgba(57, 73, 171, 0.05)) !important;
  }

  .section-card.active-section .card-header h6 {
    color: #3949ab;
    font-weight: 600;
  }

  /* Form field styling */
  .form-control:focus, .form-select:focus {
    border-color: #3949ab;
    box-shadow: 0 0 0 0.25rem rgba(57, 73, 171, 0.25);
  }

  .form-control.is-valid, .form-select.is-valid {
    border-color: #28a745;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  }

  .form-control.is-invalid, .form-select.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  }

  .invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
  }

  /* Highlight effect for section navigation */
  .highlight-section {
    animation: highlight-pulse 1s ease;
  }

  @keyframes highlight-pulse {
    0% { background-color: transparent; }
    50% { background-color: rgba(57, 73, 171, 0.1); }
    100% { background-color: transparent; }
  }
</style>
{% endblock extrastyle %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'non-teaching-staffs-list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-users"></i> Non Teaching Staffs
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'non-teaching-staffs-list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-list"></i> List
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-plus-circle"></i> Form
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title %}
  {% if object %}
    Update {{ object.fullname }}
  {% else %}
    Add New Non-Teaching Staff
  {% endif %}
{% endblock title %}

{% block content %}
<div class="container-fluid px-0">
  <div class="card shadow-sm border-0 mb-4">
    <div class="card-header bg-gradient-primary text-white">
      <h5 class="mb-0">
        <i class="fas {% if object %}fa-edit{% else %}fa-user-plus{% endif %} me-2"></i>
        {% if object %}
          Update Staff Information
        {% else %}
          New Staff Registration
        {% endif %}
      </h5>
    </div>
    <div class="card-body">
      <!-- Form Progress Indicator -->
      <div class="row mb-4">
        <div class="col-md-12">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <div>
              <span class="text-muted small">Fill in the form fields below. Fields marked with <span class="text-danger">*</span> are required.</span>
            </div>
            <div>
              <span class="text-muted small me-2">Form completion:</span>
              <span class="badge bg-primary" id="progressPercentage">0%</span>
            </div>
          </div>
          <div class="progress" style="height: 5px;">
            <div class="progress-bar bg-success" role="progressbar" style="width: 0%;"
                 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" id="formProgress"></div>
          </div>
        </div>
      </div>

      <!-- Form Navigation -->
      <div class="row mb-4">
        <div class="col-md-12">
          <div class="form-navigation-container p-3 bg-white rounded shadow-sm border">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h6 class="mb-0 text-primary fw-bold"><i class="fas fa-map-signs me-2"></i>Form Sections</h6>
              <div class="badge bg-primary p-2"><i class="fas fa-info-circle me-1"></i> Navigate between sections</div>
            </div>
            <div class="form-navigation-wrapper p-2 bg-light rounded">
              <div class="d-flex flex-wrap justify-content-between" id="formNavigation">
                <button type="button" class="btn btn-outline-primary active mb-2 mb-md-0 flex-grow-1 mx-1 py-2" data-section="personal">
                  <i class="fas fa-user me-1"></i> Personal
                  <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">1</span>
                </button>
                <button type="button" class="btn btn-outline-primary mb-2 mb-md-0 flex-grow-1 mx-1 py-2" data-section="job">
                  <i class="fas fa-briefcase me-1"></i> Job
                  <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">2</span>
                </button>
                <button type="button" class="btn btn-outline-primary mb-2 mb-md-0 flex-grow-1 mx-1 py-2" data-section="contact">
                  <i class="fas fa-address-book me-1"></i> Contact
                  <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">3</span>
                </button>
                <button type="button" class="btn btn-outline-primary mb-2 mb-md-0 flex-grow-1 mx-1 py-2" data-section="additional">
                  <i class="fas fa-info-circle me-1"></i> Additional
                  <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">4</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <form action="" method="POST" id="nonTeachingStaffForm" class="needs-validation" novalidate>
        {% csrf_token %}

        <!-- Personal Information Section -->
        <div id="personal" class="card mb-4 border-0 shadow-sm section-card">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Fullname Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.fullname.id_for_label }}" class="form-label">
                  <i class="fas fa-user-tag me-1"></i> Full Name <span class="text-danger">*</span>
                </label>
                {{ form.fullname|add_class:"form-control"|attr:"required" }}
                <div class="invalid-feedback"></div>
                {% if form.fullname.errors %}
                  <div class="text-danger small mt-1">{{ form.fullname.errors }}</div>
                {% endif %}
              </div>

              <!-- Gender Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.gender.id_for_label }}" class="form-label">
                  <i class="fas fa-venus-mars me-1"></i> Gender
                </label>
                {{ form.gender|add_class:"form-select" }}
                <div class="invalid-feedback"></div>
              </div>

              <!-- Date of Birth Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                  <i class="fas fa-birthday-cake me-1"></i> Date of Birth
                </label>
                {{ form.date_of_birth|add_class:"form-control" }}
                <div class="invalid-feedback"></div>
              </div>

              <!-- Registration Date Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.date_of_registration.id_for_label }}" class="form-label">
                  <i class="fas fa-calendar-check me-1"></i> Registration Date
                </label>
                {{ form.date_of_registration|add_class:"form-control" }}
                <div class="invalid-feedback"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Job Information Section -->
        <div id="job" class="card mb-4 border-0 shadow-sm section-card">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>Job Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Job Role Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.job_role.id_for_label }}" class="form-label">
                  <i class="fas fa-user-tie me-1"></i> Job Role <span class="text-danger">*</span>
                </label>
                {{ form.job_role|add_class:"form-control"|attr:"required" }}
                <div class="invalid-feedback"></div>
                {% if form.job_role.errors %}
                  <div class="text-danger small mt-1">{{ form.job_role.errors }}</div>
                {% endif %}
                <div class="form-text">Enter the staff member's job title or role</div>
              </div>

              <!-- Current Status Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.current_status.id_for_label }}" class="form-label">
                  <i class="fas fa-toggle-on me-1"></i> Current Status
                </label>
                {{ form.current_status|add_class:"form-select" }}
                <div class="invalid-feedback"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information Section -->
        <div id="contact" class="card mb-4 border-0 shadow-sm section-card">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-address-book me-2"></i>Contact Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Mobile Number Field -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.mobile_number.id_for_label }}" class="form-label">
                  <i class="fas fa-phone me-1"></i> Mobile Number <span class="text-danger">*</span>
                </label>
                {{ form.mobile_number|add_class:"form-control"|attr:"required" }}
                <div class="invalid-feedback"></div>
                {% if form.mobile_number.errors %}
                  <div class="text-danger small mt-1">{{ form.mobile_number.errors }}</div>
                {% endif %}
                <div class="form-text">Enter a 10-15 digit mobile number without spaces</div>
              </div>

              <!-- Address Field -->
              <div class="col-md-12 mb-3">
                <label for="{{ form.address.id_for_label }}" class="form-label">
                  <i class="fas fa-map-marker-alt me-1"></i> Address
                </label>
                {{ form.address|add_class:"form-control" }}
                <div class="invalid-feedback"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Information Section -->
        <div id="additional" class="card mb-4 border-0 shadow-sm section-card">
          <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Additional Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Others Field -->
              <div class="col-md-12 mb-3">
                <label for="{{ form.others.id_for_label }}" class="form-label">
                  <i class="fas fa-sticky-note me-1"></i> Other Information
                </label>
                {{ form.others|add_class:"form-control" }}
                <div class="form-text">Any additional information about the staff member</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between mt-4">
          <div>
            <a href="{% url 'non-teaching-staffs-list' %}" class="btn btn-outline-secondary me-2">
              <i class="fas fa-arrow-left me-1"></i> Cancel
            </a>
            <button type="button" class="btn btn-outline-warning" onclick="resetForm()">
              <i class="fas fa-undo me-1"></i> Reset Form
            </button>
          </div>
          <button type="submit" class="btn btn-primary px-4">
            <i class="fas {% if object %}fa-save{% else %}fa-user-plus{% endif %} me-2"></i>
            {% if object %}
              Update Staff
            {% else %}
              Register Staff
            {% endif %}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock content %}

{% block morejs %}
<script>
  // Function to update progress bar based on filled fields
  function updateProgressBar() {
    const form = document.getElementById('nonTeachingStaffForm');
    const inputs = form.querySelectorAll('input, select, textarea');
    let filledCount = 0;
    let totalFields = 0;

    inputs.forEach(input => {
      if (input.name !== 'csrfmiddlewaretoken') {
        totalFields++;
        if (input.value && input.value.trim() !== '') {
          filledCount++;
        }
      }
    });

    const progressPercent = Math.min(Math.round((filledCount / totalFields) * 100), 100);
    const progressBar = document.getElementById('formProgress');
    const progressPercentage = document.getElementById('progressPercentage');

    progressBar.style.width = progressPercent + '%';
    progressBar.setAttribute('aria-valuenow', progressPercent);
    progressPercentage.textContent = progressPercent + '%';

    // Update progress bar color based on completion percentage
    if (progressPercent < 30) {
      progressBar.className = 'progress-bar bg-danger';
    } else if (progressPercent < 70) {
      progressBar.className = 'progress-bar bg-warning';
    } else {
      progressBar.className = 'progress-bar bg-success';
    }
  }

  // Form validation function
  function validateForm() {
    const form = document.getElementById('nonTeachingStaffForm');
    let isValid = true;

    // Required fields - explicitly define required fields
    const requiredFields = [
      'fullname', 'job_role', 'mobile_number'
    ];

    requiredFields.forEach(fieldName => {
      const field = form.querySelector(`[name="${fieldName}"]`);
      if (!field) return; // Skip if field doesn't exist

      const errorDiv = field.nextElementSibling;

      if (!field.value || field.value.trim() === '') {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        if (errorDiv) {
          errorDiv.textContent = `${field.labels[0].textContent.replace('*', '').trim()} is required`;
          errorDiv.style.display = 'block';
        }
        isValid = false;
      } else {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        if (errorDiv) {
          errorDiv.textContent = '';
          errorDiv.style.display = 'none';
        }
      }
    });

    // Validate mobile number format
    const mobileField = form.querySelector('[name="mobile_number"]');
    if (mobileField && mobileField.value) {
      if (!/^[0-9]{10,15}$/.test(mobileField.value)) {
        mobileField.classList.add('is-invalid');
        mobileField.classList.remove('is-valid');
        const errorDiv = mobileField.nextElementSibling;
        if (errorDiv) {
          errorDiv.textContent = 'Mobile number must be 10-15 digits without spaces or special characters';
          errorDiv.style.display = 'block';
        }
        isValid = false;
      }
    }

    return isValid;
  }

  // Function to handle form navigation
  function setupFormNavigation() {
    const navButtons = document.querySelectorAll('#formNavigation .btn');
    const sections = document.querySelectorAll('.section-card');

    navButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        navButtons.forEach(b => b.classList.remove('active'));
        this.classList.add('active');

        const targetId = this.getAttribute('data-section');
        const targetSection = document.getElementById(targetId);

        if (targetSection) {
          sections.forEach(s => s.classList.remove('active-section'));
          targetSection.classList.add('active-section');
          targetSection.scrollIntoView({ behavior: 'smooth', block: 'start' });

          targetSection.classList.add('highlight-section');
          setTimeout(() => {
            targetSection.classList.remove('highlight-section');
          }, 1000);
        }
      });
    });
  }

  // Initialize form when document is ready
  $(document).ready(function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Setup form navigation
    setupFormNavigation();

    // Set up input change handlers for progress bar
    const form = document.getElementById('nonTeachingStaffForm');
    if (form) {
      const inputs = form.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        input.addEventListener('change', function() {
          updateProgressBar();
          // Add real-time validation feedback
          if (input.value && input.value.trim() !== '') {
            input.classList.add('is-valid');
            input.classList.remove('is-invalid');
          }
        });
        input.addEventListener('keyup', updateProgressBar);
      });

      // Initialize progress bar
      updateProgressBar();
    }

    // Set up form submission handler with loading indicator
    if (form) {
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalBtnText = submitBtn.innerHTML;

      form.addEventListener('submit', function(event) {
        if (!validateForm()) {
          event.preventDefault();
          // Show validation error toast
          showToast('Please correct the errors in the form', 'error');
          // Scroll to first error
          const firstError = document.querySelector('.is-invalid');
          if (firstError) {
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstError.focus();
          }
        } else {
          // Show loading state
          submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Saving...';
          submitBtn.disabled = true;
        }
      });
    }

    // Add animation to form sections
    const formSections = document.querySelectorAll('.section-card');
    formSections.forEach((section, index) => {
      section.style.opacity = '0';
      section.style.transform = 'translateY(20px)';
      section.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

      setTimeout(() => {
        section.style.opacity = '1';
        section.style.transform = 'translateY(0)';
      }, 100 * (index + 1));
    });

    // Add visual indicators for current section while scrolling
    function updateActiveSection() {
      const sections = document.querySelectorAll('.section-card');
      const navButtons = document.querySelectorAll('#formNavigation .btn');

      let currentSectionIndex = 0;

      sections.forEach((section, index) => {
        const rect = section.getBoundingClientRect();
        section.classList.remove('active-section');

        if (rect.top <= 150 && rect.bottom >= 150) {
          currentSectionIndex = index;
          section.classList.add('active-section');
        }
      });

      navButtons.forEach((button, index) => {
        if (index === currentSectionIndex) {
          button.classList.add('active');
        } else {
          button.classList.remove('active');
        }
      });
    }

    // Call on page load
    updateActiveSection();

    // Call on scroll
    window.addEventListener('scroll', updateActiveSection);

    // Call on window resize
    window.addEventListener('resize', updateActiveSection);
  });
  // Function to show toast notifications
  function showToast(message, type = 'info') {
    // Check if toastr is available
    if (typeof toastr !== 'undefined') {
      toastr.options = {
        closeButton: true,
        progressBar: true,
        positionClass: 'toast-top-right',
        timeOut: 5000
      };

      switch(type) {
        case 'success':
          toastr.success(message);
          break;
        case 'error':
          toastr.error(message);
          break;
        case 'warning':
          toastr.warning(message);
          break;
        default:
          toastr.info(message);
      }
    } else {
      // Fallback if toastr is not available
      alert(message);
    }
  }

  // Function to reset the form
  function resetForm() {
    const form = document.getElementById('nonTeachingStaffForm');
    if (form) {
      // Reset the form
      form.reset();

      // Clear validation classes
      const inputs = form.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        input.classList.remove('is-valid', 'is-invalid');
      });

      // Reset error messages
      const errorDivs = form.querySelectorAll('.invalid-feedback');
      errorDivs.forEach(div => {
        div.textContent = '';
        div.style.display = 'none';
      });

      // Update progress bar
      updateProgressBar();

      // Show confirmation toast
      showToast('Form has been reset', 'info');
    }
  }
</script>
{% endblock morejs %}