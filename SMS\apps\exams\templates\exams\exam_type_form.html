{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-graduation-cap"></i> Examination
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:exam_type_list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-list"></i> Exam Types
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-{% if object %}edit{% else %}plus{% endif %}"></i> 
        {% if object %}Edit{% else %}Add{% endif %} Exam Type
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-{% if object %}edit{% else %}plus{% endif %}{% endblock title-icon %}

{% block title %}{% if object %}Edit{% else %}Add{% endif %} Exam Type{% endblock title %}

{% block subtitle %}{% if object %}Update existing{% else %}Create new{% endif %} exam type{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-md-8 mx-auto">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">
            <i class="fas fa-{% if object %}edit{% else %}plus{% endif %} me-2"></i>
            {% if object %}Edit{% else %}Add{% endif %} Exam Type
          </h5>
        </div>
        <div class="card-body">
          <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
              <label for="{{ form.name.id_for_label }}" class="form-label">Name <span class="text-danger">*</span></label>
              {{ form.name|add_class:"form-control"|attr:"placeholder:Enter exam type name" }}
              {% if form.name.errors %}
                <div class="text-danger">
                  {% for error in form.name.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
            
            <div class="mb-3">
              <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
              {{ form.description|add_class:"form-control"|attr:"placeholder:Enter description (optional)" }}
              {% if form.description.errors %}
                <div class="text-danger">
                  {% for error in form.description.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
            
            <div class="d-flex justify-content-between">
              <a href="{% url 'exams:exam_type_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to List
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i> {% if object %}Update{% else %}Save{% endif %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
