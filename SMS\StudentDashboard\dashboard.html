{% extends 'base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
// ...existing code...
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-tachometer-alt{% endblock title-icon %}

{% block title %}Dashboard{% endblock title %}

{% block subtitle %}Welcome to the school management system dashboard{% endblock subtitle %}

{% block page-actions %}
<a href="{% url 'current-session' %}" class="btn btn-sm btn-outline-primary">
  <i class="fas fa-calendar-alt"></i> Current Session: {{ current_session }}
</a>
{% endblock page-actions %}

{% block content %}
<div class="container mt-4">
  <div class="row mb-4">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i> Your Courses</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for course in student_courses %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ course.name }}
                <span class="badge bg-primary">{{ course.teacher_name }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No courses enrolled.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="fas fa-tasks me-2"></i> Assignments</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for assignment in student_assignments %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ assignment.title }}
                <span class="badge bg-secondary">Due: {{ assignment.due_date }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No assignments found.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i> Upcoming Events</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for event in student_events %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ event.title }}
                <span class="badge bg-info">{{ event.date }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No upcoming events.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i> Grades</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for grade in student_grades %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ grade.course_name }}
                <span class="badge bg-success">{{ grade.score }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No grades available.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}