{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}
  {{title}}
{% endblock title %}



{% block title-side %}{% endblock title-side %}

{% block content %}
  <div class="row">
    <div class="col-sm-12">
      <form method="POST">
        {% csrf_token %}

        <div class="mb-3">
          {% for field in form %}
          <div class="form-group">
            {{ field.label_tag}}
            {{ field | add_class:"form-control" |add_error_class:"is-invalid" | attr:"required"}}
            {{field.help_text}}
            {{field.errors}}
          </div>
          {% endfor %}
        </div>


        <input type="submit" value="Save" class="w-25 btn btn-primary">

      </form>
    </div>
  </div>

{% endblock content %}


