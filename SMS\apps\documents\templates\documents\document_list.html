{% extends 'base.html' %}
{% load static %}

{% block title %}All Documents{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item active" aria-current="page">All Documents</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card shadow-sm">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="card-title">All Documents</h2>
            <a href="{% url 'documents:document_upload' %}" class="btn btn-primary">
              <i class="fas fa-upload me-2"></i> Upload Document
            </a>
          </div>
          
          <!-- Search Form -->
          <div class="card mb-4">
            <div class="card-header bg-light">
              <h5 class="mb-0">Search Documents</h5>
            </div>
            <div class="card-body">
              <form method="get" action="{% url 'documents:document_list' %}" class="row g-3">
                <div class="col-md-4">
                  <label for="search_query" class="form-label">Search</label>
                  <input type="text" class="form-control" id="search_query" name="search_query" 
                         value="{{ search_form.search_query.value|default:'' }}" 
                         placeholder="Search by title, number, description...">
                </div>
                <div class="col-md-4">
                  <label for="entity_type" class="form-label">Entity Type</label>
                  <select class="form-select" id="entity_type" name="entity_type">
                    <option value="">All</option>
                    <option value="student" {% if search_form.entity_type.value == 'student' %}selected{% endif %}>Student</option>
                    <option value="staff" {% if search_form.entity_type.value == 'staff' %}selected{% endif %}>Staff</option>
                    <option value="exam" {% if search_form.entity_type.value == 'exam' %}selected{% endif %}>Exam</option>
                    <option value="general" {% if search_form.entity_type.value == 'general' %}selected{% endif %}>General</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <label for="document_type" class="form-label">Document Type</label>
                  <select class="form-select" id="document_type" name="document_type">
                    <option value="">All Document Types</option>
                    {% for doc_type in search_form.document_type.field.queryset %}
                    <option value="{{ doc_type.id }}" {% if search_form.document_type.value|stringformat:'s' == doc_type.id|stringformat:'s' %}selected{% endif %}>
                      {{ doc_type.name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-md-4">
                  <label for="status" class="form-label">Status</label>
                  <select class="form-select" id="status" name="status">
                    <option value="">All</option>
                    <option value="draft" {% if search_form.status.value == 'draft' %}selected{% endif %}>Draft</option>
                    <option value="pending" {% if search_form.status.value == 'pending' %}selected{% endif %}>Pending Approval</option>
                    <option value="approved" {% if search_form.status.value == 'approved' %}selected{% endif %}>Approved</option>
                    <option value="rejected" {% if search_form.status.value == 'rejected' %}selected{% endif %}>Rejected</option>
                    <option value="expired" {% if search_form.status.value == 'expired' %}selected{% endif %}>Expired</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <label for="date_from" class="form-label">From Date</label>
                  <input type="date" class="form-control" id="date_from" name="date_from" 
                         value="{{ search_form.date_from.value|date:'Y-m-d'|default:'' }}">
                </div>
                <div class="col-md-4">
                  <label for="date_to" class="form-label">To Date</label>
                  <input type="date" class="form-control" id="date_to" name="date_to" 
                         value="{{ search_form.date_to.value|date:'Y-m-d'|default:'' }}">
                </div>
                <div class="col-12 text-end">
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i> Search
                  </button>
                  <a href="{% url 'documents:document_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-redo me-2"></i> Reset
                  </a>
                </div>
              </form>
            </div>
          </div>
          
          <!-- Documents Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>S/N</th>
                  <th>Title</th>
                  <th>Document Type</th>
                  <th>Entity</th>
                  <th>Status</th>
                  <th>Date Added</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for document in documents %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ document.title }}</td>
                  <td>{{ document.document_type.name }}</td>
                  <td>
                    {% if document.content_type %}
                      {% if document.content_type.model == 'student' %}
                        <span class="badge bg-primary">Student</span>
                      {% elif document.content_type.model == 'staff' %}
                        <span class="badge bg-success">Staff</span>
                      {% elif document.content_type.model == 'exam' %}
                        <span class="badge bg-warning">Exam</span>
                      {% endif %}
                    {% else %}
                      <span class="badge bg-secondary">General</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if document.status == 'approved' %}
                    <span class="badge bg-success">Approved</span>
                    {% elif document.status == 'pending' %}
                    <span class="badge bg-warning">Pending</span>
                    {% elif document.status == 'rejected' %}
                    <span class="badge bg-danger">Rejected</span>
                    {% elif document.status == 'expired' %}
                    <span class="badge bg-secondary">Expired</span>
                    {% else %}
                    <span class="badge bg-info">Draft</span>
                    {% endif %}
                  </td>
                  <td>{{ document.created_at|date:"d M, Y" }}</td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="{% url 'documents:document_detail' document.id %}" class="btn btn-sm btn-outline-primary" title="View">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="{% url 'documents:document_download' document.id %}" class="btn btn-sm btn-outline-success" title="Download">
                        <i class="fas fa-download"></i>
                      </a>
                      <a href="{% url 'documents:document_update' document.id %}" class="btn btn-sm btn-outline-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>
                      <a href="{% url 'documents:document_delete' document.id %}" class="btn btn-sm btn-outline-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="7" class="text-center">No documents found</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          {% if is_paginated %}
          <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
              {% if page_obj.has_previous %}
              <li class="page-item">
                <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                  <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
              </li>
              <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                  <span aria-hidden="true">&laquo;</span>
                </a>
              </li>
              {% endif %}
              
              {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                </li>
                {% endif %}
              {% endfor %}
              
              {% if page_obj.has_next %}
              <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                  <span aria-hidden="true">&raquo;</span>
                </a>
              </li>
              <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                  <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
              </li>
              {% endif %}
            </ul>
          </nav>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block extrascripts %}
<script>
  $(document).ready(function() {
    // Update document types based on entity type selection
    $('#entity_type').change(function() {
      var entityType = $(this).val();
      
      // Make AJAX request to get document types for the selected entity
      $.ajax({
        url: "{% url 'documents:api_document_types' %}",
        data: {
          'entity_type': entityType
        },
        dataType: 'json',
        success: function(data) {
          var options = '<option value="">All Document Types</option>';
          for (var i = 0; i < data.length; i++) {
            options += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
          }
          $('#document_type').html(options);
        }
      });
    });
  });
</script>
{% endblock extrascripts %}
