{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title>{{ profile.college_name | default:"MySchool" }} - Logout</title>

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="{% static 'plugins/fontawesome-free/css/all.min.css' %}">
  <link rel="stylesheet" href="{% static 'plugins/toastr/toastr.min.css' %}">
  <!-- Custom Styles -->
  <link rel="stylesheet" href="{% static 'dist/css/login-page.css' %}">

  <style>
    body {
      background: url("{% static 'dist/img/bg.jpg' %}") no-repeat center center fixed;
      background-size: cover;
      font-family: 'Poppins', sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      animation: fadeIn 1s ease-in;
      color: #333;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .container {
      width: 1200px;
      display: flex;
      background: rgba(255, 255, 255, 0.9);
      box-shadow: 0px 15px 40px rgba(0, 0, 0, 0.3);
      border-radius: 20px;
      overflow: hidden;
    }
    .left-section {
      flex: 1;
      background: linear-gradient(135deg, #1E3C72, #2a5298);
      color: white;
      padding: 50px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
    .right-section {
      flex: 1;
      padding: 50px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
    .logo {
      margin-bottom: 30px;
    }
    .logo img {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      object-fit: cover;
      border: 5px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
      color: inherit;
    }
    .left-section h1 {
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    h3 {
      font-size: 1.5rem;
      margin-bottom: 20px;
      font-weight: 400;
      color: inherit;
    }
    .left-section h3 {
      color: rgba(255, 255, 255, 0.9);
    }
    p {
      font-size: 1.1rem;
      margin-bottom: 30px;
      line-height: 1.6;
    }
    .left-section p {
      color: rgba(255, 255, 255, 0.8);
    }
    .logout-message {
      font-size: 1.2rem;
      margin: 20px 0;
      padding: 20px;
      background-color: rgba(30, 60, 114, 0.1);
      border-radius: 10px;
      border-left: 4px solid #1E3C72;
    }
    .buttons {
      display: flex;
      gap: 20px;
      margin-top: 20px;
    }
    .btn {
      padding: 12px 30px;
      border-radius: 50px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
    .btn i {
      margin-right: 8px;
    }
    .btn-primary {
      background: linear-gradient(135deg, #1E3C72, #2a5298);
      color: white;
      border: none;
      box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
    }
    .btn-primary:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(30, 60, 114, 0.4);
    }
    .btn-outline {
      background: transparent;
      color: #1E3C72;
      border: 2px solid #1E3C72;
    }
    .btn-outline:hover {
      background: rgba(30, 60, 114, 0.1);
      transform: translateY(-3px);
    }
    .logout-icon {
      font-size: 5rem;
      color: #1E3C72;
      margin-bottom: 20px;
      animation: pulse 2s infinite;
    }
    @keyframes pulse {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.1);
        opacity: 0.8;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }
    .countdown {
      font-size: 1rem;
      color: #666;
      margin-top: 20px;
    }
    .timer {
      font-weight: bold;
      color: #1E3C72;
    }
    @media (max-width: 1200px) {
      .container {
        width: 90%;
      }
    }
    @media (max-width: 768px) {
      .container {
        flex-direction: column;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
      }
      .left-section, .right-section {
        padding: 30px;
      }
      .logo img {
        width: 80px;
        height: 80px;
      }
      h1 {
        font-size: 1.8rem;
      }
      h3 {
        font-size: 1.2rem;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="left-section">
      <div class="logo">
        {% if profile.college_logo.url %}
          <img src="{{ profile.college_logo.url }}" alt="Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        {% else %}
          <img src="{% static 'dist/img/default-logo.png' %}" alt="Default Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        {% endif %}
      </div>
      <h1>{{ profile.college_name | default:"MySchool" }}</h1>
      <h3>Thank You for Using Our System</h3>
      <p>We hope to see you again soon. Have a great day!</p>
    </div>
    <div class="right-section">
      <i class="fas fa-sign-out-alt logout-icon"></i>
      <h1>Logged Out Successfully</h1>
      <h3>Your session has ended</h3>
      
      <div class="logout-message">
        <p>You have been securely logged out of the system. All your session data has been cleared.</p>
      </div>
      
      <div class="buttons">
        <a href="{% url 'login' %}" class="btn btn-primary"><i class="fas fa-sign-in-alt"></i> Log In Again</a>
        <a href="{% url 'home' %}" class="btn btn-outline"><i class="fas fa-home"></i> Home Page</a>
      </div>
      
      <div class="countdown">
        Redirecting to home page in <span class="timer" id="countdown">5</span> seconds...
      </div>
    </div>
  </div>
  
  <!-- Scripts -->
  <script src="{% static 'plugins/jquery/jquery.min.js' %}"></script>
  <script>
    // Countdown timer
    let seconds = 5;
    const countdownElement = document.getElementById('countdown');
    
    const interval = setInterval(function() {
      seconds--;
      countdownElement.textContent = seconds;
      
      if (seconds <= 0) {
        clearInterval(interval);
        window.location.href = "{% url 'home' %}";
      }
    }, 1000);
  </script>
</body>
</html>
