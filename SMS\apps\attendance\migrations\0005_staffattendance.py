# Generated by Django 4.1.2 on 2025-03-20 09:49

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("staffs", "0017_rename_fullnaame_staff_fullname"),
        ("attendance", "0004_studentattendance"),
    ]

    operations = [
        migrations.CreateModel(
            name="StaffAttendance",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Present", "Present"),
                            ("Absent", "Absent"),
                            ("Leave", "Leave"),
                        ],
                        default="Absent",
                        max_length=10,
                    ),
                ),
                ("date", models.DateField(default=django.utils.timezone.now)),
                (
                    "Staff",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendance",
                        to="staffs.staff",
                    ),
                ),
            ],
        ),
    ]
