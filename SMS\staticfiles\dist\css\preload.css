/* Preload CSS to prevent styling flash */


/* Hide Control Sidebar immediately */
.control-sidebar, .control-sidebar-dark, .control-sidebar-bg {
  display: none !important;
  width: 0 !important;
  visibility: hidden !important;
  opacity: 0 !important;
  right: -300px !important;
  position: absolute !important;
}

/* Remove control sidebar button immediately */
[data-widget="control-sidebar"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Fix for AdminLTE layout immediately */
body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .content-wrapper,
body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-footer,
body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-header {
  margin-right: 0 !important;
}

/* Initial sidebar styles */
.main-sidebar {
  background: linear-gradient(90deg, black, #1E3C72) !important;
  color: white !important;
}

/* Loader Styles */
.loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure loader animations work immediately */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes shine {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes dots {
  0% { content: '.'; }
  33% { content: '..'; }
  66% { content: '...'; }
  100% { content: '.'; }
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 100%; }
  100% { width: 0%; }
}

@keyframes pulseGlow {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.1); opacity: 1; }
}
