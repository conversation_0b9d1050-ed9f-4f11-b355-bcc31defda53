# Generated by Django 5.2 on 2025-04-24 05:16

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("corecode", "0016_section"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AutomatedBackupSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("enabled", models.BooleanField(default=False)),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("biweekly", "Bi-weekly"),
                            ("monthly", "Monthly"),
                        ],
                        default="weekly",
                        max_length=10,
                    ),
                ),
                ("backup_time", models.TimeField(default=django.utils.timezone.now)),
                ("day_of_week", models.IntegerField(default=0)),
                (
                    "backup_type",
                    models.CharField(
                        choices=[
                            ("full", "Full Backup"),
                            ("database", "Database Only"),
                            ("incremental", "Incremental Backup"),
                        ],
                        default="full",
                        max_length=20,
                    ),
                ),
                (
                    "retention_policy",
                    models.CharField(
                        choices=[
                            ("5", "Keep last 5 backups"),
                            ("10", "Keep last 10 backups"),
                            ("20", "Keep last 20 backups"),
                            ("all", "Keep all backups"),
                        ],
                        default="10",
                        max_length=10,
                    ),
                ),
                ("notify_on_backup", models.BooleanField(default=True)),
                ("last_backup", models.DateTimeField(blank=True, null=True)),
                ("next_backup", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Automated Backup Settings",
                "verbose_name_plural": "Automated Backup Settings",
            },
        ),
        migrations.CreateModel(
            name="Backup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("file_path", models.CharField(max_length=500)),
                (
                    "backup_type",
                    models.CharField(
                        choices=[
                            ("full", "Full Backup"),
                            ("database", "Database Only"),
                            ("media", "Media Files Only"),
                            ("settings", "Settings Only"),
                            ("custom", "Custom Backup"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "format",
                    models.CharField(
                        choices=[
                            ("zip", "ZIP Archive"),
                            ("sql", "SQL Dump"),
                            ("json", "JSON Format"),
                        ],
                        max_length=10,
                    ),
                ),
                ("size", models.CharField(max_length=20)),
                ("size_bytes", models.BigIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("is_encrypted", models.BooleanField(default=False)),
                ("description", models.TextField(blank=True, null=True)),
                ("includes_students", models.BooleanField(default=True)),
                ("includes_staff", models.BooleanField(default=True)),
                ("includes_classes", models.BooleanField(default=True)),
                ("includes_results", models.BooleanField(default=True)),
                ("includes_attendance", models.BooleanField(default=True)),
                ("includes_fees", models.BooleanField(default=True)),
                ("includes_settings", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Backup",
                "verbose_name_plural": "Backups",
                "ordering": ["-created_at"],
            },
        ),
    ]
