{"version": 3, "sources": ["toastr.js"], "names": ["define", "$", "error", "message", "title", "optionsOverride", "notify", "type", "toastType", "iconClass", "getOptions", "iconClasses", "getContainer", "options", "create", "$container", "containerId", "length", "createContainer", "info", "subscribe", "callback", "listener", "success", "warning", "clear", "$toastElement", "clearOptions", "clearToast", "clearContainer", "remove", "removeToast", "children", "toastsToClear", "i", "force", "<PERSON><PERSON><PERSON><PERSON>", "duration", "hideDuration", "easing", "hideEasing", "complete", "attr", "addClass", "positionClass", "appendTo", "target", "getDefaults", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toastClass", "debug", "showMethod", "showDuration", "showEasing", "onShown", "undefined", "onHidden", "closeMethod", "closeDuration", "closeEasing", "closeOnHover", "extendedTimeOut", "timeOut", "titleClass", "messageClass", "escapeHtml", "closeHtml", "closeClass", "newestOnTop", "preventDuplicates", "progressBar", "progressClass", "rtl", "publish", "args", "map", "source", "replace", "personalizeToast", "setIcon", "setTitle", "setMessage", "setCloseButton", "setProgressBar", "setRTL", "setSequence", "setAria", "ariaValue", "handleEvents", "hover", "stickAround", "delayedHideToast", "onclick", "click", "hideToast", "closeButton", "$closeElement", "event", "stopPropagation", "cancelBubble", "onCloseClick", "displayToast", "hide", "intervalId", "setTimeout", "maxHideTime", "parseFloat", "<PERSON><PERSON><PERSON>", "Date", "getTime", "setInterval", "updateProgress", "prepend", "append", "suffix", "$titleElement", "$messageElement", "$progressElement", "shouldExit", "previousToast", "override", "method", "clearTimeout", "response", "state", "endTime", "stop", "percentage", "width", "extend", "toastId", "startTime", "console", "log", "toastr", "is", "version", "amd", "deps", "factory", "module", "exports", "require", "window", "j<PERSON><PERSON><PERSON>"], "mappings": "CAaC,SAAUA,GACPA,GAAQ,UAAW,SAAUC,GACzB,MAAO,YA8BH,QAASC,GAAMC,EAASC,EAAOC,GAC3B,MAAOC,IACHC,KAAMC,EAAUN,MAChBO,UAAWC,IAAaC,YAAYT,MACpCC,QAASA,EACTE,gBAAiBA,EACjBD,MAAOA,IAIf,QAASQ,GAAaC,EAASC,GAG3B,MAFKD,KAAWA,EAAUH,KAC1BK,EAAad,EAAE,IAAMY,EAAQG,aACzBD,EAAWE,OACJF,GAEPD,IACAC,EAAaG,EAAgBL,IAE1BE,GAGX,QAASI,GAAKhB,EAASC,EAAOC,GAC1B,MAAOC,IACHC,KAAMC,EAAUW,KAChBV,UAAWC,IAAaC,YAAYQ,KACpChB,QAASA,EACTE,gBAAiBA,EACjBD,MAAOA,IAIf,QAASgB,GAAUC,GACfC,EAAWD,EAGf,QAASE,GAAQpB,EAASC,EAAOC,GAC7B,MAAOC,IACHC,KAAMC,EAAUe,QAChBd,UAAWC,IAAaC,YAAYY,QACpCpB,QAASA,EACTE,gBAAiBA,EACjBD,MAAOA,IAIf,QAASoB,GAAQrB,EAASC,EAAOC,GAC7B,MAAOC,IACHC,KAAMC,EAAUgB,QAChBf,UAAWC,IAAaC,YAAYa,QACpCrB,QAASA,EACTE,gBAAiBA,EACjBD,MAAOA,IAIf,QAASqB,GAAMC,EAAeC,GAC1B,GAAId,GAAUH,GACTK,IAAcH,EAAaC,GAC3Be,EAAWF,EAAeb,EAASc,IACpCE,EAAehB,GAIvB,QAASiB,GAAOJ,GACZ,GAAIb,GAAUH,GAEd,OADKK,IAAcH,EAAaC,GAC5Ba,GAAuD,IAAtCzB,EAAE,SAAUyB,GAAeT,WAC5Cc,GAAYL,QAGZX,EAAWiB,WAAWf,QACtBF,EAAWe,UAMnB,QAASD,GAAgBhB,GAErB,IAAK,GADDoB,GAAgBlB,EAAWiB,WACtBE,EAAID,EAAchB,OAAS,EAAGiB,GAAK,EAAGA,IAC3CN,EAAW3B,EAAEgC,EAAcC,IAAKrB,GAIxC,QAASe,GAAYF,EAAeb,EAASc,GACzC,GAAIQ,MAAQR,IAAgBA,EAAaQ,QAAQR,EAAaQ,KAC9D,UAAIT,IAAkBS,GAA+C,IAAtClC,EAAE,SAAUyB,GAAeT,UACtDS,EAAcb,EAAQuB,aAClBC,SAAUxB,EAAQyB,aAClBC,OAAQ1B,EAAQ2B,WAChBC,SAAU,WAAcV,EAAYL,OAEjC,GAKf,QAASR,GAAgBL,GAMrB,MALAE,GAAad,EAAE,UACVyC,KAAK,KAAM7B,EAAQG,aACnB2B,SAAS9B,EAAQ+B,eAEtB7B,EAAW8B,SAAS5C,EAAEY,EAAQiC,SACvB/B,EAGX,QAASgC,KACL,OACIC,cAAc,EACdC,WAAY,QACZjC,YAAa,kBACbkC,OAAO,EAEPC,WAAY,SACZC,aAAc,IACdC,WAAY,QACZC,QAASC,OACTnB,WAAY,UACZE,aAAc,IACdE,WAAY,QACZgB,SAAUD,OACVE,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EAEdC,gBAAiB,IACjBlD,aACIT,MAAO,cACPiB,KAAM,aACNI,QAAS,gBACTC,QAAS,iBAEbf,UAAW,aACXmC,cAAe,kBACfkB,QAAS,IACTC,WAAY,cACZC,aAAc,gBACdC,YAAY,EACZnB,OAAQ,OACRoB,UAAW,yCACXC,WAAY,qBACZC,aAAa,EACbC,mBAAmB,EACnBC,aAAa,EACbC,cAAe,iBACfC,KAAK,GAIb,QAASC,GAAQC,GACRpD,GACLA,EAASoD,GAGb,QAASpE,GAAOqE,GAgDZ,QAASV,GAAWW,GAKhB,MAJc,OAAVA,IACAA,EAAS,IAGNA,EACFC,QAAQ,KAAM,SACdA,QAAQ,KAAM,UACdA,QAAQ,KAAM,SACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,QAGvB,QAASC,KACLC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IAGJ,QAASA,KACL,GAAIC,GAAY,EAChB,QAAQZ,EAAIlE,WACR,IAAK,gBACL,IAAK,aACD8E,EAAa,QACb,MACJ,SACIA,EAAY,YAEpB7D,EAAcgB,KAAK,YAAa6C,GAGpC,QAASC,KACD3E,EAAQ+C,cACRlC,EAAc+D,MAAMC,EAAaC,IAGhC9E,EAAQ+E,SAAW/E,EAAQmC,cAC5BtB,EAAcmE,MAAMC,GAGpBjF,EAAQkF,aAAeC,GACvBA,EAAcH,MAAM,SAAUI,GACtBA,EAAMC,gBACND,EAAMC,kBACwB3C,SAAvB0C,EAAME,cAA8BF,EAAME,gBAAiB,IAClEF,EAAME,cAAe,GAGrBtF,EAAQuF,cACRvF,EAAQuF,aAAaH,GAGzBH,GAAU,KAIdjF,EAAQ+E,SACRlE,EAAcmE,MAAM,SAAUI,GAC1BpF,EAAQ+E,QAAQK,GAChBH,MAKZ,QAASO,KACL3E,EAAc4E,OAEd5E,EAAcb,EAAQsC,aACjBd,SAAUxB,EAAQuC,aAAcb,OAAQ1B,EAAQwC,WAAYZ,SAAU5B,EAAQyC,UAG/EzC,EAAQiD,QAAU,IAClByC,EAAaC,WAAWV,EAAWjF,EAAQiD,SAC3CQ,EAAYmC,YAAcC,WAAW7F,EAAQiD,SAC7CQ,EAAYqC,SAAU,GAAIC,OAAOC,UAAYvC,EAAYmC,YACrD5F,EAAQyD,cACRA,EAAYiC,WAAaO,YAAYC,EAAgB,MAKjE,QAAShC,KACDJ,EAAIlE,WACJiB,EAAciB,SAAS9B,EAAQoC,YAAYN,SAASlC,GAI5D,QAAS4E,KACDxE,EAAQuD,YACRrD,EAAWiG,QAAQtF,GAEnBX,EAAWkG,OAAOvF,GAI1B,QAASsD,KACL,GAAIL,EAAIvE,MAAO,CACX,GAAI8G,GAASvC,EAAIvE,KACbS,GAAQoD,aACRiD,EAASjD,EAAWU,EAAIvE,QAE5B+G,EAAcF,OAAOC,GAAQvE,SAAS9B,EAAQkD,YAC9CrC,EAAcuF,OAAOE,IAI7B,QAASlC,KACL,GAAIN,EAAIxE,QAAS,CACb,GAAI+G,GAASvC,EAAIxE,OACbU,GAAQoD,aACRiD,EAASjD,EAAWU,EAAIxE,UAE5BiH,EAAgBH,OAAOC,GAAQvE,SAAS9B,EAAQmD,cAChDtC,EAAcuF,OAAOG,IAI7B,QAASlC,KACDrE,EAAQkF,cACRC,EAAcrD,SAAS9B,EAAQsD,YAAYzB,KAAK,OAAQ,UACxDhB,EAAcsF,QAAQhB,IAI9B,QAASb,KACDtE,EAAQyD,cACR+C,EAAiB1E,SAAS9B,EAAQ0D,eAClC7C,EAAcsF,QAAQK,IAI9B,QAASjC,KACDvE,EAAQ2D,KACR9C,EAAciB,SAAS,OAI/B,QAAS2E,GAAWzG,EAAS8D,GACzB,GAAI9D,EAAQwD,kBAAmB,CAC3B,GAAIM,EAAIxE,UAAYoH,EAChB,OAAO,CAEPA,GAAgB5C,EAAIxE,QAG5B,OAAO,EAGX,QAAS2F,GAAU0B,GACf,GAAIC,GAASD,GAAY3G,EAAQ4C,eAAgB,EAAQ5C,EAAQ4C,YAAc5C,EAAQuB,WACnFC,EAAWmF,GAAY3G,EAAQ6C,iBAAkB,EACjD7C,EAAQ6C,cAAgB7C,EAAQyB,aAChCC,EAASiF,GAAY3G,EAAQ8C,eAAgB,EAAQ9C,EAAQ8C,YAAc9C,EAAQ2B,UACvF,KAAIvC,EAAE,SAAUyB,GAAeT,QAAWuG,EAI1C,MADAE,cAAapD,EAAYiC,YAClB7E,EAAc+F,IACjBpF,SAAUA,EACVE,OAAQA,EACRE,SAAU,WACNV,EAAYL,GACZgG,aAAanB,GACT1F,EAAQ2C,UAA+B,WAAnBmE,EAASC,OAC7B/G,EAAQ2C,WAEZmE,EAASC,MAAQ,SACjBD,EAASE,QAAU,GAAIjB,MACvBnC,EAAQkD,MAKpB,QAAShC,MACD9E,EAAQiD,QAAU,GAAKjD,EAAQgD,gBAAkB,KACjD0C,EAAaC,WAAWV,EAAWjF,EAAQgD,iBAC3CS,EAAYmC,YAAcC,WAAW7F,EAAQgD,iBAC7CS,EAAYqC,SAAU,GAAIC,OAAOC,UAAYvC,EAAYmC,aAIjE,QAASf,KACLgC,aAAanB,GACbjC,EAAYqC,QAAU,EACtBjF,EAAcoG,MAAK,GAAM,GAAMjH,EAAQsC,aAClCd,SAAUxB,EAAQuC,aAAcb,OAAQ1B,EAAQwC,aAIzD,QAAS0D,KACL,GAAIgB,IAAezD,EAAYqC,SAAW,GAAIC,OAAOC,WAAcvC,EAAYmC,YAAe,GAC9FY,GAAiBW,MAAMD,EAAa,KApPxC,GAAIlH,GAAUH,IACVD,EAAYkE,EAAIlE,WAAaI,EAAQJ,SAOzC,IALqC,mBAAzBkE,GAAmB,kBAC3B9D,EAAUZ,EAAEgI,OAAOpH,EAAS8D,EAAItE,iBAChCI,EAAYkE,EAAItE,gBAAgBI,WAAaA,IAG7C6G,EAAWzG,EAAS8D,GAAxB,CAEAuD,IAEAnH,EAAaH,EAAaC,GAAS,EAEnC,IAAI0F,GAAa,KACb7E,EAAgBzB,EAAE,UAClBkH,EAAgBlH,EAAE,UAClBmH,EAAkBnH,EAAE,UACpBoH,EAAmBpH,EAAE,UACrB+F,EAAgB/F,EAAEY,EAAQqD,WAC1BI,GACAiC,WAAY,KACZI,QAAS,KACTF,YAAa,MAEbkB,GACAO,QAASA,EACTN,MAAO,UACPO,UAAW,GAAIvB,MACf/F,QAASA,EACT8D,IAAKA,EAeT,OAZAG,KAEAuB,IAEAb,IAEAf,EAAQkD,GAEJ9G,EAAQqC,OAASkF,SACjBA,QAAQC,IAAIV,GAGTjG,GA2MX,QAAShB,KACL,MAAOT,GAAEgI,UAAWlF,IAAeuF,EAAOzH,SAG9C,QAASkB,GAAYL,GACZX,IAAcA,EAAaH,KAC5Bc,EAAc6G,GAAG,cAGrB7G,EAAcI,SACdJ,EAAgB,KACqB,IAAjCX,EAAWiB,WAAWf,SACtBF,EAAWe,SACXyF,EAAgBhE,SA/bxB,GAAIxC,GACAO,EAsBAiG,EArBAW,EAAU,EACV1H,GACAN,MAAO,QACPiB,KAAM,OACNI,QAAS,UACTC,QAAS,WAGT8G,GACA7G,MAAOA,EACPK,OAAQA,EACR5B,MAAOA,EACPU,aAAcA,EACdO,KAAMA,EACNN,WACAO,UAAWA,EACXG,QAASA,EACTiH,QAAS,QACThH,QAASA,EAKb,OAAO8G,SA4aC,kBAAXtI,SAAyBA,OAAOyI,IAAMzI,OAAS,SAAU0I,EAAMC,GAC9C,mBAAXC,SAA0BA,OAAOC,QACxCD,OAAOC,QAAUF,EAAQG,QAAQ,WAEjCC,OAAOT,OAASK,EAAQI,OAAOC", "file": "toastr.js", "sourcesContent": ["/*\n * Toastr\n * Copyright 2012-2015\n * Authors: <AUTHORS>\n * All Rights Reserved.\n * Use, reproduction, distribution, and modification of this code is subject to the terms and\n * conditions of the MIT license, available at http://www.opensource.org/licenses/mit-license.php\n *\n * ARIA Support: <PERSON><PERSON>\n *\n * Project: https://github.com/CodeSeven/toastr\n */\n/* global define */\n(function (define) {\n    define(['jquery'], function ($) {\n        return (function () {\n            var $container;\n            var listener;\n            var toastId = 0;\n            var toastType = {\n                error: 'error',\n                info: 'info',\n                success: 'success',\n                warning: 'warning'\n            };\n\n            var toastr = {\n                clear: clear,\n                remove: remove,\n                error: error,\n                getContainer: getContainer,\n                info: info,\n                options: {},\n                subscribe: subscribe,\n                success: success,\n                version: '2.1.4',\n                warning: warning\n            };\n\n            var previousToast;\n\n            return toastr;\n\n            ////////////////\n\n            function error(message, title, optionsOverride) {\n                return notify({\n                    type: toastType.error,\n                    iconClass: getOptions().iconClasses.error,\n                    message: message,\n                    optionsOverride: optionsOverride,\n                    title: title\n                });\n            }\n\n            function getContainer(options, create) {\n                if (!options) { options = getOptions(); }\n                $container = $('#' + options.containerId);\n                if ($container.length) {\n                    return $container;\n                }\n                if (create) {\n                    $container = createContainer(options);\n                }\n                return $container;\n            }\n\n            function info(message, title, optionsOverride) {\n                return notify({\n                    type: toastType.info,\n                    iconClass: getOptions().iconClasses.info,\n                    message: message,\n                    optionsOverride: optionsOverride,\n                    title: title\n                });\n            }\n\n            function subscribe(callback) {\n                listener = callback;\n            }\n\n            function success(message, title, optionsOverride) {\n                return notify({\n                    type: toastType.success,\n                    iconClass: getOptions().iconClasses.success,\n                    message: message,\n                    optionsOverride: optionsOverride,\n                    title: title\n                });\n            }\n\n            function warning(message, title, optionsOverride) {\n                return notify({\n                    type: toastType.warning,\n                    iconClass: getOptions().iconClasses.warning,\n                    message: message,\n                    optionsOverride: optionsOverride,\n                    title: title\n                });\n            }\n\n            function clear($toastElement, clearOptions) {\n                var options = getOptions();\n                if (!$container) { getContainer(options); }\n                if (!clearToast($toastElement, options, clearOptions)) {\n                    clearContainer(options);\n                }\n            }\n\n            function remove($toastElement) {\n                var options = getOptions();\n                if (!$container) { getContainer(options); }\n                if ($toastElement && $(':focus', $toastElement).length === 0) {\n                    removeToast($toastElement);\n                    return;\n                }\n                if ($container.children().length) {\n                    $container.remove();\n                }\n            }\n\n            // internal functions\n\n            function clearContainer (options) {\n                var toastsToClear = $container.children();\n                for (var i = toastsToClear.length - 1; i >= 0; i--) {\n                    clearToast($(toastsToClear[i]), options);\n                }\n            }\n\n            function clearToast ($toastElement, options, clearOptions) {\n                var force = clearOptions && clearOptions.force ? clearOptions.force : false;\n                if ($toastElement && (force || $(':focus', $toastElement).length === 0)) {\n                    $toastElement[options.hideMethod]({\n                        duration: options.hideDuration,\n                        easing: options.hideEasing,\n                        complete: function () { removeToast($toastElement); }\n                    });\n                    return true;\n                }\n                return false;\n            }\n\n            function createContainer(options) {\n                $container = $('<div/>')\n                    .attr('id', options.containerId)\n                    .addClass(options.positionClass);\n\n                $container.appendTo($(options.target));\n                return $container;\n            }\n\n            function getDefaults() {\n                return {\n                    tapToDismiss: true,\n                    toastClass: 'toast',\n                    containerId: 'toast-container',\n                    debug: false,\n\n                    showMethod: 'fadeIn', //fadeIn, slideDown, and show are built into jQuery\n                    showDuration: 300,\n                    showEasing: 'swing', //swing and linear are built into jQuery\n                    onShown: undefined,\n                    hideMethod: 'fadeOut',\n                    hideDuration: 1000,\n                    hideEasing: 'swing',\n                    onHidden: undefined,\n                    closeMethod: false,\n                    closeDuration: false,\n                    closeEasing: false,\n                    closeOnHover: true,\n\n                    extendedTimeOut: 1000,\n                    iconClasses: {\n                        error: 'toast-error',\n                        info: 'toast-info',\n                        success: 'toast-success',\n                        warning: 'toast-warning'\n                    },\n                    iconClass: 'toast-info',\n                    positionClass: 'toast-top-right',\n                    timeOut: 5000, // Set timeOut and extendedTimeOut to 0 to make it sticky\n                    titleClass: 'toast-title',\n                    messageClass: 'toast-message',\n                    escapeHtml: false,\n                    target: 'body',\n                    closeHtml: '<button type=\"button\">&times;</button>',\n                    closeClass: 'toast-close-button',\n                    newestOnTop: true,\n                    preventDuplicates: false,\n                    progressBar: false,\n                    progressClass: 'toast-progress',\n                    rtl: false\n                };\n            }\n\n            function publish(args) {\n                if (!listener) { return; }\n                listener(args);\n            }\n\n            function notify(map) {\n                var options = getOptions();\n                var iconClass = map.iconClass || options.iconClass;\n\n                if (typeof (map.optionsOverride) !== 'undefined') {\n                    options = $.extend(options, map.optionsOverride);\n                    iconClass = map.optionsOverride.iconClass || iconClass;\n                }\n\n                if (shouldExit(options, map)) { return; }\n\n                toastId++;\n\n                $container = getContainer(options, true);\n\n                var intervalId = null;\n                var $toastElement = $('<div/>');\n                var $titleElement = $('<div/>');\n                var $messageElement = $('<div/>');\n                var $progressElement = $('<div/>');\n                var $closeElement = $(options.closeHtml);\n                var progressBar = {\n                    intervalId: null,\n                    hideEta: null,\n                    maxHideTime: null\n                };\n                var response = {\n                    toastId: toastId,\n                    state: 'visible',\n                    startTime: new Date(),\n                    options: options,\n                    map: map\n                };\n\n                personalizeToast();\n\n                displayToast();\n\n                handleEvents();\n\n                publish(response);\n\n                if (options.debug && console) {\n                    console.log(response);\n                }\n\n                return $toastElement;\n\n                function escapeHtml(source) {\n                    if (source == null) {\n                        source = '';\n                    }\n\n                    return source\n                        .replace(/&/g, '&amp;')\n                        .replace(/\"/g, '&quot;')\n                        .replace(/'/g, '&#39;')\n                        .replace(/</g, '&lt;')\n                        .replace(/>/g, '&gt;');\n                }\n\n                function personalizeToast() {\n                    setIcon();\n                    setTitle();\n                    setMessage();\n                    setCloseButton();\n                    setProgressBar();\n                    setRTL();\n                    setSequence();\n                    setAria();\n                }\n\n                function setAria() {\n                    var ariaValue = '';\n                    switch (map.iconClass) {\n                        case 'toast-success':\n                        case 'toast-info':\n                            ariaValue =  'polite';\n                            break;\n                        default:\n                            ariaValue = 'assertive';\n                    }\n                    $toastElement.attr('aria-live', ariaValue);\n                }\n\n                function handleEvents() {\n                    if (options.closeOnHover) {\n                        $toastElement.hover(stickAround, delayedHideToast);\n                    }\n\n                    if (!options.onclick && options.tapToDismiss) {\n                        $toastElement.click(hideToast);\n                    }\n\n                    if (options.closeButton && $closeElement) {\n                        $closeElement.click(function (event) {\n                            if (event.stopPropagation) {\n                                event.stopPropagation();\n                            } else if (event.cancelBubble !== undefined && event.cancelBubble !== true) {\n                                event.cancelBubble = true;\n                            }\n\n                            if (options.onCloseClick) {\n                                options.onCloseClick(event);\n                            }\n\n                            hideToast(true);\n                        });\n                    }\n\n                    if (options.onclick) {\n                        $toastElement.click(function (event) {\n                            options.onclick(event);\n                            hideToast();\n                        });\n                    }\n                }\n\n                function displayToast() {\n                    $toastElement.hide();\n\n                    $toastElement[options.showMethod](\n                        {duration: options.showDuration, easing: options.showEasing, complete: options.onShown}\n                    );\n\n                    if (options.timeOut > 0) {\n                        intervalId = setTimeout(hideToast, options.timeOut);\n                        progressBar.maxHideTime = parseFloat(options.timeOut);\n                        progressBar.hideEta = new Date().getTime() + progressBar.maxHideTime;\n                        if (options.progressBar) {\n                            progressBar.intervalId = setInterval(updateProgress, 10);\n                        }\n                    }\n                }\n\n                function setIcon() {\n                    if (map.iconClass) {\n                        $toastElement.addClass(options.toastClass).addClass(iconClass);\n                    }\n                }\n\n                function setSequence() {\n                    if (options.newestOnTop) {\n                        $container.prepend($toastElement);\n                    } else {\n                        $container.append($toastElement);\n                    }\n                }\n\n                function setTitle() {\n                    if (map.title) {\n                        var suffix = map.title;\n                        if (options.escapeHtml) {\n                            suffix = escapeHtml(map.title);\n                        }\n                        $titleElement.append(suffix).addClass(options.titleClass);\n                        $toastElement.append($titleElement);\n                    }\n                }\n\n                function setMessage() {\n                    if (map.message) {\n                        var suffix = map.message;\n                        if (options.escapeHtml) {\n                            suffix = escapeHtml(map.message);\n                        }\n                        $messageElement.append(suffix).addClass(options.messageClass);\n                        $toastElement.append($messageElement);\n                    }\n                }\n\n                function setCloseButton() {\n                    if (options.closeButton) {\n                        $closeElement.addClass(options.closeClass).attr('role', 'button');\n                        $toastElement.prepend($closeElement);\n                    }\n                }\n\n                function setProgressBar() {\n                    if (options.progressBar) {\n                        $progressElement.addClass(options.progressClass);\n                        $toastElement.prepend($progressElement);\n                    }\n                }\n\n                function setRTL() {\n                    if (options.rtl) {\n                        $toastElement.addClass('rtl');\n                    }\n                }\n\n                function shouldExit(options, map) {\n                    if (options.preventDuplicates) {\n                        if (map.message === previousToast) {\n                            return true;\n                        } else {\n                            previousToast = map.message;\n                        }\n                    }\n                    return false;\n                }\n\n                function hideToast(override) {\n                    var method = override && options.closeMethod !== false ? options.closeMethod : options.hideMethod;\n                    var duration = override && options.closeDuration !== false ?\n                        options.closeDuration : options.hideDuration;\n                    var easing = override && options.closeEasing !== false ? options.closeEasing : options.hideEasing;\n                    if ($(':focus', $toastElement).length && !override) {\n                        return;\n                    }\n                    clearTimeout(progressBar.intervalId);\n                    return $toastElement[method]({\n                        duration: duration,\n                        easing: easing,\n                        complete: function () {\n                            removeToast($toastElement);\n                            clearTimeout(intervalId);\n                            if (options.onHidden && response.state !== 'hidden') {\n                                options.onHidden();\n                            }\n                            response.state = 'hidden';\n                            response.endTime = new Date();\n                            publish(response);\n                        }\n                    });\n                }\n\n                function delayedHideToast() {\n                    if (options.timeOut > 0 || options.extendedTimeOut > 0) {\n                        intervalId = setTimeout(hideToast, options.extendedTimeOut);\n                        progressBar.maxHideTime = parseFloat(options.extendedTimeOut);\n                        progressBar.hideEta = new Date().getTime() + progressBar.maxHideTime;\n                    }\n                }\n\n                function stickAround() {\n                    clearTimeout(intervalId);\n                    progressBar.hideEta = 0;\n                    $toastElement.stop(true, true)[options.showMethod](\n                        {duration: options.showDuration, easing: options.showEasing}\n                    );\n                }\n\n                function updateProgress() {\n                    var percentage = ((progressBar.hideEta - (new Date().getTime())) / progressBar.maxHideTime) * 100;\n                    $progressElement.width(percentage + '%');\n                }\n            }\n\n            function getOptions() {\n                return $.extend({}, getDefaults(), toastr.options);\n            }\n\n            function removeToast($toastElement) {\n                if (!$container) { $container = getContainer(); }\n                if ($toastElement.is(':visible')) {\n                    return;\n                }\n                $toastElement.remove();\n                $toastElement = null;\n                if ($container.children().length === 0) {\n                    $container.remove();\n                    previousToast = undefined;\n                }\n            }\n\n        })();\n    });\n}(typeof define === 'function' && define.amd ? define : function (deps, factory) {\n    if (typeof module !== 'undefined' && module.exports) { //Node\n        module.exports = factory(require('jquery'));\n    } else {\n        window.toastr = factory(window.jQuery);\n    }\n}));\n"]}