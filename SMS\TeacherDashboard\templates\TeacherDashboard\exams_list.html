{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-graduation-cap"></i> Examinations
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-graduation-cap{% endblock title-icon %}
{% block title %}Examinations{% endblock title %}
{% block subtitle %}View and manage examinations{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Examinations</h5>
        </div>
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <a href="{% url 'teacher_marks_entry' %}" class="btn btn-primary">
                <i class="fas fa-clipboard-check"></i> Enter Marks
              </a>
            </div>
          </div>
          
          {% if exams %}
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead class="table-dark">
                  <tr>
                    <th>Exam Name</th>
                    <th>Type</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for exam in exams %}
                  <tr>
                    <td>{{ exam.name }}</td>
                    <td>{{ exam.exam_type|default:"N/A" }}</td>
                    <td>{{ exam.start_date|date:"d M Y"|default:"N/A" }}</td>
                    <td>{{ exam.end_date|date:"d M Y"|default:"N/A" }}</td>
                    <td>
                      <span class="badge bg-info">
                        Active
                      </span>
                    </td>
                    <td>
                      <a href="{% url 'teacher_marks_entry' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-clipboard-check"></i> Enter Marks
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Examinations</h5>
              <p class="text-muted">No examinations are currently available.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
