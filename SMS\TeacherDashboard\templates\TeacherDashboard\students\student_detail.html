{% extends 'TeacherDashboard/base.html' %}
{% load static %}

{% block title %}{{ object.fullname }} - Student Details{% endblock %}

{% block extra_css %}
<style>
    .student-photo {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border: 3px solid #dee2e6;
    }
    .info-card {
        border-left: 4px solid #007bff;
    }
    .document-item {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }
    .document-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .fee-card {
        border-left: 4px solid #28a745;
    }
    .pending-fee {
        border-left: 4px solid #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user text-primary"></i>
                        Student Details
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'teacher_dashboard' %}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'teacher_students_list' %}">Students</a></li>
                            <li class="breadcrumb-item active">{{ object.fullname }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'teacher_student_update' object.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit Student
                    </a>
                    <a href="{% url 'teacher_upload_student_documents' object.pk %}" class="btn btn-info">
                        <i class="fas fa-upload"></i> Upload Documents
                    </a>
                    <a href="{% url 'teacher_student_delete' object.pk %}" class="btn btn-danger"
                       onclick="return confirm('Are you sure you want to delete this student?')">
                        <i class="fas fa-trash"></i> Delete
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Student Basic Information -->
        <div class="col-lg-4">
            <div class="card info-card">
                <div class="card-body text-center">
                    {% if object.passport %}
                        <img src="{{ object.passport.url }}" alt="{{ object.fullname }}" 
                             class="student-photo rounded-circle mb-3">
                    {% else %}
                        <div class="student-photo rounded-circle mx-auto mb-3 bg-light d-flex align-items-center justify-content-center">
                            <i class="fas fa-user fa-4x text-muted"></i>
                        </div>
                    {% endif %}
                    
                    <h4 class="mb-1">{{ object.fullname }}</h4>
                    <p class="text-muted mb-2">{{ object.registration_number|default:"No Registration Number" }}</p>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <span class="badge bg-{% if object.current_status == 'active' %}success{% else %}danger{% endif %} p-2">
                                {{ object.get_current_status_display }}
                            </span>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-{% if object.gender == 'male' %}primary{% else %}pink{% endif %} p-2">
                                {{ object.get_gender_display }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'teacher_student_update_udise' object.pk %}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-file-alt"></i> UDISE+ Form
                        </a>
                        {% if not udise_info %}
                        <a href="{% url 'teacher_create_udise_info' object.pk %}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-plus"></i> Create UDISE Info
                        </a>
                        {% endif %}
                        <button class="btn btn-outline-success btn-sm" onclick="window.print()">
                            <i class="fas fa-print"></i> Print Details
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Information -->
        <div class="col-lg-8">
            <!-- Personal Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-user-circle"></i> Personal Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Full Name:</strong> {{ object.fullname }}</p>
                            <p><strong>Date of Birth:</strong> {{ object.date_of_birth|date:"d M, Y" }}</p>
                            <p><strong>Gender:</strong> {{ object.get_gender_display }}</p>
                            <p><strong>Category:</strong> {{ object.get_category_display }}</p>
                            <p><strong>Aadhaar Number:</strong> {{ object.aadhar|default:"-" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Mobile Number:</strong> {{ object.mobile_number|default:"-" }}</p>
                            <p><strong>Email:</strong> {{ object.email_id|default:"-" }}</p>
                            <p><strong>Address:</strong> {{ object.address|default:"-" }}</p>
                            <p><strong>Blood Group:</strong> {{ object.blood_group|default:"-" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Academic Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-graduation-cap"></i> Academic Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Current Class:</strong> {{ object.current_class|default:"-" }}</p>
                            <p><strong>Section:</strong> {{ object.section|default:"-" }}</p>
                            <p><strong>Date of Admission:</strong> {{ object.date_of_admission|date:"d M, Y" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Registration Number:</strong> {{ object.registration_number|default:"-" }}</p>
                            <p><strong>Status:</strong> 
                                <span class="badge bg-{% if object.current_status == 'active' %}success{% else %}danger{% endif %}">
                                    {{ object.get_current_status_display }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Parent Information -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-users"></i> Parent Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Father's Name:</strong> {{ object.Father_name|default:"-" }}</p>
                            <p><strong>Father's Mobile:</strong> {{ object.Father_mobile_number|default:"-" }}</p>
                            <p><strong>Father's Aadhaar:</strong> {{ object.Father_aadhar|default:"-" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Mother's Name:</strong> {{ object.Mother_name|default:"-" }}</p>
                            <p><strong>Alternate Mobile:</strong> {{ object.alternate_mobile|default:"-" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents Section -->
            {% if documents %}
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-file-alt"></i> Documents</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if documents.aadhar_card %}
                        <div class="col-md-6 mb-2">
                            <div class="document-item">
                                <i class="fas fa-id-card text-primary"></i>
                                <strong>Aadhaar Card</strong>
                                <br><small>{{ documents.aadhar_card_number|default:"No number" }}</small>
                                <a href="{{ documents.aadhar_card.url }}" target="_blank" class="btn btn-sm btn-outline-primary float-end">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if documents.transfer_certificate %}
                        <div class="col-md-6 mb-2">
                            <div class="document-item">
                                <i class="fas fa-certificate text-success"></i>
                                <strong>Transfer Certificate</strong>
                                <br><small>{{ documents.transfer_certificate_number|default:"No number" }}</small>
                                <a href="{{ documents.transfer_certificate.url }}" target="_blank" class="btn btn-sm btn-outline-primary float-end">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Fee Information -->
            {% if pending_fees %}
            <div class="card pending-fee mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-money-bill-wave"></i> Pending Fees</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Fee Type</th>
                                    <th>Amount</th>
                                    <th>Due Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for fee in pending_fees %}
                                <tr>
                                    <td>{{ fee.fee_type }}</td>
                                    <td>₹{{ fee.get_discounted_amount }}</td>
                                    <td>{{ fee.due_date|date:"d M, Y" }}</td>
                                    <td>
                                        <span class="badge bg-danger">Pending</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end">
                        <strong>Total Pending: ₹{{ total_pending_amount }}</strong>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any specific JavaScript for student details
</script>
{% endblock %}
