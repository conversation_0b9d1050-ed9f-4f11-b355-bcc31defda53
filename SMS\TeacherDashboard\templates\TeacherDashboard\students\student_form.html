{% extends 'TeacherDashboard/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block extrastyle %}
<style>
  /* Professional form styling */
  .card-header.bg-gradient-primary {
    background: linear-gradient(135deg, #1a237e, #283593, #3949ab);
  }

  .form-label {
    font-weight: 500;
    color: #495057;
  }

  .form-control:focus, .form-select:focus {
    border-color: #3949ab;
    box-shadow: 0 0 0 0.2rem rgba(57, 73, 171, 0.25);
  }

  .card {
    transition: all 0.3s ease;
  }

  .card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
  }

  .card-header {
    border-bottom: none;
    padding: 0.75rem 1.25rem;
  }

  .card-header h6 {
    font-weight: 600;
  }

  .text-danger {
    font-size: 0.8rem;
  }

  .form-text {
    font-size: 0.75rem;
    color: #6c757d;
  }

  .btn-primary {
    background-color: #3949ab;
    border-color: #3949ab;
  }

  .btn-primary:hover {
    background-color: #283593;
    border-color: #283593;
  }

  .section-card {
    border: 1px solid #e3f2fd;
    border-radius: 8px;
  }

  .section-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e3f2fd;
  }

  .form-navigation-container {
    border: 1px solid #e3f2fd;
  }

  .form-navigation-wrapper {
    background-color: #f8f9fa;
  }

  .btn-outline-primary {
    border-color: #3949ab;
    color: #3949ab;
  }

  .btn-outline-primary:hover {
    background-color: #3949ab;
    border-color: #3949ab;
  }

  .btn-outline-primary.active {
    background-color: #3949ab;
    border-color: #3949ab;
    color: white;
  }

  .progress-bar {
    background-color: #4caf50;
  }

  .badge.bg-primary {
    background-color: #3949ab !important;
  }

  .invalid-feedback {
    display: block;
  }

  .form-control.is-invalid, .form-select.is-invalid {
    border-color: #dc3545;
  }

  .form-control.is-valid, .form-select.is-valid {
    border-color: #28a745;
  }

  .passport-preview {
    max-width: 150px;
    max-height: 150px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #dee2e6;
  }

  .upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
  }

  .upload-area:hover {
    border-color: #3949ab;
    background-color: #f8f9fa;
  }

  .upload-area.dragover {
    border-color: #3949ab;
    background-color: #e3f2fd;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .form-navigation-wrapper .btn {
      font-size: 0.8rem;
      padding: 0.5rem;
    }

    .card-body {
      padding: 1rem;
    }
  }

  /* Section visibility */
  .section-card {
    display: none;
  }

  .section-card.active {
    display: block;
  }

  /* Form validation styling */
  .was-validated .form-control:valid {
    border-color: #28a745;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38 3.72-3.72.94.94-4.66 4.66z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  }

  .was-validated .form-control:invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4 1.4-1.4M8.6 7.4 7.2 6 5.8 7.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  }
</style>
{% endblock extrastyle %}

{% block title %}
  {% if object %}
    Update {{ object }}
  {% else %}
    Add New Student
  {% endif %}
{% endblock title %}

{% block content %}
<div class="container-fluid px-0">
  <div class="card shadow-sm border-0 mb-4">
    <div class="card-header bg-gradient-primary text-white">
      <h5 class="mb-0">
        <i class="fas {% if object %}fa-edit{% else %}fa-user-plus{% endif %} me-2"></i>
        {% if object %}
          Update Student Information
        {% else %}
          New Student Registration
        {% endif %}
      </h5>
    </div>
    <div class="card-body">
      <form action="" method="POST" enctype="multipart/form-data" id="studentForm" class="needs-validation" novalidate>
        {% csrf_token %}

        <!-- Form Sections -->
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div>
                <span class="text-muted small">Fill in the form fields below. Fields marked with <span class="text-danger">*</span> are required.</span>
              </div>
              <div>
                <span class="text-muted small me-2">Form completion:</span>
                <span class="badge bg-primary" id="progressPercentage">0%</span>
              </div>
            </div>
            <div class="progress" style="height: 5px;">
              <div class="progress-bar bg-success" role="progressbar" style="width: 0%;"
                   aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" id="formProgress"></div>
            </div>
          </div>
        </div>

    <form method="post" enctype="multipart/form-data" id="studentForm">
        {% csrf_token %}
        
        <!-- Personal Information Section -->
        <div class="form-section">
            <h5 class="mb-3">
                <i class="fas fa-user-circle text-primary"></i>
                Personal Information
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.fullname.id_for_label }}" class="form-label">
                            Full Name <span class="required-field">*</span>
                        </label>
                        {{ form.fullname }}
                        {% if form.fullname.errors %}
                            <div class="text-danger small">{{ form.fullname.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.registration_number.id_for_label }}" class="form-label">
                            Registration Number
                        </label>
                        {{ form.registration_number }}
                        {% if form.registration_number.errors %}
                            <div class="text-danger small">{{ form.registration_number.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.gender.id_for_label }}" class="form-label">
                            Gender <span class="required-field">*</span>
                        </label>
                        {{ form.gender }}
                        {% if form.gender.errors %}
                            <div class="text-danger small">{{ form.gender.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                            Date of Birth <span class="required-field">*</span>
                        </label>
                        {{ form.date_of_birth }}
                        {% if form.date_of_birth.errors %}
                            <div class="text-danger small">{{ form.date_of_birth.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.category.id_for_label }}" class="form-label">
                            Category
                        </label>
                        {{ form.category }}
                        {% if form.category.errors %}
                            <div class="text-danger small">{{ form.category.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.aadhar.id_for_label }}" class="form-label">
                            Aadhaar Number
                        </label>
                        {{ form.aadhar }}
                        {% if form.aadhar.errors %}
                            <div class="text-danger small">{{ form.aadhar.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">12-digit Aadhaar number</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.blood_group.id_for_label }}" class="form-label">
                            Blood Group
                        </label>
                        {{ form.blood_group }}
                        {% if form.blood_group.errors %}
                            <div class="text-danger small">{{ form.blood_group.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.mobile_number.id_for_label }}" class="form-label">
                            Mobile Number
                        </label>
                        {{ form.mobile_number }}
                        {% if form.mobile_number.errors %}
                            <div class="text-danger small">{{ form.mobile_number.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.email_id.id_for_label }}" class="form-label">
                            Email Address
                        </label>
                        {{ form.email_id }}
                        {% if form.email_id.errors %}
                            <div class="text-danger small">{{ form.email_id.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">
                            Address
                        </label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger small">{{ form.address.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.passport.id_for_label }}" class="form-label">
                            Student Photo
                        </label>
                        {{ form.passport }}
                        {% if form.passport.errors %}
                            <div class="text-danger small">{{ form.passport.errors.0 }}</div>
                        {% endif %}
                        {% if object.passport %}
                            <div class="mt-2">
                                <img src="{{ object.passport.url }}" alt="Current photo" class="preview-image">
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Academic Information Section -->
        <div class="form-section">
            <h5 class="mb-3">
                <i class="fas fa-graduation-cap text-primary"></i>
                Academic Information
            </h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.current_class.id_for_label }}" class="form-label">
                            Current Class <span class="required-field">*</span>
                        </label>
                        {{ form.current_class }}
                        {% if form.current_class.errors %}
                            <div class="text-danger small">{{ form.current_class.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.section.id_for_label }}" class="form-label">
                            Section
                        </label>
                        {{ form.section }}
                        {% if form.section.errors %}
                            <div class="text-danger small">{{ form.section.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.date_of_admission.id_for_label }}" class="form-label">
                            Date of Admission <span class="required-field">*</span>
                        </label>
                        {{ form.date_of_admission }}
                        {% if form.date_of_admission.errors %}
                            <div class="text-danger small">{{ form.date_of_admission.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.current_status.id_for_label }}" class="form-label">
                            Status <span class="required-field">*</span>
                        </label>
                        {{ form.current_status }}
                        {% if form.current_status.errors %}
                            <div class="text-danger small">{{ form.current_status.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Parent Information Section -->
        <div class="form-section">
            <h5 class="mb-3">
                <i class="fas fa-users text-primary"></i>
                Parent Information
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.Father_name.id_for_label }}" class="form-label">
                            Father's Name <span class="required-field">*</span>
                        </label>
                        {{ form.Father_name }}
                        {% if form.Father_name.errors %}
                            <div class="text-danger small">{{ form.Father_name.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.Mother_name.id_for_label }}" class="form-label">
                            Mother's Name
                        </label>
                        {{ form.Mother_name }}
                        {% if form.Mother_name.errors %}
                            <div class="text-danger small">{{ form.Mother_name.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.Father_mobile_number.id_for_label }}" class="form-label">
                            Father's Mobile <span class="required-field">*</span>
                        </label>
                        {{ form.Father_mobile_number }}
                        {% if form.Father_mobile_number.errors %}
                            <div class="text-danger small">{{ form.Father_mobile_number.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.Father_aadhar.id_for_label }}" class="form-label">
                            Father's Aadhaar
                        </label>
                        {{ form.Father_aadhar }}
                        {% if form.Father_aadhar.errors %}
                            <div class="text-danger small">{{ form.Father_aadhar.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.alternate_mobile.id_for_label }}" class="form-label">
                            Alternate Mobile
                        </label>
                        {{ form.alternate_mobile }}
                        {% if form.alternate_mobile.errors %}
                            <div class="text-danger small">{{ form.alternate_mobile.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information Section -->
        {% if form.others %}
        <div class="form-section">
            <h5 class="mb-3">
                <i class="fas fa-info-circle text-primary"></i>
                Additional Information
            </h5>
            <div class="mb-3">
                <label for="{{ form.others.id_for_label }}" class="form-label">
                    Other Details
                </label>
                {{ form.others }}
                {% if form.others.errors %}
                    <div class="text-danger small">{{ form.others.errors.0 }}</div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Form Actions -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                    <a href="{% url 'teacher_students_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 
                        {% if object %}Update Student{% else %}Add Student{% endif %}
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Load sections when class changes
function loadSections(classId) {
    if (classId) {
        fetch(`/teacher-dashboard/students/api/class/${classId}/sections/`)
            .then(response => response.json())
            .then(data => {
                const sectionSelect = document.getElementById('id_section');
                sectionSelect.innerHTML = '<option value="">---------</option>';
                
                if (data.sections) {
                    data.sections.forEach(section => {
                        const option = document.createElement('option');
                        option.value = section.value;
                        option.textContent = section.text;
                        sectionSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading sections:', error);
            });
    }
}

// Form validation
document.getElementById('studentForm').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields.');
    }
});

// Date picker setup
document.addEventListener('DOMContentLoaded', function() {
    // Set max date for date of birth (not in future)
    const dobField = document.getElementById('id_date_of_birth');
    if (dobField) {
        const today = new Date().toISOString().split('T')[0];
        dobField.setAttribute('max', today);
    }
    
    // Set default date for admission date
    const admissionField = document.getElementById('id_date_of_admission');
    if (admissionField && !admissionField.value) {
        const today = new Date().toISOString().split('T')[0];
        admissionField.value = today;
    }
});
</script>
{% endblock %}
