{% extends 'base.html' %}

{% block breadcrumb-left %}
<div class="breadcrumb-container no-print">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'fees:fee_list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-money-bill-wave"></i> Fee Management
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'fees:student_fee_history' payment.student.id %}" class="text-decoration-none fw-bold">
          <i class="fas fa-history"></i> Payment History
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-file-invoice"></i> Receipt
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-file-invoice{% endblock title-icon %}

{% block title %}Fee Receipt{% endblock title %}

{% block subtitle %}Payment receipt for {{ payment.student.fullname }}{% endblock subtitle %}

{% block page-actions %}
<div class="no-print">
  <button onclick="window.print()" class="btn btn-outline-primary me-2">
    <i class="fas fa-print me-1"></i> Print
  </button>
  <a href="{% url 'fees:generate_receipt' payment.id %}" class="btn btn-primary">
    <i class="fas fa-download me-1"></i> Download PDF
  </a>
</div>
{% endblock page-actions %}

{% block content %}
<div class="container mt-4 receipt-container position-relative">
    <!-- Watermark -->
    <div class="position-absolute top-50 start-50 translate-middle" style="transform: translate(-50%, -50%) rotate(-45deg); font-size: 100px; color: rgba(200, 200, 200, 0.15); z-index: 0; white-space: nowrap;">
        PAID
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-body position-relative" style="z-index: 1;">
                    <!-- Header with logo and school info -->
                    <div class="row mb-4 align-items-center">
                        <div class="col-md-8 d-flex align-items-center">
                            {% if profile.college_logo %}
                            <img src="{{ profile.college_logo.url }}" alt="School Logo" class="img-fluid me-3" style="max-height: 80px;">
                            {% endif %}
                            <div>
                                <h2 class="mb-0 text-primary">{{ profile.college_name|default:"School Management System" }}</h2>
                                <p class="mb-1">{{ profile.college_address|default:"123 School Street, City" }}</p>
                                <p class="mb-0">{{ profile.college_phone|default:"Phone" }} | {{ profile.college_email|default:"Email" }}</p>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <p class="mb-1"><strong>Date:</strong> {{ payment.date|date:"d M Y" }}</p>
                            <p class="mb-1"><strong>Receipt #:</strong> {{ payment.id }}</p>
                            <p class="mb-1"><strong>Session:</strong> {{ current_session }}</p>
                            <p class="mb-1"><strong>Term:</strong> {{ current_term }}</p>
                        </div>
                    </div>

                    <div class="border-bottom border-primary mb-4"></div>

                    <!-- Receipt Title -->
                    <div class="text-center mb-4">
                        <h1 class="text-primary text-uppercase fw-bold">Fee Receipt</h1>
                    </div>

                    <!-- Student and Payment Information -->
                    <div class="row mb-4 p-3 bg-light rounded">
                        <div class="col-md-6">
                            <h5 class="text-primary border-bottom pb-2 mb-3">Student Information</h5>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Name:</div>
                                <div class="col-7">{{ payment.student.fullname }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Registration No:</div>
                                <div class="col-7">{{ payment.student.registration_number }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Class:</div>
                                <div class="col-7">{{ payment.student.current_class }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Section:</div>
                                <div class="col-7">{{ payment.student.section }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Gender:</div>
                                <div class="col-7">{{ payment.student.gender|title }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Father's Name:</div>
                                <div class="col-7">{{ payment.student.Father_name }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Contact:</div>
                                <div class="col-7">{{ payment.student.Father_mobile_number }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary border-bottom pb-2 mb-3">Payment Details</h5>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Payment Date:</div>
                                <div class="col-7">{{ payment.date|date:"d M Y" }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Payment Method:</div>
                                <div class="col-7">{{ payment.payment_method }}</div>
                            </div>
                            {% if payment.transaction_id %}
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Transaction ID:</div>
                                <div class="col-7">{{ payment.transaction_id }}</div>
                            </div>
                            {% endif %}
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Status:</div>
                                <div class="col-7">
                                    <span class="badge {% if payment.status == 'Paid' %}bg-success{% else %}bg-warning{% endif %}">
                                        {{ payment.status }}
                                    </span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5 fw-bold">Fee Category:</div>
                                <div class="col-7">{{ payment.fee_category }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Table -->
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead class="table-primary">
                                <tr>
                                    <th style="width: 70%;">Fee Category</th>
                                    <th class="text-end">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ payment.fee_category }}</td>
                                    <td class="text-end fw-bold">₹{{ payment.amount|floatformat:2 }}</td>
                                </tr>
                                <tr class="table-light fw-bold">
                                    <td class="text-end">Total</td>
                                    <td class="text-end">₹{{ payment.amount|floatformat:2 }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pending Fees Note (if any) -->
                    {% if payment.student.get_due_fee > 0 %}
                    <div class="alert alert-warning small">
                        <strong>Note:</strong> Student has pending fees of ₹{{ payment.student.get_due_fee|floatformat:2 }}. Please clear all dues before the due date to avoid late fees.
                    </div>
                    {% endif %}

                    <!-- Footer with signature -->
                    <div class="row mt-5">
                        <div class="col-md-6">
                            <p class="fw-bold mb-1">Thank you for your payment!</p>
                            <p class="text-muted small">This is a computer-generated receipt and does not require a signature.</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="d-inline-block text-center">
                                <div style="border-top: 1px solid #333; width: 200px; margin-bottom: 8px;"></div>
                                <p class="mb-0"><strong>Authorized Signature</strong></p>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <p class="text-muted small fst-italic">Generated on: {% now "d M Y H:i" %} | Valid Receipt</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .receipt-container, .receipt-container * {
            visibility: visible;
        }
        .receipt-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            font-size: 10pt;
        }
        .no-print {
            display: none;
        }
        /* Ensure the receipt fits on one page */
        .card-body {
            padding: 0.8cm !important;
        }
        .mb-4 {
            margin-bottom: 0.5rem !important;
        }
        .mt-4 {
            margin-top: 0.5rem !important;
        }
        .mt-5 {
            margin-top: 1rem !important;
        }
        .p-3 {
            padding: 0.5rem !important;
        }
        table th, table td {
            padding: 0.3rem !important;
        }
        h1 {
            font-size: 1.5rem !important;
            margin-bottom: 0.5rem !important;
        }
        h5 {
            font-size: 0.9rem !important;
            margin-bottom: 0.3rem !important;
        }
        .row {
            margin-bottom: 0.3rem !important;
        }
    }
</style>
{% endblock %}
