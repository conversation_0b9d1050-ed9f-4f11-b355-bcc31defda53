/*!
 * OverlayScrollbars
 * https://github.com/KingSora/OverlayScrollbars
 *
 * Version: 1.11.0
 *
 * Copyright KingSora | <PERSON>.
 * https://github.com/KingSora
 *
 * Released under the MIT license.
 * Date: 29.02.2020
 */
!function(n,t){"function"==typeof define&&define.amd?define(function(){return t(n,n.document,undefined)}):"object"==typeof module&&"object"==typeof module.exports?module.exports=t(n,n.document,undefined):t(n,n.document,undefined)}("undefined"!=typeof window?window:this,function(gt,mt,bi){"use strict";var o,a,c,u,wt="object",yt="function",xt="array",_t="string",Ot="boolean",St="number",f="undefined",n="null",gi={c:"class",s:"style",i:"id",l:"length",p:"prototype",ti:"tabindex",oH:"offsetHeight",cH:"clientHeight",sH:"scrollHeight",oW:"offsetWidth",cW:"clientWidth",sW:"scrollWidth",hOP:"hasOwnProperty",bCR:"getBoundingClientRect"},zt=(o={},a={},{e:c=["-webkit-","-moz-","-o-","-ms-"],u:u=["WebKit","Moz","O","MS"],v:function(n){var t=a[n];if(a[gi.hOP](n))return t;for(var r,e,i,o=s(n),u=mt.createElement("div")[gi.s],f=0;f<c.length;f++)for(i=c[f].replace(/-/g,""),r=[n,c[f]+n,i+o,s(i)+o],e=0;e<r[gi.l];e++)if(u[r[e]]!==bi){t=r[e];break}return a[n]=t},d:function(n,t,r){var e=0,i=o[n];if(!o[gi.hOP](n)){for(i=gt[n];e<u[gi.l];e++)i=i||gt[(t?u[e]:u[e].toLowerCase())+s(n)];o[n]=i}return i||r}});function s(n){return n.charAt(0).toUpperCase()+n.slice(1)}var mi={wW:r(t,0,!0),wH:r(t,0),mO:r(zt.d,0,"MutationObserver",!0),rO:r(zt.d,0,"ResizeObserver",!0),rAF:r(zt.d,0,"requestAnimationFrame",!1,function(n){return gt.setTimeout(n,1e3/60)}),cAF:r(zt.d,0,"cancelAnimationFrame",!1,function(n){return gt.clearTimeout(n)}),now:function(){return Date.now&&Date.now()||(new Date).getTime()},stpP:function(n){n.stopPropagation?n.stopPropagation():n.cancelBubble=!0},prvD:function(n){n.preventDefault&&n.cancelable?n.preventDefault():n.returnValue=!1},page:function(n){var t="page",r="client",e="X",i=((n=n.originalEvent||n).target||n.srcElement||mt).ownerDocument||mt,o=i.documentElement,u=i.body;if(n.touches===bi)return!n[t+e]&&n[r+e]&&null!=n[r+e]?{x:n[r+e]+(o&&o.scrollLeft||u&&u.scrollLeft||0)-(o&&o.clientLeft||u&&u.clientLeft||0),y:n[r+"Y"]+(o&&o.scrollTop||u&&u.scrollTop||0)-(o&&o.clientTop||u&&u.clientTop||0)}:{x:n[t+e],y:n.pageY};var f=n.touches[0];return{x:f[t+e],y:f.pageY}},mBtn:function(n){var t=n.button;return n.which||t===bi?n.which:1&t?1:2&t?3:4&t?2:0},inA:function(n,t){for(var r=0;r<t[gi.l];r++)try{if(t[r]===n)return r}catch(e){}return-1},isA:function(n){var t=Array.isArray;return t?t(n):this.type(n)==xt},type:function(n){return n===bi||null===n?n+"":Object[gi.p].toString.call(n).replace(/^\[object (.+)\]$/,"$1").toLowerCase()},bind:r};function t(n){return n?gt.innerWidth||mt.documentElement[gi.cW]||mt.body[gi.cW]:gt.innerHeight||mt.documentElement[gi.cH]||mt.body[gi.cH]}function r(n,t){if(typeof n!=yt)throw"Can't bind function!";var r=gi.p,e=Array[r].slice.call(arguments,2),i=function(){},o=function(){return n.apply(this instanceof i?this:t,e.concat(Array[r].slice.call(arguments)))};return n[r]&&(i[r]=n[r]),o[r]=new i,o}var l,v,h,C,I,T,d,p,wi=Math,yi=gt.jQuery,A=(l={p:wi.PI,c:wi.cos,s:wi.sin,w:wi.pow,t:wi.sqrt,n:wi.asin,a:wi.abs,o:1.70158},{swing:function(n,t,r,e,i){return.5-l.c(n*l.p)/2},linear:function(n,t,r,e,i){return n},easeInQuad:function(n,t,r,e,i){return e*(t/=i)*t+r},easeOutQuad:function(n,t,r,e,i){return-e*(t/=i)*(t-2)+r},easeInOutQuad:function(n,t,r,e,i){return(t/=i/2)<1?e/2*t*t+r:-e/2*(--t*(t-2)-1)+r},easeInCubic:function(n,t,r,e,i){return e*(t/=i)*t*t+r},easeOutCubic:function(n,t,r,e,i){return e*((t=t/i-1)*t*t+1)+r},easeInOutCubic:function(n,t,r,e,i){return(t/=i/2)<1?e/2*t*t*t+r:e/2*((t-=2)*t*t+2)+r},easeInQuart:function(n,t,r,e,i){return e*(t/=i)*t*t*t+r},easeOutQuart:function(n,t,r,e,i){return-e*((t=t/i-1)*t*t*t-1)+r},easeInOutQuart:function(n,t,r,e,i){return(t/=i/2)<1?e/2*t*t*t*t+r:-e/2*((t-=2)*t*t*t-2)+r},easeInQuint:function(n,t,r,e,i){return e*(t/=i)*t*t*t*t+r},easeOutQuint:function(n,t,r,e,i){return e*((t=t/i-1)*t*t*t*t+1)+r},easeInOutQuint:function(n,t,r,e,i){return(t/=i/2)<1?e/2*t*t*t*t*t+r:e/2*((t-=2)*t*t*t*t+2)+r},easeInSine:function(n,t,r,e,i){return-e*l.c(t/i*(l.p/2))+e+r},easeOutSine:function(n,t,r,e,i){return e*l.s(t/i*(l.p/2))+r},easeInOutSine:function(n,t,r,e,i){return-e/2*(l.c(l.p*t/i)-1)+r},easeInExpo:function(n,t,r,e,i){return 0==t?r:e*l.w(2,10*(t/i-1))+r},easeOutExpo:function(n,t,r,e,i){return t==i?r+e:e*(1-l.w(2,-10*t/i))+r},easeInOutExpo:function(n,t,r,e,i){return 0==t?r:t==i?r+e:(t/=i/2)<1?e/2*l.w(2,10*(t-1))+r:e/2*(2-l.w(2,-10*--t))+r},easeInCirc:function(n,t,r,e,i){return-e*(l.t(1-(t/=i)*t)-1)+r},easeOutCirc:function(n,t,r,e,i){return e*l.t(1-(t=t/i-1)*t)+r},easeInOutCirc:function(n,t,r,e,i){return(t/=i/2)<1?-e/2*(l.t(1-t*t)-1)+r:e/2*(l.t(1-(t-=2)*t)+1)+r},easeInElastic:function(n,t,r,e,i){var o=l.o,u=0,f=e;return 0==t?r:1==(t/=i)?r+e:(u=u||.3*i,o=f<l.a(e)?(f=e,u/4):u/(2*l.p)*l.n(e/f),-(f*l.w(2,10*--t)*l.s((t*i-o)*(2*l.p)/u))+r)},easeOutElastic:function(n,t,r,e,i){var o=l.o,u=0,f=e;return 0==t?r:1==(t/=i)?r+e:(u=u||.3*i,o=f<l.a(e)?(f=e,u/4):u/(2*l.p)*l.n(e/f),f*l.w(2,-10*t)*l.s((t*i-o)*(2*l.p)/u)+e+r)},easeInOutElastic:function(n,t,r,e,i){var o=l.o,u=0,f=e;return 0==t?r:2==(t/=i/2)?r+e:(u=u||i*(.3*1.5),o=f<l.a(e)?(f=e,u/4):u/(2*l.p)*l.n(e/f),t<1?f*l.w(2,10*--t)*l.s((t*i-o)*(2*l.p)/u)*-.5+r:f*l.w(2,-10*--t)*l.s((t*i-o)*(2*l.p)/u)*.5+e+r)},easeInBack:function(n,t,r,e,i,o){return e*(t/=i)*t*(((o=o||l.o)+1)*t-o)+r},easeOutBack:function(n,t,r,e,i,o){return e*((t=t/i-1)*t*(((o=o||l.o)+1)*t+o)+1)+r},easeInOutBack:function(n,t,r,e,i,o){return o=o||l.o,(t/=i/2)<1?e/2*(t*t*((1+(o*=1.525))*t-o))+r:e/2*((t-=2)*t*((1+(o*=1.525))*t+o)+2)+r},easeInBounce:function(n,t,r,e,i){return e-this.easeOutBounce(n,i-t,0,e,i)+r},easeOutBounce:function(n,t,r,e,i){var o=7.5625;return(t/=i)<1/2.75?e*(o*t*t)+r:t<2/2.75?e*(o*(t-=1.5/2.75)*t+.75)+r:t<2.5/2.75?e*(o*(t-=2.25/2.75)*t+.9375)+r:e*(o*(t-=2.625/2.75)*t+.984375)+r},easeInOutBounce:function(n,t,r,e,i){return t<i/2?.5*this.easeInBounce(n,2*t,0,e,i)+r:.5*this.easeOutBounce(n,2*t-i,0,e,i)+.5*e+r}}),xi=(v=/[^\x20\t\r\n\f]+/g,h=" ",C="scrollLeft",I="scrollTop",T=[],d=mi.type,p={animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},D[gi.p]={on:function(t,r){var e,i=(t=(t||"").match(v)||[""])[gi.l],o=0;return this.each(function(){e=this;try{if(e.addEventListener)for(;o<i;o++)e.addEventListener(t[o],r);else if(e.detachEvent)for(;o<i;o++)e.attachEvent("on"+t[o],r)}catch(n){}})},off:function(t,r){var e,i=(t=(t||"").match(v)||[""])[gi.l],o=0;return this.each(function(){e=this;try{if(e.removeEventListener)for(;o<i;o++)e.removeEventListener(t[o],r);else if(e.detachEvent)for(;o<i;o++)e.detachEvent("on"+t[o],r)}catch(n){}})},one:function(n,i){return n=(n||"").match(v)||[""],this.each(function(){var e=D(this);D.each(n,function(n,t){var r=function(n){i.call(this,n),e.off(t,r)};e.on(t,r)})})},trigger:function(n){var t,r;return this.each(function(){t=this,mt.createEvent?((r=mt.createEvent("HTMLEvents")).initEvent(n,!0,!1),t.dispatchEvent(r)):t.fireEvent("on"+n)})},append:function(n){return this.each(function(){i(this,"beforeend",n)})},prepend:function(n){return this.each(function(){i(this,"afterbegin",n)})},before:function(n){return this.each(function(){i(this,"beforebegin",n)})},after:function(n){return this.each(function(){i(this,"afterend",n)})},remove:function(){return this.each(function(){var n=this.parentNode;null!=n&&n.removeChild(this)})},unwrap:function(){var n,t,r,e=[];for(this.each(function(){-1===H(r=this.parentNode,e)&&e.push(r)}),n=0;n<e[gi.l];n++){for(t=e[n],r=t.parentNode;t.firstChild;)r.insertBefore(t.firstChild,t);r.removeChild(t)}return this},wrapAll:function(n){for(var t,r=this,e=D(n)[0],i=e,o=r[0].parentNode,u=r[0].previousSibling;0<i.childNodes[gi.l];)i=i.childNodes[0];for(t=0;r[gi.l]-t;i.firstChild===r[0]&&t++)i.appendChild(r[t]);var f=u?u.nextSibling:o.firstChild;return o.insertBefore(e,f),this},wrapInner:function(r){return this.each(function(){var n=D(this),t=n.contents();t[gi.l]?t.wrapAll(r):n.append(r)})},wrap:function(n){return this.each(function(){D(this).wrapAll(n)})},css:function(n,t){var r,e,i,o=gt.getComputedStyle;return d(n)==_t?t===bi?(r=this[0],i=o?o(r,null):r.currentStyle[n],o?null!=i?i.getPropertyValue(n):r[gi.s][n]:i):this.each(function(){y(this,n,t)}):this.each(function(){for(e in n)y(this,e,n[e])})},hasClass:function(n){for(var t,r,e=0,i=h+n+h;t=this[e++];){if((r=t.classList)&&r.contains(n))return!0;if(1===t.nodeType&&-1<(h+m(t.className+"")+h).indexOf(i))return!0}return!1},addClass:function(n){var t,r,e,i,o,u,f,a,c=0,s=0;if(n)for(t=n.match(v)||[];r=this[c++];)if(a=r.classList,f===bi&&(f=a!==bi),f)for(;o=t[s++];)a.add(o);else if(i=r.className+"",e=1===r.nodeType&&h+m(i)+h){for(;o=t[s++];)e.indexOf(h+o+h)<0&&(e+=o+h);i!==(u=m(e))&&(r.className=u)}return this},removeClass:function(n){var t,r,e,i,o,u,f,a,c=0,s=0;if(n)for(t=n.match(v)||[];r=this[c++];)if(a=r.classList,f===bi&&(f=a!==bi),f)for(;o=t[s++];)a.remove(o);else if(i=r.className+"",e=1===r.nodeType&&h+m(i)+h){for(;o=t[s++];)for(;-1<e.indexOf(h+o+h);)e=e.replace(h+o+h,h);i!==(u=m(e))&&(r.className=u)}return this},hide:function(){return this.each(function(){this[gi.s].display="none"})},show:function(){return this.each(function(){this[gi.s].display="block"})},attr:function(n,t){for(var r,e=0;r=this[e++];){if(t===bi)return r.getAttribute(n);r.setAttribute(n,t)}return this},removeAttr:function(n){return this.each(function(){this.removeAttribute(n)})},offset:function(){var n=this[0][gi.bCR](),t=gt.pageXOffset||mt.documentElement[C],r=gt.pageYOffset||mt.documentElement[I];return{top:n.top+r,left:n.left+t}},position:function(){var n=this[0];return{top:n.offsetTop,left:n.offsetLeft}},scrollLeft:function(n){for(var t,r=0;t=this[r++];){if(n===bi)return t[C];t[C]=n}return this},scrollTop:function(n){for(var t,r=0;t=this[r++];){if(n===bi)return t[I];t[I]=n}return this},val:function(n){var t=this[0];return n?(t.value=n,this):t.value},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(n){return D(this[0<=n?n:this[gi.l]+n])},find:function(t){var r,e=[];return this.each(function(){var n=this.querySelectorAll(t);for(r=0;r<n[gi.l];r++)e.push(n[r])}),D(e)},children:function(n){var t,r,e,i=[];return this.each(function(){for(r=this.children,e=0;e<r[gi.l];e++)t=r[e],(!n||t.matches&&t.matches(n)||w(t,n))&&i.push(t)}),D(i)},parent:function(n){var t,r=[];return this.each(function(){t=this.parentNode,n&&!D(t).is(n)||r.push(t)}),D(r)},is:function(n){var t,r;for(r=0;r<this[gi.l];r++){if(t=this[r],":visible"===n)return _(t);if(":hidden"===n)return!_(t);if(t.matches&&t.matches(n)||w(t,n))return!0}return!1},contents:function(){var n,t,r=[];return this.each(function(){for(n=this.childNodes,t=0;t<n[gi.l];t++)r.push(n[t])}),D(r)},each:function(n){return e(this,n)},animate:function(n,t,r,e){return this.each(function(){x(this,n,t,r,e)})},stop:function(n,t){return this.each(function(){!function f(n,t,r){for(var e,i,o,u=0;u<T[gi.l];u++)if((e=T[u]).el===n){if(0<e.q[gi.l]){if((i=e.q[0]).stop=!0,mi.cAF()(i.frame),e.q.splice(0,1),r)for(o in i.props)W(n,o,i.props[o]);t?e.q=[]:R(e,!1)}break}}(this,n,t)})}},b(D,{extend:b,inArray:H,isEmptyObject:L,isPlainObject:N,each:e}),D);function b(){var n,t,r,e,i,o,u=arguments[0]||{},f=1,a=arguments[gi.l],c=!1;for(d(u)==Ot&&(c=u,u=arguments[1]||{},f=2),d(u)!=wt&&!d(u)==yt&&(u={}),a===f&&(u=D,--f);f<a;f++)if(null!=(i=arguments[f]))for(e in i)n=u[e],u!==(r=i[e])&&(c&&r&&(N(r)||(t=mi.isA(r)))?(o=t?(t=!1,n&&mi.isA(n)?n:[]):n&&N(n)?n:{},u[e]=b(c,o,r)):r!==bi&&(u[e]=r));return u}function H(n,t,r){for(var e=r||0;e<t[gi.l];e++)if(t[e]===n)return e;return-1}function E(n){return d(n)==yt}function L(n){for(var t in n)return!1;return!0}function N(n){if(!n||d(n)!=wt)return!1;var t,r=gi.p,e=Object[r].hasOwnProperty,i=e.call(n,"constructor"),o=n.constructor&&n.constructor[r]&&e.call(n.constructor[r],"isPrototypeOf");if(n.constructor&&!i&&!o)return!1;for(t in n);return d(t)==f||e.call(n,t)}function e(n,t){var r=0;if(g(n))for(;r<n[gi.l]&&!1!==t.call(n[r],r,n[r]);r++);else for(r in n)if(!1===t.call(n[r],r,n[r]))break;return n}function g(n){var t=!!n&&[gi.l]in n&&n[gi.l],r=d(n);return!E(r)&&(r==xt||0===t||d(t)==St&&0<t&&t-1 in n)}function m(n){return(n.match(v)||[]).join(h)}function w(n,t){for(var r=(n.parentNode||mt).querySelectorAll(t)||[],e=r[gi.l];e--;)if(r[e]==n)return 1}function i(n,t,r){if(d(r)==xt)for(var e=0;e<r[gi.l];e++)i(n,t,r[e]);else d(r)==_t?n.insertAdjacentHTML(t,r):n.insertAdjacentElement(t,r.nodeType?r:r[0])}function y(n,t,r){try{n[gi.s][t]!==bi&&(n[gi.s][t]=function e(n,t){p[n.toLowerCase()]||d(t)!=St||(t+="px");return t}(t,r))}catch(i){}}function R(n,t){var r,e;!1!==t&&n.q.splice(0,1),0<n.q[gi.l]?(e=n.q[0],x(n.el,e.props,e.duration,e.easing,e.complete,!0)):-1<(r=H(n,T))&&T.splice(r,1)}function W(n,t,r){t===C||t===I?n[t]=r:y(n,t,r)}function x(n,t,r,e,i,o){var u,f,a,c,s,l,v=N(r),h={},d={},p=0;for(l=v?(e=r.easing,r.start,a=r.progress,c=r.step,s=r.specialEasing,i=r.complete,r.duration):r,s=s||{},l=l||400,e=e||"swing",o=o||!1;p<T[gi.l];p++)if(T[p].el===n){f=T[p];break}for(u in f||(f={el:n,q:[]},T.push(f)),t)h[u]=u===C||u===I?n[u]:D(n).css(u);for(u in h)h[u]!==t[u]&&t[u]!==bi&&(d[u]=t[u]);if(L(d))o&&R(f);else{var b,g,m,w,y,x,_,O,S,z=o?0:H(k,f.q),k={props:d,duration:v?r:l,easing:e,complete:i};if(-1===z&&(z=f.q[gi.l],f.q.push(k)),0===z)if(0<l)_=mi.now(),O=function(){for(u in b=mi.now(),S=b-_,g=k.stop||l<=S,m=1-(wi.max(0,_+l-b)/l||0),d)w=parseFloat(h[u]),y=parseFloat(d[u]),x=(y-w)*A[s[u]||e](m,m*l,0,1,l)+w,W(n,u,x),E(c)&&c(x,{elem:n,prop:u,start:w,now:x,end:y,pos:m,options:{easing:e,speacialEasing:s,duration:l,complete:i,step:c},startTime:_});E(a)&&a({},m,wi.max(0,l-S)),g?(R(f),E(i)&&i()):k.frame=mi.rAF()(O)},k.frame=mi.rAF()(O);else{for(u in d)W(n,u,d[u]);R(f)}}}function _(n){return!!(n[gi.oW]||n[gi.oH]||n.getClientRects()[gi.l])}function D(n){if(0===arguments[gi.l])return this;var t,r,e=new D,i=n,o=0;if(d(n)==_t)for(i=[],t="<"===n.charAt(0)?((r=mt.createElement("div")).innerHTML=n,r.children):mt.querySelectorAll(n);o<t[gi.l];o++)i.push(t[o]);if(i){for(d(i)==_t||g(i)&&i!==gt&&i!==i.self||(i=[i]),o=0;o<i[gi.l];o++)e[o]=i[o];e[gi.l]=i[gi.l]}return e}var O,S,_i,z,k,M,F,j,P,B,Q,U,V,q,Oi,Si=(O=[],S="__overlayScrollbars__",function(n,t){var r=arguments[gi.l];if(r<1)return O;if(t)n[S]=t,O.push(n);else{var e=mi.inA(n,O);if(-1<e){if(!(1<r))return O[e][S];delete n[S],O.splice(e,1)}}}),X=(q=[],M=mi.type,U={className:["os-theme-dark",[n,_t]],resize:["none","n:none b:both h:horizontal v:vertical"],sizeAutoCapable:j=[!0,Ot],clipAlways:j,normalizeRTL:j,paddingAbsolute:P=[!(F=[Ot,St,_t,xt,wt,yt,n]),Ot],autoUpdate:[null,[n,Ot]],autoUpdateInterval:[33,St],nativeScrollbarsOverlaid:{showNativeScrollbars:P,initialize:j},overflowBehavior:{x:["scroll",Q="v-h:visible-hidden v-s:visible-scroll s:scroll h:hidden"],y:["scroll",Q]},scrollbars:{visibility:["auto","v:visible h:hidden a:auto"],autoHide:["never","n:never s:scroll l:leave m:move"],autoHideDelay:[800,St],dragScrolling:j,clickScrolling:P,touchSupport:j,snapHandle:P},textarea:{dynWidth:P,dynHeight:P,inheritedAttrs:[["style","class"],[_t,xt,n]]},callbacks:{onInitialized:B=[null,[n,yt]],onInitializationWithdrawn:B,onDestroyed:B,onScrollStart:B,onScroll:B,onScrollStop:B,onOverflowChanged:B,onOverflowAmountChanged:B,onDirectionChanged:B,onContentSizeChanged:B,onHostSizeChanged:B,onUpdated:B}},Oi={g:(V=function(i){var o=function(n){var t,r,e;for(t in n)n[gi.hOP](t)&&(r=n[t],(e=M(r))==xt?n[t]=r[i?1:0]:e==wt&&(n[t]=o(r)));return n};return o(xi.extend(!0,{},U))})(),m:V(!0),_:function(n,t,I,r){var e={},i={},o=xi.extend(!0,{},n),T=xi.inArray,A=xi.isEmptyObject,H=function(n,t,r,e,i,o){for(var u in t)if(t[gi.hOP](u)&&n[gi.hOP](u)){var f,a,c,s,l,v,h,d,p=!1,b=!1,g=t[u],m=M(g),w=m==wt,y=M(g)!=xt?[g]:g,x=r[u],_=n[u],O=M(_),S=o?o+".":"",z='The option "'+S+u+"\" wasn't set, because",k=[],C=[];if(x=x===bi?{}:x,w&&O==wt)e[u]={},i[u]={},H(_,g,x,e[u],i[u],S+u),xi.each([n,e,i],function(n,t){A(t[u])&&delete t[u]});else if(!w){for(v=0;v<y[gi.l];v++)if(l=y[v],c=(m=M(l))==_t&&-1===T(l,F))for(k.push(_t),f=l.split(" "),C=C.concat(f),h=0;h<f[gi.l];h++){for(s=(a=f[h].split(":"))[0],d=0;d<a[gi.l];d++)if(_===a[d]){p=!0;break}if(p)break}else if(k.push(l),O===l){p=!0;break}p?((b=_!==x)&&(e[u]=_),(c?T(x,a)<0:b)&&(i[u]=c?s:_)):I&&console.warn(z+" it doesn't accept the type [ "+O.toUpperCase()+' ] with the value of "'+_+'".\r\nAccepted types are: [ '+k.join(", ").toUpperCase()+" ]."+(0<C[length]?"\r\nValid strings are: [ "+C.join(", ").split(":").join(", ")+" ].":"")),delete n[u]}}};return H(o,t,r||{},e,i),!A(o)&&I&&console.warn("The following options are discarded due to invalidity:\r\n"+gt.JSON.stringify(o,null,2)),{O:e,S:i}}},(_i=gt.OverlayScrollbars=function(n,r,e){if(0===arguments[gi.l])return this;var i,t,o=[],u=xi.isPlainObject(r);return n?(n=n[gi.l]!=bi?n:[n[0]||n],Y(),0<n[gi.l]&&(u?xi.each(n,function(n,t){(i=t)!==bi&&o.push(K(i,r,e,z,k))}):xi.each(n,function(n,t){i=Si(t),("!"===r&&_i.valid(i)||mi.type(r)==yt&&r(t,i)||r===bi)&&o.push(i)}),t=1===o[gi.l]?o[0]:o),t):u||!r?t:o}).globals=function(){Y();var n=xi.extend(!0,{},z);return delete n.msie,n},_i.defaultOptions=function(n){Y();var t=z.defaultOptions;if(n===bi)return xi.extend(!0,{},t);z.defaultOptions=xi.extend(!0,{},t,Oi._(n,Oi.m,!0,t).O)},_i.valid=function(n){return n instanceof _i&&!n.getState().destroyed},_i.extension=function(n,t,r){var e=mi.type(n)==_t,i=arguments[gi.l],o=0;if(i<1||!e)return xi.extend(!0,{length:q[gi.l]},q);if(e)if(mi.type(t)==yt)q.push({name:n,extensionFactory:t,defaultOptions:r});else for(;o<q[gi.l];o++)if(q[o].name===n){if(!(1<i))return xi.extend(!0,{},q[o]);q.splice(o,1)}},_i);function Y(){z=z||new $(Oi.g),k=k||new G(z)}function $(n){var _=this,i="overflow",O=xi("body"),S=xi('<div id="os-dummy-scrollbar-size"><div></div></div>'),o=S[0],e=xi(S.children("div").eq(0));O.append(S),S.hide().show();var t,r,u,f,a,c,s,l,v,h=z(o),d={x:0===h.x,y:0===h.y},p=(r=gt.navigator.userAgent,f="substring",a=r[u="indexOf"]("MSIE "),c=r[u]("Trident/"),s=r[u]("Edge/"),l=r[u]("rv:"),v=parseInt,0<a?t=v(r[f](a+5,r[u](".",a)),10):0<c?t=v(r[f](l+3,r[u](".",l)),10):0<s&&(t=v(r[f](s+5,r[u](".",s)),10)),t);function z(n){return{x:n[gi.oH]-n[gi.cH],y:n[gi.oW]-n[gi.cW]}}xi.extend(_,{defaultOptions:n,msie:p,autoUpdateLoop:!1,autoUpdateRecommended:!mi.mO(),nativeScrollbarSize:h,nativeScrollbarIsOverlaid:d,nativeScrollbarStyling:function(){var n=!1;S.addClass("os-viewport-native-scrollbars-invisible");try{n="none"===S.css("scrollbar-width")&&(9<p||!p)||"none"===gt.getComputedStyle(o,"::-webkit-scrollbar").getPropertyValue("display")}catch(t){}return n}(),overlayScrollbarDummySize:{x:30,y:30},cssCalc:function(){for(var n,t=mt.createElement("div")[gi.s],r=-1;r<zt.e[gi.l];r++)if(n=r<0?"calc":zt.e[r]+"calc",t.cssText="width:"+n+"(1px);",t[gi.l])return n;return null}(),restrictedMeasuring:function(){S.css(i,"hidden");var n=o[gi.sW],t=o[gi.sH];S.css(i,"visible");var r=o[gi.sW],e=o[gi.sH];return n-r!=0||t-e!=0}(),rtlScrollBehavior:function(){S.css({"overflow-y":"hidden","overflow-x":"scroll",direction:"rtl"}).scrollLeft(0);var n=S.offset(),t=e.offset();S.scrollLeft(-999);var r=e.offset();return{i:n.left===t.left,n:t.left!==r.left}}(),supportTransform:zt.v("transform")!==bi,supportTransition:zt.v("transition")!==bi,supportPassiveEvents:function(){var n=!1;try{gt.addEventListener("test",null,Object.defineProperty({},"passive",{get:function(){n=!0}}))}catch(t){}return n}(),supportResizeObserver:!!mi.rO(),supportMutationObserver:!!mi.mO()}),S.removeAttr(gi.s).remove(),function(){if(!d.x||!d.y){var g=wi.abs,m=mi.wW(),w=mi.wH(),y=x();xi(gt).on("resize",function(){if(0<Si().length){var n=mi.wW(),t=mi.wH(),r=n-m,e=t-w;if(0==r&&0==e)return;var i,o=wi.round(n/(m/100)),u=wi.round(t/(w/100)),f=g(r),a=g(e),c=g(o),s=g(u),l=x(),v=2<f&&2<a,h=!function b(n,t){var r=g(n),e=g(t);return r!==e&&r+1!==e&&r-1!==e}(c,s),d=v&&h&&(l!==y&&0<y),p=_.nativeScrollbarSize;d&&(O.append(S),i=_.nativeScrollbarSize=z(S[0]),S.remove(),p.x===i.x&&p.y===i.y||xi.each(Si(),function(){Si(this)&&Si(this).update("zoom")})),m=n,w=t,y=l}})}function x(){var n=gt.screen.deviceXDPI||0,t=gt.screen.logicalXDPI||1;return gt.devicePixelRatio||n/t}}()}function G(r){var c,e=xi.inArray,s=mi.now,l="autoUpdate",v=gi.l,h=[],d=[],p=!1,b=33,g=s(),m=function(){if(0<h[v]&&p){c=mi.rAF()(function(){m()});var n,t,r,e,i,o,u=s(),f=u-g;if(b<f){g=u-f%b,n=33;for(var a=0;a<h[v];a++)(t=h[a])!==bi&&(e=(r=t.options())[l],i=wi.max(1,r.autoUpdateInterval),o=s(),(!0===e||null===e)&&o-d[a]>i&&(t.update("auto"),d[a]=new Date(o+=i)),n=wi.max(1,wi.min(n,i)));b=n}}else b=33};this.add=function(n){-1===e(n,h)&&(h.push(n),d.push(s()),0<h[v]&&!p&&(p=!0,r.autoUpdateLoop=p,m()))},this.remove=function(n){var t=e(n,h);-1<t&&(d.splice(t,1),h.splice(t,1),0===h[v]&&p&&(p=!1,r.autoUpdateLoop=p,c!==bi&&(mi.cAF()(c),c=-1)))}}function K(r,n,t,kt,Ct){var cn=mi.type,sn=xi.inArray,c=xi.each,It=new _i,e=xi[gi.p];if(st(r)){if(Si(r)){var i=Si(r);return i.options(n),i}var Tt,At,Ht,Et,R,Lt,Nt,Rt,W,ln,m,T,l,Wt,Dt,Mt,Ft,w,v,jt,Pt,Bt,Qt,Ut,Vt,qt,Xt,Yt,$t,o,u,Gt,Kt,Jt,f,D,h,M,Zt,nr,tr,rr,er,ir,or,ur,fr,ar,cr,a,s,d,p,b,g,y,A,sr,lr,vr,H,hr,dr,pr,br,gr,mr,wr,yr,xr,_r,Or,Sr,zr,kr,Cr,Ir,E,Tr,Ar,Hr,Er,Lr,Nr,Rr,Wr,x,_,Dr,Mr,Fr,jr,Pr,Br,Qr,Ur,Vr,qr,Xr,Yr,$r,Gr,Kr,O,S,z,k,Jr,Zr,C,I,ne,te,re,ee,ie,F,j,oe,ue,fe,ae,ce={},vn={},hn={},se={},le={},L="-hidden",ve="margin-",he="padding-",de="border-",pe="top",be="right",ge="bottom",me="left",we="min-",ye="max-",xe="width",_e="height",Oe="float",Se="",ze="auto",dn="sync",ke="scroll",Ce="100%",pn="x",bn="y",N=".",Ie=" ",P="scrollbar",B="-horizontal",Q="-vertical",Te=ke+"Left",Ae=ke+"Top",U="mousedown touchstart",V="mouseup touchend touchcancel",q="mousemove touchmove",X="mouseenter",Y="mouseleave",$="keydown",G="keyup",K="selectstart",J="transitionend webkitTransitionEnd oTransitionEnd",Z="__overlayScrollbarsRO__",nn="os-",tn="os-html",rn="os-host",en=rn+"-textarea",on=rn+"-"+P+B+L,un=rn+"-"+P+Q+L,fn=rn+"-transition",He=rn+"-rtl",Ee=rn+"-resize-disabled",Le=rn+"-scrolling",Ne=rn+"-overflow",Re=Ne+"-x",We=Ne+"-y",an="os-textarea",gn=an+"-cover",mn="os-padding",wn="os-viewport",De=wn+"-native-scrollbars-invisible",yn=wn+"-native-scrollbars-overlaid",xn="os-content",Me="os-content-arrange",Fe="os-content-glue",je="os-size-auto-observer",_n="os-resize-observer",On="os-resize-observer-item",Sn=On+"-final",zn="os-text-inherit",kn=nn+P,Cn=kn+"-track",In=Cn+"-off",Tn=kn+"-handle",An=Tn+"-off",Hn=kn+"-unusable",En=kn+"-"+ze+L,Ln=kn+"-corner",Pe=Ln+"-resize",Be=Pe+"-both",Qe=Pe+B,Ue=Pe+Q,Nn=kn+B,Rn=kn+Q,Wn="os-dragging",Ve="os-theme-none",Dn=[De,yn,In,An,Hn,En,Pe,Be,Qe,Ue,Wn].join(Ie),Mn=[],Fn=[gi.ti],jn={},qe={},Xe=42,Pn=[],Bn={},Qn=["wrap","cols","rows"],Un=[gi.i,gi.c,gi.s,"open"].concat(Fn),Vn=[];return It.sleep=function(){$t=!0},It.update=function(n){if(!Dt){var t,r,e,i,o,u=cn(n)==_t;return u?n===ze?(t=function f(){if(!$t&&!Jr){var r,e,i,o=[],n=[{z:nr,k:Un.concat(":visible")},{z:Mt?Zt:bi,k:Qn}];return c(n,function(n,t){(r=t.z)&&c(t.k,function(n,t){e=":"===t.charAt(0)?r.is(t):r.attr(t),i=Bn[t],vi(e,i)&&o.push(t),Bn[t]=e})}),rt(o),0<o[gi.l]}}(),r=function a(){if($t)return!1;var n,t,r,e,i=si(),o=Mt&&xr&&!Vr?Zt.val().length:0,u=!Jr&&xr&&!Mt,f={};return u&&(n=or.css(Oe),f[Oe]=Yt?be:me,f[xe]=ze,or.css(f)),e={w:i[gi.sW]+o,h:i[gi.sH]+o},u&&(f[Oe]=n,f[xe]=Ce,or.css(f)),t=Je(),r=vi(e,x),x=e,r||t}(),(e=t||r)&&ni({C:r,I:Wt?bi:Gt})):n===dn?Jr?(i=z(O.takeRecords()),o=k(S.takeRecords())):i=It.update(ze):"zoom"===n&&ni({T:!0,C:!0}):(n=$t||n,$t=!1,It.update(dn)&&!n||ni({A:n})),Mt||or.find("img").each(function(n,t){-1===mi.inA(t,Pn)&&xi(t).off("load",nt).on("load",nt)}),e||i||o}},It.options=function(n,t){var r,e={};if(xi.isEmptyObject(n)||!xi.isPlainObject(n)){if(cn(n)!=_t)return u;if(!(1<arguments.length))return ht(u,n);!function a(n,t,r){for(var e=t.split(N),i=e.length,o=0,u={},f=u;o<i;o++)u=u[e[o]]=o+1<i?{}:r;xi.extend(n,f,!0)}(e,n,t),r=et(e)}else r=et(n);xi.isEmptyObject(r)||ni({I:r})},It.destroy=function(){if(!Dt){for(var n in Ct.remove(It),Ge(),Ye(rr),Ye(tr),jn)It.removeExt(n);for(;0<Vn[gi.l];)Vn.pop()();Ke(!0),fr&&dt(fr),ur&&dt(ur),Pt&&dt(tr),ut(!0),at(!0),it(!0);for(var t=0;t<Pn[gi.l];t++)xi(Pn[t]).off("load",nt);Pn=bi,$t=Dt=!0,Si(r,0),fi("onDestroyed")}},It.scroll=function(n,t,r,e){if(0===arguments.length||n===bi){var i=Br&&Yt&&Ht.i,o=Br&&Yt&&Ht.n,u=vn.H,f=vn.L,a=vn.N;return f=i?1-f:f,u=i?a-u:u,a*=o?-1:1,{position:{x:u*=o?-1:1,y:hn.H},ratio:{x:f,y:hn.L},max:{x:a,y:hn.N},handleOffset:{x:vn.R,y:hn.R},handleLength:{x:vn.W,y:hn.W},handleLengthRatio:{x:vn.D,y:hn.D},trackLength:{x:vn.M,y:hn.M},snappedHandleOffset:{x:vn.F,y:hn.F},isRTL:Yt,isRTLNormalized:Br}}It.update(dn);var c,s,l,v,h,m,w,d,p,y=Br,b=[pn,me,"l"],g=[bn,pe,"t"],x=["+=","-=","*=","/="],_=cn(t)==wt,O=_?t.complete:e,S={},z={},k="begin",C="nearest",I="never",T="ifneeded",A=gi.l,H=[pn,bn,"xy","yx"],E=[k,"end","center",C],L=["always",I,T],N=n[gi.hOP]("el"),R=N?n.el:n,W=!!(R instanceof xi||yi)&&R instanceof yi,D=!W&&st(R),M=function(){s&&ii(!0),l&&ii(!1)},F=cn(O)!=yt?bi:function(){M(),O()};function j(n,t){for(c=0;c<t[A];c++)if(n===t[c])return 1}function P(n,t){var r=n?b:g;if(t=cn(t)==_t||cn(t)==St?[t,t]:t,cn(t)==xt)return n?t[0]:t[1];if(cn(t)==wt)for(c=0;c<r[A];c++)if(r[c]in t)return t[r[c]]}function B(n,t){var r,e,i,o,u=cn(t)==_t,f=n?vn:hn,a=f.H,c=f.N,s=Yt&&n,l=s&&Ht.n&&!y,v="replace",h=eval;if((e=u?(2<t[A]&&(o=t.substr(0,2),-1<sn(o,x)&&(r=o)),t=(t=r?t.substr(2):t)[v](/min/g,0)[v](/</g,0)[v](/max/g,(l?"-":Se)+Ce)[v](/>/g,(l?"-":Se)+Ce)[v](/px/g,Se)[v](/%/g," * "+c*(s&&Ht.n?-1:1)/100)[v](/vw/g," * "+se.w)[v](/vh/g," * "+se.h),ci(isNaN(t)?ci(h(t),!0).toFixed():t)):t)!==bi&&!isNaN(e)&&cn(e)==St){var d=y&&s,p=a*(d&&Ht.n?-1:1),b=d&&Ht.i,g=d&&Ht.n;switch(p=b?c-p:p,r){case"+=":i=p+e;break;case"-=":i=p-e;break;case"*=":i=p*e;break;case"/=":i=p/e;break;default:i=e}i=b?c-i:i,i*=g?-1:1,i=s&&Ht.n?wi.min(0,wi.max(c,i)):wi.max(0,wi.min(c,i))}return i===a?bi:i}function Q(n,t,r,e){var i,o,u=[r,r],f=cn(n);if(f==t)n=[n,n];else if(f==xt){if(2<(i=n[A])||i<1)n=u;else for(1===i&&(n[1]=r),c=0;c<i;c++)if(o=n[c],cn(o)!=t||!j(o,e)){n=u;break}}else n=f==wt?[n[pn]||r,n[bn]||r]:u;return{x:n[0],y:n[1]}}function U(n){var t,r,e=[],i=[pe,be,ge,me];for(c=0;c<n[A]&&c!==i[A];c++)t=n[c],(r=cn(t))==Ot?e.push(t?ci(p.css(ve+i[c])):0):e.push(r==St?t:0);return e}if(W||D){var V,q=N?n.margin:0,X=N?n.axis:0,Y=N?n.scroll:0,$=N?n.block:0,G=[0,0,0,0],K=cn(q);if(0<(p=W?R:xi(R))[A]){q=K==St||K==Ot?U([q,q,q,q]):K==xt?2===(V=q[A])?U([q[0],q[1],q[0],q[1]]):4<=V?U(q):G:K==wt?U([q[pe],q[be],q[ge],q[me]]):G,h=j(X,H)?X:"xy",m=Q(Y,_t,"always",L),w=Q($,_t,k,E),d=q;var J=vn.H,Z=hn.H,nn=er.offset(),tn=p.offset(),rn={x:m.x==I||h==bn,y:m.y==I||h==pn};tn[pe]-=d[0],tn[me]-=d[3];var en={x:wi.round(tn[me]-nn[me]+J),y:wi.round(tn[pe]-nn[pe]+Z)};if(Yt&&(Ht.n||Ht.i||(en.x=wi.round(nn[me]-tn[me]+J)),Ht.n&&y&&(en.x*=-1),Ht.i&&y&&(en.x=wi.round(nn[me]-tn[me]+(vn.N-J)))),w.x!=k||w.y!=k||m.x==T||m.y==T||Yt){var on=p[0],un=ln?on[gi.bCR]():{width:on[gi.oW],height:on[gi.oH]},fn={w:un[xe]+d[3]+d[1],h:un[_e]+d[0]+d[2]},an=function(n){var t=ui(n),r=t.j,e=t.P,i=t.B,o=w[i]==(n&&Yt?k:"end"),u="center"==w[i],f=w[i]==C,a=m[i]==I,c=m[i]==T,s=se[r],l=nn[e],v=fn[r],h=tn[e],d=u?2:1,p=h+v/2,b=l+s/2,g=v<=s&&l<=h&&h+v<=l+s;a?rn[i]=!0:rn[i]||((f||c)&&(rn[i]=c&&g,o=v<s?b<p:p<b),en[i]-=o||u?(s/d-v/d)*(n&&Yt&&y?-1:1):0)};an(!0),an(!1)}rn.y&&delete en.y,rn.x&&delete en.x,n=en}}S[Te]=B(!0,P(!0,n)),S[Ae]=B(!1,P(!1,n)),s=S[Te]!==bi,l=S[Ae]!==bi,(s||l)&&(0<t||_)?_?(t.complete=F,ir.animate(S,t)):(v={duration:t,complete:F},cn(r)==xt||xi.isPlainObject(r)?(z[Te]=r[0]||r.x,z[Ae]=r[1]||r.y,v.specialEasing=z):v.easing=r,ir.animate(S,v)):(s&&ir[Te](S[Te]),l&&ir[Ae](S[Ae]),M())},It.scrollStop=function(n,t,r){return ir.stop(n,t,r),It},It.getElements=function(n){var t={target:sr,host:lr,padding:hr,viewport:dr,content:pr,scrollbarHorizontal:{scrollbar:a[0],track:s[0],handle:d[0]},scrollbarVertical:{scrollbar:p[0],track:b[0],handle:g[0]},scrollbarCorner:cr[0]};return cn(n)==_t?ht(t,n):t},It.getState=function(n){function t(n){if(!xi.isPlainObject(n))return n;var r=hi({},n),t=function(n,t){r[gi.hOP](n)&&(r[t]=r[n],delete r[n])};return t("w",xe),t("h",_e),delete r.c,r}var r={destroyed:!!t(Dt),sleeping:!!t($t),autoUpdate:t(!Jr),widthAuto:t(xr),heightAuto:t(_r),padding:t(zr),overflowAmount:t(Lr),hideOverflow:t(yr),hasOverflow:t(wr),contentScrollSize:t(gr),viewportSize:t(se),hostSize:t(br),documentMixed:t(w)};return cn(n)==_t?ht(r,n):r},It.ext=function(n){var t,r="added removed on contract".split(" "),e=0;if(cn(n)==_t){if(jn[gi.hOP](n))for(t=hi({},jn[n]);e<r.length;e++)delete t[r[e]]}else for(e in t={},jn)t[e]=hi({},It.ext(e));return t},It.addExt=function(n,t){var r,e,i,o,u=_i.extension(n),f=!0;if(u){if(jn[gi.hOP](n))return It.ext(n);if((r=u.extensionFactory.call(It,hi({},u.defaultOptions),xi,mi))&&(i=r.contract,cn(i)==yt&&(o=i(gt),f=cn(o)==Ot?o:f),f))return e=(jn[n]=r).added,cn(e)==yt&&e(t),It.ext(n)}else console.warn('A extension with the name "'+n+"\" isn't registered.")},It.removeExt=function(n){var t,r=jn[n];return!!r&&(delete jn[n],t=r.removed,cn(t)==yt&&t(),!0)},_i.valid(function bt(n,t,r){var e,i;return o=kt.defaultOptions,Lt=kt.nativeScrollbarStyling,Rt=hi({},kt.nativeScrollbarSize),Tt=hi({},kt.nativeScrollbarIsOverlaid),At=hi({},kt.overlayScrollbarDummySize),Ht=hi({},kt.rtlScrollBehavior),et(hi({},o,t)),Nt=kt.cssCalc,R=kt.msie,Et=kt.autoUpdateRecommended,W=kt.supportTransition,ln=kt.supportTransform,m=kt.supportPassiveEvents,T=kt.supportResizeObserver,l=kt.supportMutationObserver,kt.restrictedMeasuring,D=xi(n.ownerDocument),A=D[0],f=xi(A.defaultView||A.parentWindow),y=f[0],h=pt(D,"html"),M=pt(h,"body"),Zt=xi(n),sr=Zt[0],Mt=Zt.is("textarea"),Ft=Zt.is("body"),w=A!==mt,v=Mt?Zt.hasClass(an)&&Zt.parent().hasClass(xn):Zt.hasClass(rn)&&Zt.children(N+mn)[gi.l],Tt.x&&Tt.y&&!Gt.nativeScrollbarsOverlaid.initialize?(fi("onInitializationWithdrawn"),v&&(it(!0),ut(!0),at(!0)),$t=Dt=!0):(Ft&&((e={}).l=wi.max(Zt[Te](),h[Te](),f[Te]()),e.t=wi.max(Zt[Ae](),h[Ae](),f[Ae]()),i=function(){ir.removeAttr(gi.ti),qn(ir,U,i,!0,!0)}),it(),ut(),at(),ot(),ft(!0),ft(!1),function s(){var r,t=y.top!==y,e={},i={},o={};function u(n){if(a(n)){var t=c(n),r={};(fe||ue)&&(r[xe]=i.w+(t.x-e.x)*o.x),(ae||ue)&&(r[_e]=i.h+(t.y-e.y)*o.y),nr.css(r),mi.stpP(n)}else f(n)}function f(n){var t=n!==bi;qn(D,[K,q,V],[Zn,u,f],!0),pi(M,Wn),cr.releaseCapture&&cr.releaseCapture(),t&&(r&&$e(),It.update(ze)),r=!1}function a(n){var t=(n.originalEvent||n).touches!==bi;return!$t&&!Dt&&(1===mi.mBtn(n)||t)}function c(n){return R&&t?{x:n.screenX,y:n.screenY}:mi.page(n)}Xn(cr,U,function(n){a(n)&&!oe&&(Jr&&(r=!0,Ge()),e=c(n),i.w=lr[gi.oW]-(jt?0:Bt),i.h=lr[gi.oH]-(jt?0:Qt),o=ct(),qn(D,[K,q,V],[Zn,u,f]),di(M,Wn),cr.setCapture&&cr.setCapture(),mi.prvD(n),mi.stpP(n))})}(),Yn(),Ye(rr,$n),Ft&&(ir[Te](e.l)[Ae](e.t),mt.activeElement==n&&dr.focus&&(ir.attr(gi.ti,"-1"),dr.focus(),qn(ir,U,i,!1,!0))),It.update(ze),Wt=!0,fi("onInitialized"),c(Mn,function(n,t){fi(t.n,t.a)}),Mn=[],cn(r)==_t&&(r=[r]),mi.isA(r)?c(r,function(n,t){It.addExt(t)}):xi.isPlainObject(r)&&c(r,function(n,t){It.addExt(n,t)}),setTimeout(function(){W&&!Dt&&di(nr,fn)},333)),It}(r,n,t))&&Si(r,It),It}function qn(n,t,r,e,i){var o=cn(t)==xt&&cn(r)==xt,u=e?"removeEventListener":"addEventListener",f=e?"off":"on",a=!o&&t.split(Ie),c=0;if(o)for(;c<t[gi.l];c++)qn(n,t[c],r[c],e);else for(;c<a[gi.l];c++)m?n[0][u](a[c],r,{passive:i||!1}):n[f](a[c],r)}function Xn(n,t,r,e){qn(n,t,r,!1,e),Vn.push(mi.bind(qn,0,n,t,r,!0,e))}function Ye(n,t){if(n){var r=mi.rO(),e="animationstart mozAnimationStart webkitAnimationStart MSAnimationStart",i="childNodes",o=3333333,u=function(){n[Ae](o)[Te](Yt?Ht.n?-o:Ht.i?0:o:o),t()};if(t){if(T)((C=n.addClass("observed").append(li(_n)).contents()[0])[Z]=new r(u)).observe(C);else if(9<R||!Et){n.prepend(li(_n,li({c:On,dir:"ltr"},li(On,li(Sn))+li(On,li({c:Sn,style:"width: 200%; height: 200%"})))));var f,a,c,s,l=n[0][i][0][i][0],v=xi(l[i][1]),h=xi(l[i][0]),d=xi(h[0][i][0]),p=l[gi.oW],b=l[gi.oH],g=kt.nativeScrollbarSize,m=function(){h[Te](o)[Ae](o),v[Te](o)[Ae](o)},w=function(){a=0,f&&(p=c,b=s,u())},y=function(n){return c=l[gi.oW],s=l[gi.oH],f=c!=p||s!=b,n&&f&&!a?(mi.cAF()(a),a=mi.rAF()(w)):n||w(),m(),n&&(mi.prvD(n),mi.stpP(n)),!1},x={},_={};ai(_,Se,[-2*(g.y+1),-2*g.x,-2*g.y,-2*(g.x+1)]),xi(l).css(_),h.on(ke,y),v.on(ke,y),n.on(e,function(){y(!1)}),x[xe]=o,x[_e]=o,d.css(x),m()}else{var O=A.attachEvent,S=R!==bi;if(O)n.prepend(li(_n)),pt(n,N+_n)[0].attachEvent("onresize",u);else{var z=A.createElement(wt);z.setAttribute(gi.ti,"-1"),z.setAttribute(gi.c,_n),z.onload=function(){var n=this.contentDocument.defaultView;n.addEventListener("resize",u),n.document.documentElement.style.display="none"},z.type="text/html",S&&n.prepend(z),z.data="about:blank",S||n.prepend(z),n.on(e,u)}}if(n[0]===H){var k=function(){var n=nr.css("direction"),t={},r=0,e=!1;return n!==E&&(r="ltr"===n?(t[me]=0,t[be]=ze,o):(t[me]=ze,t[be]=0,Ht.n?-o:Ht.i?0:o),rr.children().eq(0).css(t),rr[Te](r)[Ae](o),E=n,e=!0),e};k(),Xn(n,ke,function(n){return k()&&ni(),mi.prvD(n),mi.stpP(n),!1})}}else if(T){var C,I=(C=n.contents()[0])[Z];I&&(I.disconnect(),delete C[Z])}else dt(n.children(N+_n).eq(0))}}function Yn(){if(l){var i,o,r,e,u,f,n=mi.mO(),a=mi.now();k=function(n){var t=!1;return Wt&&!$t&&(c(n,function(){return!(t=function o(n){var t=n.attributeName,r=n.target,e=n.type,i="closest";if(r===pr)return null===t;if("attributes"===e&&(t===gi.c||t===gi.s)&&!Mt){if(t===gi.c&&xi(r).hasClass(rn))return tt(n.oldValue,r.getAttribute(gi.c));if(typeof r[i]!=yt)return!0;if(null!==r[i](N+_n)||null!==r[i](N+kn)||null!==r[i](N+Ln))return!1}return!0}(this))}),t&&(e=mi.now(),u=_r||xr,f=function(){Dt||(a=e,Mt&&Ze(),u?ni():It.update(ze))},clearTimeout(r),11<e-a||!u?f():r=setTimeout(f,11))),t},O=new n(z=function(n){var t,r=!1,e=[];return Wt&&!$t&&(c(n,function(){i=(t=this).target,o=t.attributeName,r=r||(o===gi.c?tt(t.oldValue,i.className):o!==gi.s||t.oldValue!==i[gi.s].cssText),e.push(o)}),rt(e),r&&It.update(ze)),r}),S=new n(k)}}function $e(){l&&!Jr&&(O.observe(lr,{attributes:!0,attributeOldValue:!0,attributeFilter:Un}),S.observe(Mt?sr:pr,{attributes:!0,attributeOldValue:!0,subtree:!Mt,childList:!Mt,characterData:!Mt,attributeFilter:Mt?Qn:Un}),Jr=!0)}function Ge(){l&&Jr&&(O.disconnect(),S.disconnect(),Jr=!1)}function $n(){if(!$t){var n,t={w:H[gi.sW],h:H[gi.sH]};n=vi(t,_),_=t,n&&ni({T:!0})}}function Gn(){ie&&ri(!0)}function Kn(){ie&&!M.hasClass(Wn)&&ri(!1)}function Jn(){ee&&(ri(!0),clearTimeout(I),I=setTimeout(function(){ee&&!Dt&&ri(!1)},100))}function Zn(n){return mi.prvD(n),!1}function nt(){ni({C:!0})}function Ke(n){qn(nr,q,Jn,!ee||n,!0),qn(nr,[X,Y],[Gn,Kn],!!ee||n,!0),Wt||n||nr.one("mouseover",Gn)}function Je(){var n={};return Ft&&ur&&(n.w=ci(ur.css(we+xe)),n.h=ci(ur.css(we+_e)),n.c=vi(n,Gr),n.f=!0),!!(Gr=n).c}function tt(n,t){var r=t!==bi&&null!==t?t.split(Ie):Se,e=n!==bi&&null!==n?n.split(Ie):Se;if(r===Se&&e===Se)return!1;var i,o,u,f,a,c=function d(n,t){var r,e,i=[],o=[];for(r=0;r<n.length;r++)i[n[r]]=!0;for(r=0;r<t.length;r++)i[t[r]]?delete i[t[r]]:i[t[r]]=!0;for(e in i)o.push(e);return o}(e,r),s=!1,l=Ur!==bi&&null!==Ur?Ur.split(Ie):[Se],v=Qr!==bi&&null!==Qr?Qr.split(Ie):[Se],h=sn(Ve,c);for(-1<h&&c.splice(h,1),o=0;o<c.length;o++)if(0!==(i=c[o]).indexOf(rn)){for(a=f=!0,u=0;u<l.length;u++)if(i===l[u]){f=!1;break}for(u=0;u<v.length;u++)if(i===v[u]){a=!1;break}if(f&&a){s=!0;break}}return s}function rt(n){c(n=n||Fn,function(n,t){if(-1<mi.inA(t,Fn)){var r=Zt.attr(t);cn(r)==_t?ir.attr(t,r):ir.removeAttr(t)}})}function Ze(){if(!$t){var n,t,r,e,i=!Vr,o=se.w,u=se.h,f={},a=xr||i;return f[we+xe]=Se,f[we+_e]=Se,f[xe]=ze,Zt.css(f),n=sr[gi.oW],t=a?wi.max(n,sr[gi.sW]-1):1,f[xe]=xr?ze:Ce,f[we+xe]=Ce,f[_e]=ze,Zt.css(f),r=sr[gi.oH],e=wi.max(r,sr[gi.sH]-1),f[xe]=t,f[_e]=e,ar.css(f),f[we+xe]=o,f[we+_e]=u,Zt.css(f),{Q:n,U:r,V:t,X:e}}}function ni(n){clearTimeout(Jt),n=n||{},qe.T|=n.T,qe.C|=n.C,qe.A|=n.A;var t,r=mi.now(),e=!!qe.T,i=!!qe.C,o=!!qe.A,u=n.I,f=0<Xe&&Wt&&!Dt&&!o&&!u&&r-Kt<Xe&&!_r&&!xr;if(f&&(Jt=setTimeout(ni,Xe)),!(Dt||f||$t&&!u||Wt&&!o&&(t=nr.is(":hidden"))||"inline"===nr.css("display"))){Kt=r,qe={},!Lt||Tt.x&&Tt.y?Rt=hi({},kt.nativeScrollbarSize):(Rt.x=0,Rt.y=0),le={x:3*(Rt.x+(Tt.x?0:3)),y:3*(Rt.y+(Tt.y?0:3))};var a=function(){return vi.apply(this,[].slice.call(arguments).concat([o]))},c={x:ir[Te](),y:ir[Ae]()},s=Gt.scrollbars,l=Gt.textarea,v=s.visibility,h=a(v,Dr),d=s.autoHide,p=a(d,Mr),b=s.clickScrolling,g=a(b,Fr),m=s.dragScrolling,w=a(m,jr),y=Gt.className,x=a(y,Qr),_=Gt.resize,O=a(_,Pr)&&!Ft,S=Gt.paddingAbsolute,z=a(S,Tr),k=Gt.clipAlways,C=a(k,Ar),I=Gt.sizeAutoCapable&&!Ft,T=a(I,Wr),A=Gt.nativeScrollbarsOverlaid.showNativeScrollbars,H=a(A,Nr),E=Gt.autoUpdate,L=a(E,Rr),N=Gt.overflowBehavior,R=a(N,Er,o),W=l.dynWidth,D=a($r,W),M=l.dynHeight,F=a(Yr,M);if(te="n"===d,re="s"===d,ee="m"===d,ie="l"===d,ne=s.autoHideDelay,Ur=Qr,oe="n"===_,ue="b"===_,fe="h"===_,ae="v"===_,Br=Gt.normalizeRTL,A=A&&Tt.x&&Tt.y,Dr=v,Mr=d,Fr=b,jr=m,Qr=y,Pr=_,Tr=S,Ar=k,Wr=I,Nr=A,Rr=E,Er=hi({},N),$r=W,Yr=M,wr=wr||{x:!1,y:!1},x&&(pi(nr,Ur+Ie+Ve),di(nr,y!==bi&&null!==y&&0<y.length?y:Ve)),L&&(!0===E||null===E&&Et?(Ge(),Ct.add(It)):(Ct.remove(It),$e())),T)if(I)if(fr?fr.show():(fr=xi(li(Fe)),er.before(fr)),Pt)tr.show();else{tr=xi(li(je)),vr=tr[0],fr.before(tr);var j={w:-1,h:-1};Ye(tr,function(){var n={w:vr[gi.oW],h:vr[gi.oH]};vi(n,j)&&(Wt&&_r&&0<n.h||xr&&0<n.w||Wt&&!_r&&0===n.h||!xr&&0===n.w)&&ni(),j=n}),Pt=!0,null!==Nt&&tr.css(_e,Nt+"(100% + 1px)")}else Pt&&tr.hide(),fr&&fr.hide();o&&(rr.find("*").trigger(ke),Pt&&tr.find("*").trigger(ke));a(t=t===bi?nr.is(":hidden"):t,Kr);var P,B=!!Mt&&"off"!==Zt.attr("wrap"),Q=a(B,Vr),U=nr.css("direction"),V=a(U,Ir),q=nr.css("box-sizing"),X=a(q,Sr),Y={c:o,t:ci(nr.css(he+pe)),r:ci(nr.css(he+be)),b:ci(nr.css(he+ge)),l:ci(nr.css(he+me))};try{P=Pt?vr[gi.bCR]():null}catch(St){return}jt="border-box"===q;var $=(Yt="rtl"===U)?me:be,G=Yt?be:me,K=!1,J=!(!Pt||"none"===nr.css(Oe))&&(0===wi.round(P.right-P.left)&&(!!S||0<lr[gi.cW]-Bt));if(I&&!J){var Z=lr[gi.oW],nn=fr.css(xe);fr.css(xe,ze);var tn=lr[gi.oW];fr.css(xe,nn),(K=Z!==tn)||(fr.css(xe,Z+1),tn=lr[gi.oW],fr.css(xe,nn),K=Z!==tn)}var rn=(J||K)&&I&&!t,en=a(rn,xr),on=!rn&&xr,un=!(!Pt||!I||t)&&0===wi.round(P.bottom-P.top),fn=a(un,_r),an=!un&&_r,cn="-"+xe,sn=rn&&jt||!jt,ln=un&&jt||!jt,vn={c:o,t:ln?ci(nr.css(de+pe+cn),!0):0,r:sn?ci(nr.css(de+be+cn),!0):0,b:ln?ci(nr.css(de+ge+cn),!0):0,l:sn?ci(nr.css(de+me+cn),!0):0},hn={c:o,t:ci(nr.css(ve+pe)),r:ci(nr.css(ve+be)),b:ci(nr.css(ve+ge)),l:ci(nr.css(ve+me))},dn={h:String(nr.css(ye+_e)),w:String(nr.css(ye+xe))},pn={},bn={},gn=function(){return{w:lr[gi.cW],h:lr[gi.cH]}},mn=function(){return{w:hr[gi.oW]+wi.max(0,pr[gi.cW]-pr[gi.sW]),h:hr[gi.oH]+wi.max(0,pr[gi.cH]-pr[gi.sH])}},wn=Bt=Y.l+Y.r,yn=Qt=Y.t+Y.b;if(wn*=S?1:0,yn*=S?1:0,Y.c=a(Y,zr),Ut=vn.l+vn.r,Vt=vn.t+vn.b,vn.c=a(vn,kr),qt=hn.l+hn.r,Xt=hn.t+hn.b,hn.c=a(hn,Cr),dn.ih=ci(dn.h),dn.iw=ci(dn.w),dn.ch=-1<dn.h.indexOf("px"),dn.cw=-1<dn.w.indexOf("px"),dn.c=a(dn,Or),Kr=t,Vr=B,Ir=U,Sr=q,xr=rn,_r=un,zr=Y,kr=vn,Cr=hn,Or=dn,V&&Pt&&tr.css(Oe,G),Y.c||V||z||en||fn||X||T){var xn={},_n={};ai(bn,ve,[-Y.t,-Y.r,-Y.b,-Y.l]),S?(ai(xn,Se,[Y.t,Y.r,Y.b,Y.l]),ai(Mt?_n:pn,he)):(ai(xn,Se),ai(Mt?_n:pn,he,[Y.t,Y.r,Y.b,Y.l])),er.css(xn),Zt.css(_n)}se=mn();var On=!!Mt&&Ze(),Sn=Mt&&a(On,Xr),zn=Mt&&On?{w:W?On.V:On.Q,h:M?On.X:On.U}:{};if(Xr=On,un&&(fn||z||X||dn.c||Y.c||vn.c)?pn[_e]=ze:(fn||z)&&(pn[ye+_e]=Se,pn[_e]=Ce),rn&&(en||z||X||dn.c||Y.c||vn.c||V)?(pn[xe]=ze,bn[ye+xe]=Ce):(en||z)&&(pn[ye+xe]=Se,pn[xe]=Ce,pn[Oe]=Se,bn[ye+xe]=Se),rn?(dn.cw||(pn[ye+xe]=Se),bn[xe]=ze,pn[xe]=ze,pn[Oe]=G):bn[xe]=Se,un?(dn.ch||(pn[ye+_e]=Se),bn[_e]=zn.h||pr[gi.cH]):bn[_e]=Se,I&&fr.css(bn),or.css(pn),pn={},bn={},e||i||Sn||V||X||z||en||rn||fn||un||dn.c||H||R||C||O||h||p||w||g||D||F||Q){var kn="overflow",Cn=kn+"-x",In=kn+"-y";if(!Lt){var Tn={},An=wr.y&&yr.ys&&!A?Tt.y?ir.css($):-Rt.y:0,Hn=wr.x&&yr.xs&&!A?Tt.x?ir.css(ge):-Rt.x:0;ai(Tn,Se),ir.css(Tn)}var En=si(),Ln={w:zn.w||En[gi.cW],h:zn.h||En[gi.cH]},Nn=En[gi.sW],Rn=En[gi.sH];Lt||(Tn[ge]=an?Se:Hn,Tn[$]=on?Se:An,ir.css(Tn)),se=mn();var Wn=gn(),Dn={w:wi.max((rn?Ln.w:Nn)+wn,Wn.w),h:wi.max((un?Ln.h:Rn)+yn,Wn.h)};if(Dn.c=a(Dn,Hr),Hr=Dn,I){(Dn.c||un||rn)&&(bn[xe]=Dn.w,bn[_e]=Dn.h,Mt||(Ln={w:En[gi.cW],h:En[gi.cH]}));var Mn={},Fn=function(n){var t=ui(n),r=t.j,e=t.Y,i=n?rn:un,o=n?Ut:Vt,u=n?Bt:Qt,f=n?qt:Xt,a=bn[e]+(jt?o:-u);i&&(i||!vn.c)||(bn[e]=Wn[r]-(jt?0:u+o)-1-f),i&&dn["c"+r]&&dn["i"+r]===a&&(bn[e]=a+(jt?0:u)+1),!(i&&Ln[r]<se[r])||n&&Mt&&B||(Mt&&(Mn[e]=ci(ar.css(e))-1),--bn[e]),0<Ln[r]&&(bn[e]=wi.max(1,bn[e]))};Fn(!0),Fn(!1),Mt&&ar.css(Mn),fr.css(bn)}rn&&(pn[xe]=Ce),!rn||jt||Jr||(pn[Oe]="none"),or.css(pn),pn={};var jn={w:En[gi.sW],h:En[gi.sH]};jn.c=i=a(jn,gr),gr=jn,se=mn(),e=a(Wn=gn(),br),br=Wn;var Pn=Mt&&(0===se.w||0===se.h),Bn=Lr,Qn={},Un={},Vn={},qn={},Xn={},Yn={},$n={},Gn=hr[gi.bCR](),Kn=function(n){var t=ui(n),r=ui(!n).B,e=t.B,i=t.j,o=t.Y,u=ke+t.$+"Max",f=Gn[o]?wi.abs(Gn[o]-se[i]):0,a=Bn&&0<Bn[e]&&0===dr[u];Qn[e]="v-s"===N[e],Un[e]="v-h"===N[e],Vn[e]="s"===N[e],qn[e]=wi.max(0,wi.round(100*(jn[i]-se[i]))/100),qn[e]*=Pn||a&&0<f&&f<1?0:1,Xn[e]=0<qn[e],Yn[e]=Qn[e]||Un[e]?Xn[r]&&!Qn[r]&&!Un[r]:Xn[e],Yn[e+"s"]=!!Yn[e]&&(Vn[e]||Qn[e]),$n[e]=Xn[e]&&Yn[e+"s"]};if(Kn(!0),Kn(!1),qn.c=a(qn,Lr),Lr=qn,Xn.c=a(Xn,wr),wr=Xn,Yn.c=a(Yn,yr),yr=Yn,Tt.x||Tt.y){var Jn,Zn={},nt={},tt=o;(Xn.x||Xn.y)&&(nt.w=Tt.y&&Xn.y?jn.w+At.y:Se,nt.h=Tt.x&&Xn.x?jn.h+At.x:Se,tt=a(nt,mr),mr=nt),(Xn.c||Yn.c||jn.c||V||en||fn||rn||un||H)&&(pn[ve+G]=pn[de+G]=Se,Jn=function(n){var t=ui(n),r=ui(!n),e=t.B,i=n?ge:$,o=n?un:rn;Tt[e]&&Xn[e]&&Yn[e+"s"]?(pn[ve+i]=!o||A?Se:At[e],pn[de+i]=n&&o||A?Se:At[e]+"px solid transparent"):(nt[r.j]=pn[ve+i]=pn[de+i]=Se,tt=!0)},Lt?(A?pi:di)(ir,De):(Jn(!0),Jn(!1))),A&&(nt.w=nt.h=Se,tt=!0),tt&&!Lt&&(Zn[xe]=Yn.y?nt.w:Se,Zn[_e]=Yn.x?nt.h:Se,ur||(ur=xi(li(Me)),ir.prepend(ur)),ur.css(Zn)),or.css(pn)}var rt,et={};xn={};if((e||Xn.c||Yn.c||jn.c||R||X||H||V||C||fn)&&(et[G]=Se,(rt=function(n){var t=ui(n),r=ui(!n),e=t.B,i=t.G,o=n?ge:$,u=function(){et[o]=Se,ce[r.j]=0};Xn[e]&&Yn[e+"s"]?(et[kn+i]=ke,A||Lt?u():(et[o]=-(Tt[e]?At[e]:Rt[e]),ce[r.j]=Tt[e]?At[r.B]:0)):(et[kn+i]=Se,u())})(!0),rt(!1),!Lt&&(se.h<le.x||se.w<le.y)&&(Xn.x&&Yn.x&&!Tt.x||Xn.y&&Yn.y&&!Tt.y)?(et[he+pe]=le.x,et[ve+pe]=-le.x,et[he+G]=le.y,et[ve+G]=-le.y):et[he+pe]=et[ve+pe]=et[he+G]=et[ve+G]=Se,et[he+$]=et[ve+$]=Se,Xn.x&&Yn.x||Xn.y&&Yn.y||Pn?Mt&&Pn&&(xn[Cn]=xn[In]="hidden"):(!k||Un.x||Qn.x||Un.y||Qn.y)&&(Mt&&(xn[Cn]=xn[In]=Se),et[Cn]=et[In]="visible"),er.css(xn),ir.css(et),et={},(Xn.c||X||en||fn)&&(!Tt.x||!Tt.y))){var it=pr[gi.s];it.webkitTransform="scale(1)",it.display="run-in",pr[gi.oH],it.display=Se,it.webkitTransform=Se}if(pn={},V||en||fn)if(Yt&&rn){var ot=or.css(Oe),ut=wi.round(or.css(Oe,Se).css(me,Se).position().left);or.css(Oe,ot),ut!==wi.round(or.position().left)&&(pn[me]=ut)}else pn[me]=Se;if(or.css(pn),Mt&&i){var ft=function zt(){var n=sr.selectionStart;if(n===bi)return;var t,r,e=Zt.val(),i=e[gi.l],o=e.split("\n"),u=o[gi.l],f=e.substr(0,n).split("\n"),a=0,c=0,s=f[gi.l],l=f[f[gi.l]-1][gi.l];for(r=0;r<o[gi.l];r++)t=o[r][gi.l],c<t&&(a=r+1,c=t);return{K:s,J:l,Z:u,nn:c,tn:a,rn:n,en:i}}();if(ft){var at=qr===bi||ft.Z!==qr.Z,ct=ft.K,st=ft.J,lt=ft.tn,vt=ft.Z,ht=ft.nn,dt=ft.rn,pt=ft.en<=dt&&Zr,bt={x:B||st!==ht||ct!==lt?-1:Lr.x,y:(B?pt||at&&Bn&&c.y===Bn.y:(pt||at)&&ct===vt)?Lr.y:-1};c.x=-1<bt.x?Yt&&Br&&Ht.i?0:bt.x:c.x,c.y=-1<bt.y?bt.y:c.y}qr=ft}Yt&&Ht.i&&Tt.y&&Xn.x&&Br&&(c.x+=ce.w||0),rn&&nr[Te](0),un&&nr[Ae](0),ir[Te](c.x)[Ae](c.y);var gt="v"===v,mt="h"===v,wt="a"===v,yt=mi.bind(ti,0,!0,!0,$n.x),xt=mi.bind(ti,0,!1,!0,$n.y),_t=mi.bind(ti,0,!0,!1,$n.x),Ot=mi.bind(ti,0,!1,!1,$n.y);(Yn.x||Yn.y?di:pi)(nr,Ne),(Yn.x?di:pi)(nr,Re),(Yn.y?di:pi)(nr,We),V&&(Yt?di:pi)(nr,He),Ft&&di(nr,Ee),O&&(pi(cr,[Pe,Be,Qe,Ue].join(Ie)),oe?di(nr,Ee):(pi(nr,Ee),di(cr,Pe),ue?di(cr,Be):fe?di(cr,Qe):ae&&di(cr,Ue))),(h||R||Yn.c||Xn.c||H)&&(A?H&&(pi(nr,Le),A&&(_t(),Ot())):wt?(($n.x?yt:_t)(),($n.y?xt:Ot)()):gt?(yt(),xt()):mt&&(_t(),Ot())),(p||H)&&(ie||ee?(Ke(!0),Ke()):Ke(!0),te?ri(!0):ri(!1,!0)),(e||qn.c||fn||en||O||X||z||H||V)&&(ei(!0),ii(!0),ei(!1),ii(!1)),g&&oi(!0,b),w&&oi(!1,m),V&&fi("onDirectionChanged",{isRTL:Yt,dir:U}),e&&fi("onHostSizeChanged",{width:br.w,height:br.h}),i&&fi("onContentSizeChanged",{width:gr.w,height:gr.h}),(Xn.c||Yn.c)&&fi("onOverflowChanged",{x:Xn.x,y:Xn.y,xScrollable:Yn.xs,yScrollable:Yn.ys,clipped:Yn.x||Yn.y}),qn.c&&fi("onOverflowAmountChanged",{x:qn.x,y:qn.y})}Ft&&Gr&&(wr.c||Gr.c)&&(Gr.f||Je(),Tt.y&&wr.x&&or.css(we+xe,Gr.w+At.y),Tt.x&&wr.y&&or.css(we+_e,Gr.h+At.x),Gr.c=!1),fi("onUpdated",{forced:o})}}function et(n){var t=Oi._(n,Oi.m,!0,u);return u=hi({},u,t.O),Gt=hi({},Gt,t.S),t.S}function it(e){var n="parent",t=an+Ie+zn,r=Mt?Ie+zn:Se,i=Gt.textarea.inheritedAttrs,o={},u=function(){var r=e?Zt:nr;c(o,function(n,t){cn(t)==_t&&(n==gi.c?r.addClass(t):r.attr(n,t))})},f=[rn,en,Ee,He,on,un,fn,Le,Ne,Re,We,Ve,an,zn,Qr].join(Ie),a={};nr=nr||(Mt?v?Zt[n]()[n]()[n]()[n]():xi(li(en)):Zt),or=or||vt(xn+r),ir=ir||vt(wn+r),er=er||vt(mn+r),rr=rr||vt("os-resize-observer-host"),ar=ar||(Mt?vt(gn):bi),e&&pi(nr,f),i=cn(i)==_t?i.split(Ie):i,cn(i)==xt&&Mt&&c(i,function(n,t){cn(t)==_t&&(o[t]=e?nr.attr(t):Zt.attr(t))}),e?(v&&Wt?(rr.children().remove(),c([er,ir,or,ar],function(n,t){t&&pi(t.removeAttr(gi.s),Dn)}),di(nr,Mt?en:rn)):(dt(rr),or.contents().unwrap().unwrap().unwrap(),Mt&&(Zt.unwrap(),dt(nr),dt(ar),u())),Mt&&Zt.removeAttr(gi.s),Ft&&pi(h,tn)):(Mt&&(Gt.sizeAutoCapable||(a[xe]=Zt.css(xe),a[_e]=Zt.css(_e)),v||Zt.addClass(zn).wrap(nr),nr=Zt[n]().css(a)),v||(di(Zt,Mt?t:rn),nr.wrapInner(or).wrapInner(ir).wrapInner(er).prepend(rr),or=pt(nr,N+xn),ir=pt(nr,N+wn),er=pt(nr,N+mn),Mt&&(or.prepend(ar),u())),Lt&&di(ir,De),Tt.x&&Tt.y&&di(ir,yn),Ft&&di(h,tn),H=rr[0],lr=nr[0],hr=er[0],dr=ir[0],pr=or[0],rt())}function ot(){var r,t,e=[112,113,114,115,116,117,118,119,120,121,123,33,34,37,38,39,40,16,17,18,19,20,144],i=[],n="focus";function o(n){Ze(),It.update(ze),n&&Et&&clearInterval(r)}Mt?(9<R||!Et?Xn(Zt,"input",o):Xn(Zt,[$,G],[function u(n){var t=n.keyCode;sn(t,e)<0&&(i[gi.l]||(o(),r=setInterval(o,1e3/60)),sn(t,i)<0&&i.push(t))},function f(n){var t=n.keyCode,r=sn(t,i);sn(t,e)<0&&(-1<r&&i.splice(r,1),i[gi.l]||o(!0))}]),Xn(Zt,[ke,"drop",n,n+"out"],[function a(n){return Zt[Te](Ht.i&&Br?9999999:0),Zt[Ae](0),mi.prvD(n),mi.stpP(n),!1},function c(n){setTimeout(function(){Dt||o()},50)},function s(){Zr=!0,di(nr,n)},function l(){Zr=!1,i=[],pi(nr,n),o(!0)}])):Xn(or,J,function v(n){!0!==Rr&&function l(n){if(!Wt)return 1;var t="flex-grow",r="flex-shrink",e="flex-basis",i=[xe,we+xe,ye+xe,ve+me,ve+be,me,be,"font-weight","word-spacing",t,r,e],o=[he+me,he+be,de+me+xe,de+be+xe],u=[_e,we+_e,ye+_e,ve+pe,ve+ge,pe,ge,"line-height",t,r,e],f=[he+pe,he+ge,de+pe+xe,de+ge+xe],a="s"===Er.x||"v-s"===Er.x,c=!1,s=function(n,t){for(var r=0;r<n[gi.l];r++)if(n[r]===t)return!0;return!1};return("s"===Er.y||"v-s"===Er.y)&&((c=s(u,n))||jt||(c=s(f,n))),a&&!c&&((c=s(i,n))||jt||(c=s(o,n))),c}((n=n.originalEvent||n).propertyName)&&It.update(ze)}),Xn(ir,ke,function h(n){$t||(t!==bi?clearTimeout(t):((re||ee)&&ri(!0),lt()||di(nr,Le),fi("onScrollStart",n)),j||(ii(!0),ii(!1)),fi("onScroll",n),t=setTimeout(function(){Dt||(clearTimeout(t),t=bi,(re||ee)&&ri(!1),lt()||pi(nr,Le),fi("onScrollStop",n))},175))},!0)}function ut(i){var n,t,o=function(n){var t=vt(kn+Ie+(n?Nn:Rn),!0),r=vt(Cn,t),e=vt(Tn,t);return v||i||(t.append(r),r.append(e)),{"in":t,un:r,an:e}};function r(n){var t=ui(n),r=t["in"],e=t.un,i=t.an;v&&Wt?c([r,e,i],function(n,t){pi(t.removeAttr(gi.s),Dn)}):dt(r||o(n)["in"])}i?(r(!0),r()):(n=o(!0),t=o(),a=n["in"],s=n.un,d=n.an,p=t["in"],b=t.un,g=t.an,v||(er.after(p),er.after(a)))}function ft(_){var O,i,S,z,r=ui(_),k=r.cn,t=y.top!==y,C=r.B,e=r.G,I=ke+r.$,o="active",u="snapHandle",T=1,f=[16,17];function a(n){return R&&t?n["screen"+e]:mi.page(n)[C]}function c(n){return Gt.scrollbars[n]}function s(){T=.5}function l(){T=1}function v(n){-1<sn(n.keyCode,f)&&s()}function A(n){-1<sn(n.keyCode,f)&&l()}function H(n){var t=(n.originalEvent||n).touches!==bi;return!($t||Dt||lt()||!jr||t&&!c("touchSupport"))&&(1===mi.mBtn(n)||t)}function h(n){if(H(n)){var t=k.M,r=k.W,e=k.N*((a(n)-S)*z/(t-r));e=isFinite(e)?e:0,Yt&&_&&!Ht.i&&(e*=-1),ir[I](wi.round(i+e)),j&&ii(_,i+e),m||mi.prvD(n)}else E(n)}function E(n){if(n=n||n.originalEvent,qn(D,[q,V,$,G,K],[h,E,v,A,Zn],!0),j&&ii(_,!0),j=!1,pi(M,Wn),pi(r.an,o),pi(r.un,o),pi(r["in"],o),z=1,l(),O!==(S=i=bi)&&(It.scrollStop(),clearTimeout(O),O=bi),n){var t=lr[gi.bCR]();n.clientX>=t.left&&n.clientX<=t.right&&n.clientY>=t.top&&n.clientY<=t.bottom||Kn(),(re||ee)&&ri(!1)}}function L(n){i=ir[I](),i=isNaN(i)?0:i,(Yt&&_&&!Ht.n||!Yt)&&(i=i<0?0:i),z=ct()[C],S=a(n),j=!c(u),di(M,Wn),di(r.an,o),di(r["in"],o),qn(D,[q,V,K],[h,E,Zn]),!R&&w||mi.prvD(n),mi.stpP(n)}Xn(r.an,U,function d(n){H(n)&&L(n)}),Xn(r.un,[U,X,Y],[function N(n){if(H(n)){var h,d=wi.round(se[r.j]),p=r.un.offset()[r.P],t=n.ctrlKey,b=n.shiftKey,g=b&&t,m=!0,w=function(n){j&&ii(_,n)},y=function(){w(),L(n)},x=function(){if(!Dt){var n=(S-p)*z,t=k.R,r=k.M,e=k.W,i=k.N,o=k.H,u=270*T,f=m?wi.max(400,u):u,a=i*((n-e/2)/(r-e)),c=Yt&&_&&(!Ht.i&&!Ht.n||Br),s=c?t<n:n<t,l={},v={easing:"linear",step:function(n){j&&(ir[I](n),ii(_,n))}};a=isFinite(a)?a:0,a=Yt&&_&&!Ht.i?i-a:a,b?(ir[I](a),g?(a=ir[I](),ir[I](o),a=c&&Ht.i?i-a:a,a=c&&Ht.n?-a:a,l[C]=a,It.scroll(l,hi(v,{duration:130,complete:y}))):y()):(h=m?s:h,(c?h?n<=t+e:t<=n:h?t<=n:n<=t+e)?(clearTimeout(O),It.scrollStop(),O=bi,w(!0)):(O=setTimeout(x,f),l[C]=(h?"-=":"+=")+d,It.scroll(l,hi(v,{duration:u}))),m=!1)}};t&&s(),z=ct()[C],S=mi.page(n)[C],j=!c(u),di(M,Wn),di(r.un,o),di(r["in"],o),qn(D,[V,$,G,K],[E,v,A,Zn]),x(),mi.prvD(n),mi.stpP(n)}},function p(n){F=!0,(re||ee)&&ri(!0)},function b(n){F=!1,(re||ee)&&ri(!1)}]),Xn(r["in"],U,function g(n){mi.stpP(n)}),W&&Xn(r["in"],J,function(n){n.target===r["in"][0]&&(ei(_),ii(_))})}function ti(n,t,r){var e=n?on:un,i=n?a:p;(t?pi:di)(nr,e),(r?pi:di)(i,Hn)}function ri(n,t){if(clearTimeout(C),n)pi(a,En),pi(p,En);else{var r,e=function(){F||Dt||(!(r=d.hasClass("active")||g.hasClass("active"))&&(re||ee||ie)&&di(a,En),!r&&(re||ee||ie)&&di(p,En))};0<ne&&!0!==t?C=setTimeout(e,ne):e()}}function ei(n){var t={},r=ui(n),e=r.cn,i=wi.min(1,(br[r.j]-(Tr?n?Bt:Qt:0))/gr[r.j]);t[r.Y]=wi.floor(100*i*1e6)/1e6+"%",lt()||r.an.css(t),e.W=r.an[0]["offset"+r.sn],e.D=i}function ii(n,t){var r,e,i=cn(t)==Ot,o=Yt&&n,u=ui(n),f=u.cn,a="translate(",c=zt.v("transform"),s=zt.v("transition"),l=n?ir[Te]():ir[Ae](),v=t===bi||i?l:t,h=f.W,d=u.un[0]["offset"+u.sn],p=d-h,b={},g=(dr[ke+u.sn]-dr["client"+u.sn])*(Ht.n&&o?-1:1),m=function(n){return isNaN(n/g)?0:wi.max(0,wi.min(1,n/g))},w=function(n){var t=p*n;return t=isNaN(t)?0:t,t=o&&!Ht.i?d-h-t:t,t=wi.max(0,t)},y=m(l),x=w(m(v)),_=w(y);f.N=g,f.H=l,f.L=y,ln?(r=o?-(d-h-x):x,e=n?a+r+"px, 0)":a+"0, "+r+"px)",b[c]=e,W&&(b[s]=i&&1<wi.abs(x-f.R)?function O(n){var t=zt.v("transition"),r=n.css(t);if(r)return r;for(var e,i,o,u="\\s*(([^,(]+(\\(.+?\\))?)+)[\\s,]*",f=new RegExp(u),a=new RegExp("^("+u+")+$"),c="property duration timing-function delay".split(" "),s=[],l=0,v=function(n){if(e=[],!n.match(a))return n;for(;n.match(f);)e.push(RegExp.$1),n=n.replace(f,Se);return e};l<c[gi.l];l++)for(i=v(n.css(t+"-"+c[l])),o=0;o<i[gi.l];o++)s[o]=(s[o]?s[o]+Ie:Se)+i[o];return s.join(", ")}(u.an)+", "+(c+Ie+250)+"ms":Se)):b[u.P]=x,lt()||(u.an.css(b),ln&&W&&i&&u.an.one(J,function(){Dt||u.an.css(s,Se)})),f.R=x,f.F=_,f.M=d}function oi(n,t){var r=t?"removeClass":"addClass",e=n?b:g,i=n?In:An;(n?s:d)[r](i),e[r](i)}function ui(n){return{Y:n?xe:_e,sn:n?"Width":"Height",P:n?me:pe,$:n?"Left":"Top",B:n?pn:bn,G:n?"X":"Y",j:n?"w":"h",ln:n?"l":"t",un:n?s:b,an:n?d:g,"in":n?a:p,cn:n?vn:hn}}function at(n){cr=cr||vt(Ln,!0),n?v&&Wt?pi(cr.removeAttr(gi.s),Dn):dt(cr):v||nr.append(cr)}function fi(n,t){if(Wt){var r,e=Gt.callbacks[n],i=n;"on"===i.substr(0,2)&&(i=i.substr(2,1).toLowerCase()+i.substr(3)),cn(e)==yt&&e.call(It,t),c(jn,function(){cn((r=this).on)==yt&&r.on(i,t)})}else Dt||Mn.push({n:n,a:t})}function ai(n,t,r){r===bi&&(r=[Se,Se,Se,Se]),n[t+pe]=r[0],n[t+be]=r[1],n[t+ge]=r[2],n[t+me]=r[3]}function ct(){var n=hr[gi.bCR]();return{x:ln&&1/(wi.round(n.width)/hr[gi.oW])||1,y:ln&&1/(wi.round(n.height)/hr[gi.oH])||1}}function st(n){var t="ownerDocument",r="HTMLElement",e=n&&n[t]&&n[t].parentWindow||gt;return typeof e[r]==wt?n instanceof e[r]:n&&typeof n==wt&&null!==n&&1===n.nodeType&&typeof n.nodeName==_t}function ci(n,t){var r=t?parseFloat(n):parseInt(n,10);return isNaN(r)?0:r}function lt(){return Nr&&Tt.x&&Tt.y}function si(){return Mt?ar[0]:pr}function li(r,n){return"<div "+(r?cn(r)==_t?'class="'+r+'"':function(){var n,t=Se;if(xi.isPlainObject(r))for(n in r)t+=("c"===n?"class":n)+'="'+r[n]+'" ';return t}():Se)+">"+(n||Se)+"</div>"}function vt(n,t){var r=cn(t)==Ot,e=!r&&t||nr;return v&&!e[gi.l]?null:v?e[r?"children":"find"](N+n.replace(/\s/g,N)).eq(0):xi(li(n))}function ht(n,t){for(var r,e=t.split(N),i=0;i<e.length;i++){if(!n[gi.hOP](e[i]))return;r=n[e[i]],i<e.length&&cn(r)==wt&&(n=r)}return r}function vi(n,t,r){if(r)return r;if(cn(n)!=wt||cn(t)!=wt)return n!==t;for(var e in n)if("c"!==e){if(!n[gi.hOP](e)||!t[gi.hOP](e))return!0;if(vi(n[e],t[e]))return!0}return!1}function hi(){return xi.extend.apply(this,[!0].concat([].slice.call(arguments)))}function di(n,t){return e.addClass.call(n,t)}function pi(n,t){return e.removeClass.call(n,t)}function dt(n){return e.remove.call(n)}function pt(n,t){return e.find.call(n,t).eq(0)}}return yi&&yi.fn&&(yi.fn.overlayScrollbars=function(n,t){return yi.isPlainObject(n)?(yi.each(this,function(){X(this,n,t)}),this):X(this,n)}),X});