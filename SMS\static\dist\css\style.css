/* Base Styles */
body {
  font-family: 'Poppins', sans-serif !important;
  background-color: #f8f9fa !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  overflow-x: hidden !important;
}

/* AdminLTE specific body classes */
body.hold-transition.sidebar-mini.layout-fixed.layout-navbar-fixed.layout-footer-fixed {
  font-family: 'Poppins', sans-serif !important;
  background-color: #f8f9fa !important;
}

.wrapper {
  display: flex !important;
  width: 100% !important;
  align-items: stretch !important;
  min-height: 100vh !important;
  overflow: hidden !important;
  position: relative !important;
}

/* Navbar Styles */
.navbar {
  background: linear-gradient(90deg, black, #1E3C72) !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  height: 45px !important; /* Reduced height */
  display: flex !important;
  align-items: center !important; /* Center-align items */
}

.navbar .nav-link {
  color: white !important;
  padding: 5px 10px; /* Adjusted padding for better alignment */
  transition: color 0.3s ease, transform 0.3s ease;
  text-decoration: none; /* Remove underline */
}

.navbar .nav-link:hover {
  color: #ffc107 !important;
  text-decoration: none;
}

/* Sidebar Styles */
.sidebar, .main-sidebar {
  background: linear-gradient(90deg, black, #1E3C72) !important;
  color: white !important;
  height: 100vh !important;
  width: 250px !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 0 !important;
  scrollbar-width: thin !important;
  scrollbar-color: #495057 #212529 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  transition: width 0.3s ease !important;
  z-index: 1000 !important;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2) !important;
}

/* Sidebar brand header */
.main-sidebar .brand-link {
  height: 57px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  padding: 0.8rem 0.5rem !important;
}

/* Sidebar nav container */
.main-sidebar .sidebar {
  padding: 10px !important;
  height: calc(100vh - 57px) !important;
  overflow-y: auto !important;
  background: transparent !important;
  position: relative !important;
  width: 100% !important;
}

.sidebar::-webkit-scrollbar {
  width: 6px; /* Smaller scrollbar */
}

.sidebar::-webkit-scrollbar-thumb {
  background-color: #495057;
  border-radius: 4px;
}

/* Nav link styling */
.sidebar .nav-link,
.nav-sidebar .nav-link {
  color: white !important;
  padding: 10px 15px !important;
  border-radius: 5px !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  margin-bottom: 5px !important;
  display: flex !important;
  align-items: center !important;
}

.sidebar .nav-link:hover,
.nav-sidebar .nav-link:hover {
  color: #ffc107 !important;
  background-color: rgba(73, 80, 87, 0.5) !important;
  text-decoration: none !important;
}

.sidebar .nav-link.active,
.nav-sidebar .nav-link.active {
  background-color: rgba(30, 60, 114, 0.7) !important;
  color: #ffc107 !important;
  font-weight: bold !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* Nav link icon styling */
.sidebar .nav-link .nav-icon,
.nav-sidebar .nav-link .nav-icon {
  margin-right: 10px !important;
  width: 1.6rem !important;
  text-align: center !important;
  font-size: 1.1rem !important;
  transition: all 0.3s ease !important;
}

/* Nav link text styling */
.sidebar .nav-link p,
.nav-sidebar .nav-link p {
  margin: 0 !important;
  transition: all 0.3s ease !important;
}

/* Toggled sidebar */
.sidebar.toggled, .main-sidebar.toggled {
  width: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  margin-left: -250px !important;
}

/* Fix for AdminLTE compatibility */
.layout-fixed .main-sidebar {
  bottom: 0 !important;
  float: none !important;
  left: 0 !important;
  position: fixed !important;
  top: 0 !important;
  z-index: 1000 !important;
}

/* Additional AdminLTE compatibility fixes */
.main-header {
  border-bottom: 1px solid #dee2e6 !important;
  z-index: 1034 !important;
}

.main-header.navbar {
  background: linear-gradient(90deg, black, #1E3C72) !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  height: 45px !important;
  display: flex !important;
  align-items: center !important;
}

.main-sidebar .brand-link {
  border-bottom: 1px solid #4b545c !important;
  padding: 0.8rem 0.5rem !important;
  background: linear-gradient(90deg, black, #1E3C72) !important;
  color: white !important;
  text-align: center !important;
  font-weight: bold !important;
  text-decoration: none !important;
  display: block !important;
}

.main-sidebar .brand-link:hover {
  color: #ffc107 !important;
  text-decoration: none !important;
}

.main-footer {
  background: linear-gradient(90deg, black, #1E3C72) !important;
  color: white !important;
  text-align: center !important;
  padding: 10px 0 !important;
  font-size: 14px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 15px !important;
  border-top: 1px solid #dee2e6 !important;
  margin-left: 250px !important;
  width: calc(100% - 250px) !important;
  position: fixed !important;
  bottom: 0 !important;
  z-index: 1000 !important;
}

/* Fix for footer positioning */
.main-footer {
  transition: margin-left 0.3s ease-in-out, width 0.3s ease-in-out !important;
}

/* Footer when sidebar is collapsed */
body.sidebar-collapse .main-footer {
  margin-left: 4.6rem !important;
  width: calc(100% - 4.6rem) !important;
}

/* Footer when sidebar is completely closed */
body.sidebar-collapse.sidebar-closed .main-footer {
  margin-left: 0 !important;
  width: 100% !important;
}

/* Additional sidebar toggle fixes */
.sidebar-mini.sidebar-collapse .main-sidebar .nav-sidebar .nav-link {
  width: 4.6rem !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.sidebar .nav-header {
  color: #ced4da;
  font-weight: lighter;
  margin-top: 0rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 10px;
}

/* Content Styles */
.content-wrapper {
  flex: 1 !important;
  padding: 15px !important;
  background-color: #ffffff !important;
  border-radius: 10px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  margin-left: 250px !important; /* Match sidebar width */
  width: calc(100% - 250px) !important;
  transition: margin-left 0.3s ease !important;
  min-height: calc(100vh - 50px) !important;
}

/* When sidebar is toggled */
body.sidebar-toggled .content-wrapper {
  margin-left: 0 !important;
  width: 100% !important;
}

.card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-header {
  background: linear-gradient(90deg, #4e54c8, #8f94fb);
  color: white;
  border-radius: 10px 10px 0 0;
}

/* Breadcrumb Styles */
.breadcrumb {
  background: transparent;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  list-style: none;
}

.breadcrumb-container {
  background: linear-gradient(to right, #f8f9fa, #ffffff);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 1.5rem;
  border-left: 4px solid #1E3C72;
  position: relative;
  overflow: hidden;
}

.breadcrumb-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(30, 60, 114, 0.03) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.75rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.75rem;
  color: #6c757d;
  content: "/";
  font-weight: 900;
}

.breadcrumb-item a {
  color: #1E3C72;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.breadcrumb-item a:hover {
  color: #2A5298;
  background-color: rgba(30, 60, 114, 0.05);
}

.breadcrumb-item a i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
  color: #1E3C72;
}

.breadcrumb-item.active {
  color: #495057;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
}

.breadcrumb-item.active i {
  margin-right: 0.5rem;
  color: #1E3C72;
}

/* Professional breadcrumb separator */
.breadcrumb-chevron .breadcrumb-item + .breadcrumb-item::before {
  content: "›";
  font-size: 1.2rem;
  line-height: 1;
  color: #1E3C72;
  font-weight: 700;
}

/* Breadcrumb with background */
.breadcrumb-bg {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

/* Footer Styles */
.footer {
  background: linear-gradient(90deg, black, #1E3C72);
  color: white;
  text-align: center;
  padding: 10px 0;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.footer i {
  color: #ffc107;
  transition: transform 0.3s ease;
}

.footer i:hover {
  transform: scale(1.2);
}

/* Brand Link Styles */
.brand-link {
  background: linear-gradient(90deg, black, #1E3C72);
  color: white !important;
  font-weight: bold;
  padding: 10px 15px;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 57px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.brand-link:hover {
  color: #ffc107 !important;
  text-decoration: none;
  background: linear-gradient(90deg, #000000, #16345A);
}

.brand-link .brand-image {
  margin-right: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.brand-link:hover .brand-image {
  transform: scale(1.05);
}

.brand-link .brand-text {
  font-size: 1.1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Loader Styles */
.loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
}

.loader-hidden {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
  display: none !important;
  z-index: -1 !important;
}

.loader {
  position: relative;
  width: 120px;
  height: 120px;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loader::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(30, 60, 114, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  animation: pulseGlow 2s infinite ease-in-out;
}

.school-logo {
  position: relative;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #1E3C72 0%, #2a5298 100%);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  animation: float 3s infinite ease-in-out;
}

.school-logo i {
  color: #ffc107;
  font-size: 40px;
  animation: shine 2s infinite;
}

.loading-text {
  color: #1E3C72;
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 600;
  margin-top: 20px;
  letter-spacing: 3px;
  position: relative;
  overflow: hidden;
}

.loading-text::after {
  content: '...';
  position: absolute;
  animation: dots 1.5s infinite;
  width: 24px;
  text-align: left;
}

.loader-progress {
  width: 200px;
  height: 4px;
  background: rgba(30, 60, 114, 0.2);
  border-radius: 4px;
  margin-top: 20px;
  overflow: hidden;
  position: relative;
}

.loader-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #1E3C72, #ffc107);
  border-radius: 4px;
  animation: progress 2s infinite;
}

.loader-tagline {
  margin-top: 20px;
  color: #6c757d;
  font-size: 14px;
  font-style: italic;
  text-align: center;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes shine {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes dots {
  0% { content: '.'; }
  33% { content: '..'; }
  66% { content: '...'; }
  100% { content: '.'; }
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 100%; }
  100% { width: 0%; }
}

@keyframes pulseGlow {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.1); opacity: 1; }
}
@keyframes tasselSwing {
  0% { transform: rotate(-15deg); }
  50% { transform: rotate(15deg); }
  100% { transform: rotate(-15deg); }
}

@keyframes iconPulse {
  from { transform: scale(1); opacity: 0.8; }
  to { transform: scale(1.2); opacity: 1; }
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 100%; }
  100% { width: 0%; }
}

@keyframes pulseGlow {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.1); opacity: 1; }
}

/* Fix for duplicate loader-hidden class */

/* Dashboard Styles */
.alt-list li:nth-child(odd) {
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 2px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.alt-list li:nth-child(even) {
  background-color: #e9ecef;
  border-radius: 4px;
  margin-bottom: 2px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.alt-list li:hover {
  background-color: #e2e6ea;
  transform: translateX(5px);
}

/* Professional Page Title Styles */
.page-title-section {
  padding: 10px 0 15px 0 !important;
  margin-bottom: 15px !important;
  position: relative !important;
  overflow: hidden !important;
}

.page-title-section::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(135deg, rgba(30, 60, 114, 0.03) 0%, rgba(255, 255, 255, 0) 70%) !important;
  z-index: 0 !important;
}

.page-title-container {
  display: flex !important;
  align-items: center !important;
  position: relative !important;
  z-index: 1 !important;
  animation: fadeInUp 0.5s ease-out !important;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-title-icon {
  width: 60px !important;
  height: 60px !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, #1E3C72 0%, #2a5298 100%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-right: 20px !important;
  box-shadow: 0 8px 16px rgba(30, 60, 114, 0.2) !important;
  position: relative !important;
  overflow: hidden !important;
}

.page-title-icon::after {
  content: '' !important;
  position: absolute !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 80%) !important;
  top: 0 !important;
  left: 0 !important;
}

.page-title-icon i {
  font-size: 24px !important;
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.page-title-content {
  flex: 1 !important;
}

.page-title {
  margin: 0 !important;
  padding: 0 !important;
  font-size: 28px !important;
  font-weight: 700 !important;
  color: #1E3C72 !important;
  letter-spacing: 0.5px !important;
  line-height: 1.2 !important;
  position: relative !important;
  display: inline-block !important;
}

.page-title::after {
  content: '' !important;
  position: absolute !important;
  bottom: -5px !important;
  left: 0 !important;
  width: 40px !important;
  height: 3px !important;
  background: linear-gradient(90deg, #1E3C72, #2a5298) !important;
  border-radius: 3px !important;
}

.page-subtitle {
  margin: 8px 0 0 0 !important;
  padding: 0 !important;
  font-size: 14px !important;
  color: #6c757d !important;
  max-width: 80% !important;
}

.page-actions {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  gap: 10px !important;
}

/* Professional Date Display Styles */
.date-display {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
}

.date-container {
  display: flex !important;
  align-items: center !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-radius: 12px !important;
  padding: 8px 15px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(30, 60, 114, 0.1) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
  animation: datePulse 3s infinite ease-in-out !important;
}

@keyframes datePulse {
  0%, 100% { box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05); transform: translateY(0); }
  50% { box-shadow: 0 6px 12px rgba(30, 60, 114, 0.15); transform: translateY(-2px); }
}

.date-container::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(135deg, rgba(30, 60, 114, 0.05) 0%, rgba(255, 255, 255, 0) 70%) !important;
  z-index: 0 !important;
}

.date-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 36px !important;
  height: 36px !important;
  background: linear-gradient(135deg, #1E3C72 0%, #2a5298 100%) !important;
  border-radius: 50% !important;
  margin-right: 12px !important;
  box-shadow: 0 4px 8px rgba(30, 60, 114, 0.2) !important;
  position: relative !important;
  z-index: 1 !important;
}

.date-icon i {
  color: white !important;
  font-size: 16px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.date-text {
  position: relative !important;
  z-index: 1 !important;
  font-weight: 600 !important;
  color: #1E3C72 !important;
  font-size: 15px !important;
  letter-spacing: 0.5px !important;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8) !important;
}

/* Responsive adjustments for date display */
@media (max-width: 768px) {
  .date-container {
    padding: 6px 12px !important;
  }

  .date-icon {
    width: 30px !important;
    height: 30px !important;
    margin-right: 8px !important;
  }

  .date-icon i {
    font-size: 14px !important;
  }

  .date-text {
    font-size: 13px !important;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .page-title-section {
    padding: 10px 0 15px 0 !important;
  }

  .page-title-icon {
    width: 45px !important;
    height: 45px !important;
    margin-right: 15px !important;
  }

  .page-title-icon i {
    font-size: 18px !important;
  }

  .page-title {
    font-size: 22px !important;
  }

  .page-subtitle {
    font-size: 13px !important;
    max-width: 100% !important;
  }

  .page-actions {
    margin-top: 15px !important;
    justify-content: flex-start !important;
  }

  .container-fluid {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }

  .content-wrapper {
    padding: 10px !important;
  }

  .row {
    margin-left: -5px !important;
    margin-right: -5px !important;
  }

  .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
  .col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
  .col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
  .col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
  .col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
}

/* Enhanced Card Styles */
.card {
  border: none !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
  overflow: hidden !important;
  margin-bottom: 15px !important;
}

.card:hover {
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15) !important;
}

.card-header {
  background: linear-gradient(135deg, #1E3C72 0%, #2a5298 100%) !important;
  color: white !important;
  border-bottom: none !important;
  padding: 15px 20px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
}

.card-body {
  padding: 20px !important;
  background-color: #ffffff !important;
}

/* Dashboard Card Styles */
.dashboard-card {
  border: none;
  border-radius: 12px;
  background: white;
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  padding: 1.5rem !important;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, #1E3C72, #2a5298);
}

.dashboard-card:hover {
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.dashboard-card h5 {
  color: #495057;
  font-weight: 600;
  margin-top: 1rem;
  font-size: 1rem;
}

.dashboard-card h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0.5rem 0;
}

.dashboard-card p {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 0;
}

.dashboard-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
}

.dashboard-card-icon::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  z-index: -1;
  animation: pulse 2s infinite;
}

.dashboard-card-icon i {
  font-size: 24px;
  color: white;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* Chart Container Styles */
.chart-container {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

/* Chart wrapper with fixed height */
.chart-wrapper {
  position: relative;
  height: 300px;
  width: 100%;
  margin-bottom: 0;
  overflow: hidden;
  max-height: 300px;
}

/* Fix for nested chart containers */
.top-performers .chart-wrapper {
  height: 250px;
  max-height: 250px;
}

.chart-container:hover {
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.chart-container h5 {
  color: #1E3C72;
  font-weight: 600;
  font-size: 1.1rem;
}

.chart-actions {
  display: flex;
  gap: 5px;
}

.chart-actions button {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.chart-actions button.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.chart-actions button.active i {
  transform: scale(1.2);
}

.chart-actions .btn-outline-success.active {
  background-color: #28a745;
  border-color: #28a745;
}

/* Recent Activities Styles */
.recent-activities {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.recent-activities:hover {
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.recent-activities h4 {
  color: #1E3C72;
  font-weight: 600;
  font-size: 1.1rem;
}

.recent-activities .table {
  margin-bottom: 0;
}

.recent-activities .table th {
  font-weight: 600;
  color: #495057;
  border-top: none;
  border-bottom: 2px solid #e9ecef;
}

.recent-activities .table td {
  vertical-align: middle;
  padding: 0.75rem;
  border-color: #e9ecef;
}

/* Quick Actions Styles */
.quick-actions {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.quick-actions:hover {
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.quick-actions h4 {
  color: #1E3C72;
  font-weight: 600;
  font-size: 1.1rem;
}

.quick-actions .btn {
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.quick-actions .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.quick-actions .btn i {
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.quick-actions .btn:hover i {
  transform: scale(1.2);
}

/* System Status Styles */
.system-status {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.system-status:hover {
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.system-status h4 {
  color: #1E3C72;
  font-weight: 600;
  font-size: 1.1rem;
}

.system-status .card {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.system-status .card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.system-status .card-title {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  color: #495057;
}

.system-status .card-text {
  font-size: 1rem;
  color: #1E3C72;
}

/* Event Card Styles */
.event-card {
  transition: all 0.3s ease;
}

.event-card:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Custom Color Classes */
.bg-purple {
  background-color: #6f42c1 !important;
}

.text-purple {
  color: #6f42c1 !important;
}

.btn-outline-purple {
  color: #6f42c1;
  border-color: #6f42c1;
}

.btn-outline-purple:hover {
  color: #fff;
  background-color: #6f42c1;
  border-color: #6f42c1;
}

.list-group-item {
  border-left: 5px solid #28a745;
}

.icon-color {
  color: #007bff;
}

/* Badge Styles */
.badge {
  font-weight: 600;
  padding: 0.35em 0.65em;
  border-radius: 0.25rem;
}

/* Class count badge */
.class-count {
  font-size: 14px;
  padding: 6px 10px;
  min-width: 30px;
  display: inline-block;
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #1E3C72, #2A5298) !important;
  border: none !important;
  color: #fff !important;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease-in-out;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #16345A, #1E3C72) !important;
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.btn-success {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
  border: none !important;
  color: #fff !important;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease-in-out;
}

.btn-success:hover {
  background: linear-gradient(135deg, #218838, #1e9e7f) !important;
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.btn-success:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8, #138496) !important;
  border: none !important;
  color: #fff !important;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease-in-out;
}

.btn-info:hover {
  background: linear-gradient(135deg, #138496, #0f6674) !important;
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.btn-info:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Action buttons group styling */
.d-flex.gap-2 {
  display: flex;
  gap: 10px !important;
}

/* Rounded corners and shadow for buttons */
.rounded-3 {
  border-radius: 8px !important;
}

.shadow-sm {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* Enhanced Form Styles */
.form-control, .form-select {
  padding: 0.65rem 1rem;
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.form-control:focus, .form-select:focus {
  border-color: #1E3C72;
  box-shadow: 0 0 0 0.25rem rgba(30, 60, 114, 0.15);
}

.form-control:disabled, .form-select:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
  opacity: 0.7;
}

.form-label {
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1E3C72;
  font-size: 0.95rem;
  display: block;
}

/* Form Group Spacing */
.form-group, .mb-3 {
  margin-bottom: 1.5rem !important;
}

/* Custom Checkbox & Radio */
.form-check-input {
  width: 1.2em;
  height: 1.2em;
  margin-top: 0.15em;
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid #ced4da;
  appearance: none;
  transition: all 0.2s ease-in-out;
}

.form-check-input:checked {
  background-color: #1E3C72;
  border-color: #1E3C72;
}

.form-check-input:focus {
  border-color: #1E3C72;
  box-shadow: 0 0 0 0.25rem rgba(30, 60, 114, 0.15);
}

.form-check-label {
  margin-left: 0.5rem;
  font-size: 0.95rem;
  color: #495057;
}

/* Hide Control Sidebar */
.control-sidebar, .control-sidebar-dark, .control-sidebar-bg {
  display: none !important;
  width: 0 !important;
  visibility: hidden !important;
  opacity: 0 !important;
  right: -300px !important;
  position: absolute !important;
}

/* Remove control sidebar button */
[data-widget="control-sidebar"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Fix for AdminLTE layout */
body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .content-wrapper,
body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-footer,
body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-header {
  margin-right: 0 !important;
  transition: margin-right 0.3s ease-in-out !important;
}

/* Fix wrapper layout */
.wrapper {
  overflow-x: hidden !important;
  position: relative !important;
  min-height: 100vh !important;
  width: 100% !important;
}

/* Custom body class to ensure no control sidebar */
body.no-control-sidebar {
  padding-right: 0 !important;
  margin-right: 0 !important;
  overflow-x: hidden !important;
}

body.no-control-sidebar .wrapper {
  margin-right: 0 !important;
}

/* Content wrapper full width */
.content-wrapper {
  margin-right: 0 !important;
  width: calc(100% - 250px) !important;
  padding-bottom: 60px !important; /* Add padding for footer */
  min-height: calc(100vh - 60px) !important; /* Ensure content takes up full height minus footer */
}

/* Custom container-fluid styles to reduce padding */
.container-fluid {
  padding-left: 10px !important;
  padding-right: 10px !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Improve spacing in forms and tables */
.form-group {
  margin-bottom: 10px !important;
}

.table th, .table td {
  padding: 0.5rem !important;
}

/* Reduce spacing in cards */
.card-body {
  padding: 15px !important;
}

.card-header {
  padding: 10px 15px !important;
}

/* Optimized layout for student pages */
.student-container .container-fluid,
.fees-container .container-fluid {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.student-container .row,
.fees-container .row {
  margin-left: -5px !important;
  margin-right: -5px !important;
}

.student-container .col,
.student-container [class*="col-"],
.fees-container .col,
.fees-container [class*="col-"] {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.student-container .card,
.fees-container .card {
  margin-bottom: 10px !important;
}

.student-container .card-body,
.fees-container .card-body {
  padding: 10px !important;
}

.student-container .form-group,
.fees-container .form-group {
  margin-bottom: 8px !important;
}

/* Staff List Page Styles */
.clickable-row {
  cursor: pointer !important;
  transition: all 0.2s ease-in-out !important;
}

.clickable-row:hover {
  background-color: rgba(30, 60, 114, 0.05) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.staff-avatar {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  background-color: #e9ecef !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #1E3C72 !important;
  font-size: 18px !important;
}

.empty-state {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 30px !important;
  color: #6c757d !important;
}

/* DataTable Customizations */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
  margin-bottom: 15px !important;
}

.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
  margin-top: 15px !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  border-radius: 4px !important;
  margin: 0 2px !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background: linear-gradient(135deg, #1E3C72, #2A5298) !important;
  border-color: #1E3C72 !important;
  color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: #e9ecef !important;
  border-color: #dee2e6 !important;
}

/* Custom Badge Styles */
.badge {
  padding: 0.4em 0.6em !important;
  font-weight: 500 !important;
  letter-spacing: 0.5px !important;
  border-radius: 4px !important;
}

.badge.bg-success {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.badge.bg-secondary {
  background: linear-gradient(135deg, #6c757d, #495057) !important;
  color: white !important;
}

.badge.bg-info {
  background: linear-gradient(135deg, #17a2b8, #0dcaf0) !important;
}

.badge.bg-danger {
  background: linear-gradient(135deg, #dc3545, #f44336) !important;
}

.badge.bg-primary {
  background: linear-gradient(135deg, #1E3C72, #2A5298) !important;
}

/* Staff Profile Page Styles */
.bg-gradient-primary {
  background: linear-gradient(135deg, #1E3C72, #2A5298) !important;
}

/* Profile Header Styles */
.profile-header-cover {
  background: linear-gradient(135deg, #1E3C72, #2A5298) !important;
  position: relative !important;
}

.profile-header-img {
  margin-top: -50px !important;
  position: relative !important;
  z-index: 10 !important;
}

.profile-header-img img {
  width: 100px !important;
  height: 100px !important;
  object-fit: cover !important;
}

.profile-header-content {
  padding-bottom: 20px !important;
}

.profile-header-tab {
  background-color: transparent !important;
  margin-bottom: -20px !important;
}

.profile-header-tab .nav-tabs {
  border-bottom: none !important;
}

.profile-header-tab .nav-link {
  color: #495057 !important;
  border: none !important;
  padding: 0.75rem 1.25rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  background-color: transparent !important;
}

.profile-header-tab .nav-link:hover {
  color: #1E3C72 !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: transparent !important;
}

.profile-header-tab .nav-link.active {
  color: #1E3C72 !important;
  background-color: white !important;
  border-radius: 0.25rem 0.25rem 0 0 !important;
  font-weight: 600 !important;
}

/* Contact Icon Styles */
.contact-icon {
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Profile Edit Button */
.profile-edit-button .btn {
  padding: 0.5rem 1.25rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  border: none !important;
}

.profile-edit-button .btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.profile-edit-button .btn-warning {
  background: linear-gradient(135deg, #f39c12, #e67e22) !important;
}

/* Action Buttons in Header */
.action-buttons {
  display: flex !important;
  align-items: center !important;
  white-space: nowrap !important;
}

.action-btn {
  padding: 0.5rem 1rem !important;
  margin-right: 0.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  border-radius: 0.25rem !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: auto !important;
}

.action-btn i {
  margin-right: 0.5rem !important;
}

.action-btn.btn-warning {
  background: linear-gradient(135deg, #f39c12, #e67e22) !important;
  border: none !important;
}

.action-btn.btn-primary {
  background: linear-gradient(135deg, #1E3C72, #2A5298) !important;
  border: none !important;
}

.action-btn.btn-outline-secondary {
  border: 1px solid #ced4da !important;
  background: white !important;
}

.action-btn.btn-outline-secondary:hover {
  background: #f8f9fa !important;
}

/* Dropdown Menu Styles */
.dropdown-menu {
  min-width: 200px !important;
  padding: 0.5rem 0 !important;
  margin: 0.125rem 0 0 !important;
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
  border-radius: 0.25rem !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  z-index: 1000 !important;
}

.dropdown-menu-end {
  right: 0 !important;
  left: auto !important;
}

.dropdown-item {
  padding: 0.5rem 1rem !important;
  clear: both !important;
  font-weight: 400 !important;
  color: #212529 !important;
  text-align: inherit !important;
  white-space: nowrap !important;
  background-color: transparent !important;
  border: 0 !important;
}

.dropdown-item:hover, .dropdown-item:focus {
  color: #16181b !important;
  text-decoration: none !important;
  background-color: #f8f9fa !important;
}

.dropdown-divider {
  height: 0 !important;
  margin: 0.5rem 0 !important;
  overflow: hidden !important;
  border-top: 1px solid #e9ecef !important;
}

.info-group label {
  color: #6c757d !important;
  font-size: 0.8rem !important;
  margin-bottom: 0.2rem !important;
}

.info-group p {
  font-size: 1rem !important;
  color: #212529 !important;
}

.fw-medium {
  font-weight: 500 !important;
}

/* Timeline Styles */
.timeline {
  position: relative !important;
  padding-left: 30px !important;
}

.timeline-item {
  position: relative !important;
  padding-bottom: 25px !important;
}

.timeline-item:last-child {
  padding-bottom: 0 !important;
}

.timeline-marker {
  position: absolute !important;
  left: -30px !important;
  top: 0 !important;
  width: 15px !important;
  height: 15px !important;
  border-radius: 50% !important;
  background-color: #1E3C72 !important;
  z-index: 2 !important;
}

.timeline-item:not(:last-child):before {
  content: '' !important;
  position: absolute !important;
  left: -23px !important;
  top: 15px !important;
  height: calc(100% - 15px) !important;
  width: 2px !important;
  background-color: #dee2e6 !important;
}

.timeline-content {
  background-color: #f8f9fa !important;
  padding: 15px !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* Tab Styles */
.nav-tabs .nav-link {
  color: white !important;
  border: none !important;
  padding: 0.75rem 1.25rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.nav-tabs .nav-link:hover {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-color: transparent !important;
}

.nav-tabs .nav-link.active {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-bottom: 2px solid #ffffff !important;
  font-weight: 600 !important;
}

.tab-content {
  padding-top: 1.5rem !important;
  background-color: white !important;
  border-radius: 0 0 0.25rem 0.25rem !important;
}

/* Print Modal Styles */
.modal-header.bg-primary {
  background: linear-gradient(135deg, #1E3C72, #2A5298) !important;
}

#printPreviewContent {
  max-height: 70vh !important;
  overflow-y: auto !important;
}

/* When sidebar is collapsed */
body.sidebar-collapse .content-wrapper {
  margin-left: 4.6rem !important;
  width: calc(100% - 4.6rem) !important;
}

/* When sidebar is completely collapsed */
body.sidebar-collapse.sidebar-closed .content-wrapper {
  width: 100% !important;
  margin-left: 0 !important;
}

/* Fix for AdminLTE sidebar mini state */
.sidebar-mini.sidebar-collapse .main-sidebar,
.sidebar-mini.sidebar-collapse .main-sidebar::before {
  margin-left: 0 !important;
  width: 4.6rem !important;
  overflow: visible !important;
}

.sidebar-mini.sidebar-collapse .content-wrapper,
.sidebar-mini.sidebar-collapse .main-footer,
.sidebar-mini.sidebar-collapse .main-header {
  margin-left: 4.6rem !important;
}

/* Fix for sidebar mini nav links */
.sidebar-mini.sidebar-collapse .main-sidebar .nav-sidebar .nav-link {
  width: 4.6rem !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  text-align: center !important;
  display: block !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar .nav-sidebar .nav-link .nav-icon {
  margin-right: 0 !important;
  margin-left: 0 !important;
  width: 2.5rem !important;
  font-size: 1.2rem !important;
  text-align: center !important;
  display: block !important;
  margin: 0 auto !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar .nav-sidebar .nav-link p {
  width: 0 !important;
  height: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  opacity: 0 !important;
  display: none !important;
  visibility: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Fix for sidebar mini hover state */
.sidebar-mini.sidebar-collapse .main-sidebar:hover {
  width: 250px !important;
  z-index: 1038 !important;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3) !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar:hover .nav-sidebar .nav-link {
  width: 100% !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  text-align: left !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar:hover .nav-sidebar .nav-link .nav-icon {
  margin-right: 0.5rem !important;
  width: 1.6rem !important;
  text-align: center !important;
  display: inline-block !important;
  margin: 0 !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar:hover .nav-sidebar .nav-link p {
  width: auto !important;
  height: auto !important;
  opacity: 1 !important;
  display: inline-block !important;
  visibility: visible !important;
  margin-left: 0.5rem !important;
  padding: 0 !important;
}

/* Fix for sidebar toggle button */
.nav-link[data-widget="pushmenu"] {
  transition: all 0.3s ease !important;
}

.nav-link[data-widget="pushmenu"]:hover {
  color: #ffc107 !important;
}

/* Enhanced User Account Dropdown Styles */
.user-menu {
  position: relative !important;
}

.user-dropdown-toggle {
  display: flex !important;
  align-items: center !important;
  padding: 0.5rem 1rem !important;
  transition: all 0.3s ease-in-out !important;
  border-radius: 50px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.user-dropdown-toggle:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.user-dropdown-toggle:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

.user-avatar-container {
  position: relative !important;
  margin-right: 10px !important;
}

.user-avatar {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #1E3C72, #2a5298) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

.user-dropdown-toggle:hover .user-avatar {
  transform: scale(1.05) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
}

.user-avatar i {
  color: white !important;
  font-size: 18px !important;
  transition: all 0.3s ease !important;
}

.user-dropdown-toggle:hover .user-avatar i {
  transform: scale(1.1) !important;
}

.user-status-indicator {
  position: absolute !important;
  bottom: 0 !important;
  right: 0 !important;
  width: 10px !important;
  height: 10px !important;
  border-radius: 50% !important;
  border: 2px solid #1E3C72 !important;
  z-index: 2 !important;
}

.user-status-indicator.online {
  background-color: #28a745 !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 5px #28a745 !important;
  animation: pulse-green 2s infinite !important;
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

.user-info {
  display: flex !important;
  flex-direction: column !important;
  line-height: 1.2 !important;
  margin-right: 5px !important;
}

.user-name {
  color: white !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease !important;
}

.user-dropdown-toggle:hover .user-name {
  color: #ffc107 !important;
}

.user-role {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 12px !important;
  font-style: italic !important;
  transition: all 0.3s ease !important;
}

.user-dropdown-toggle:hover .user-role {
  color: rgba(255, 255, 255, 1) !important;
}

.user-dropdown-icon {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 12px !important;
  margin-left: 5px !important;
  transition: all 0.3s ease !important;
}

.user-dropdown-toggle:hover .user-dropdown-icon {
  color: #ffc107 !important;
  transform: translateY(2px) !important;
}

.user-dropdown-menu {
  min-width: 280px !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25) !important;
  overflow: hidden !important;
  margin-top: 15px !important;
  animation: dropdown-fade-in 0.3s ease-out !important;
  transform-origin: top right !important;
  display: none !important;
  position: absolute !important;
  z-index: 1000 !important;
  right: 0 !important; /* Ensure dropdown appears on the right side of the toggle button */
  left: auto !important; /* Override any left positioning */
}

.user-dropdown-menu.show {
  display: block !important;
}

/* Fix for user menu dropdown positioning */
.user-menu {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
}

/* Ensure the dropdown container has proper positioning */
.dropdown {
  position: relative !important;
}

@keyframes dropdown-fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.user-header {
  position: relative !important;
  color: white !important;
  padding: 0 !important;
  height: 120px !important;
  overflow: hidden !important;
}

.user-header-bg {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(135deg, #1E3C72, #2a5298) !important;
  z-index: 1 !important;
  overflow: hidden !important;
}

.user-header-bg::before {
  content: '' !important;
  position: absolute !important;
  top: -50% !important;
  left: -50% !important;
  width: 200% !important;
  height: 200% !important;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%) !important;
  animation: rotate 20s linear infinite !important;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.user-header-content {
  position: relative !important;
  z-index: 2 !important;
  display: flex !important;
  align-items: center !important;
  padding: 20px !important;
  height: 100% !important;
}

.user-avatar-lg {
  position: relative !important;
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.2) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-right: 15px !important;
  border: 3px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
}

.user-avatar-lg i {
  color: white !important;
  font-size: 28px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.user-status-indicator-lg {
  position: absolute !important;
  bottom: 0 !important;
  right: 0 !important;
  width: 14px !important;
  height: 14px !important;
  border-radius: 50% !important;
  border: 2px solid #1E3C72 !important;
  z-index: 2 !important;
}

.user-status-indicator-lg.online {
  background-color: #28a745 !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 5px #28a745 !important;
  animation: pulse-green 2s infinite !important;
}

.user-details {
  flex: 1 !important;
}

.user-name-lg {
  margin: 0 !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  letter-spacing: 0.5px !important;
}

.user-role-lg {
  margin: 5px 0 0 0 !important;
  font-size: 13px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-style: italic !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.user-menu-items {
  padding: 10px 0 !important;
}

.dropdown-item {
  padding: 12px 20px !important;
  display: flex !important;
  align-items: center !important;
  transition: all 0.3s ease !important;
  border-left: 3px solid transparent !important;
}

.dropdown-item-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  background: rgba(30, 60, 114, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-right: 15px !important;
  transition: all 0.3s ease !important;
}

.dropdown-item-icon i {
  color: #1E3C72 !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
}

.dropdown-item-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.dropdown-item-title {
  font-weight: 600 !important;
  color: #1E3C72 !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}

.dropdown-item-description {
  font-size: 12px !important;
  color: #6c757d !important;
  margin-top: 2px !important;
  transition: all 0.3s ease !important;
}

.dropdown-item:hover {
  background-color: rgba(30, 60, 114, 0.05) !important;
  color: #1E3C72 !important;
  border-left-color: #1E3C72 !important;
}

.dropdown-item:hover .dropdown-item-icon {
  background: rgba(30, 60, 114, 0.2) !important;
  transform: scale(1.05) !important;
}

.dropdown-item:hover .dropdown-item-icon i {
  transform: scale(1.2) !important;
  color: #1E3C72 !important;
}

.dropdown-item:hover .dropdown-item-title {
  color: #1E3C72 !important;
}

.dropdown-item:hover .dropdown-item-description {
  color: #495057 !important;
}

.dropdown-divider {
  margin: 5px 0 !important;
  border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Responsive adjustments for user menu */
@media (max-width: 768px) {
  .user-dropdown-toggle {
    padding: 0.5rem !important;
  }

  .user-info, .user-dropdown-icon {
    display: none !important;
  }

  .user-avatar-container {
    margin-right: 0 !important;
  }

  .user-dropdown-menu {
    min-width: 260px !important;
    right: 0 !important;
    left: auto !important;
    position: absolute !important;
    transform: none !important;
    top: 100% !important;
    margin-top: 0.5rem !important;
  }

  /* Position dropdown relative to navbar on mobile */
  .position-static {
    position: static !important;
  }

  /* But keep it relative on larger screens */
  @media (min-width: 768px) {
    .position-md-relative {
      position: relative !important;
    }
  }
}

.form-select:focus {
  border-color: #1E3C72;
  box-shadow: 0 0 0 0.25rem rgba(30, 60, 114, 0.25);
}

.form-select:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #495057;
}
