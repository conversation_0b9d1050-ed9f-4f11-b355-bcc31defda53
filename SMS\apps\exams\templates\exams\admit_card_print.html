<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admit Card - {% if admit_cards.0 %}{{ admit_cards.0.student.fullname }}{% else %}{{ admit_card.student.fullname }}{% endif %}</title>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f8f9fa;
      color: #333;
    }

    .admit-card {
      background-color: #fff;
      border: 1px solid #1E3C72;
      margin: 20px auto;
      max-width: 800px;
      box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
      position: relative;
      overflow: hidden;
    }

    .admit-card::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 150px;
      height: 150px;
      background: linear-gradient(135deg, rgba(30, 60, 114, 0.05), rgba(42, 82, 152, 0.1));
      border-radius: 0 0 0 150px;
      z-index: 0;
    }

    .admit-card-header {
      text-align: center;
      padding: 20px 15px;
      border-bottom: 2px solid #1E3C72;
      background: linear-gradient(135deg, rgba(30, 60, 114, 0.05), rgba(42, 82, 152, 0.1));
      position: relative;
      z-index: 1;
    }

    .school-logo {
      max-height: 80px;
      margin-bottom: 10px;
    }

    .school-name {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 5px;
      color: #1E3C72;
    }

    .school-address {
      font-size: 14px;
      margin-bottom: 5px;
      color: #555;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      text-transform: uppercase;
      background-color: #1E3C72;
      color: #fff;
      padding: 8px;
      border-radius: 4px;
      margin-top: 10px;
      letter-spacing: 1px;
    }

    .student-photo {
      border: 2px solid #1E3C72;
      height: 150px;
      width: 120px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;
      overflow: hidden;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .student-info {
      margin-top: 15px;
      position: relative;
      z-index: 1;
    }

    .info-row {
      display: flex;
      margin-bottom: 10px;
      border-bottom: 1px dashed #e9ecef;
      padding-bottom: 5px;
    }

    .info-label {
      font-weight: 600;
      width: 120px;
      color: #1E3C72;
    }

    .info-value {
      flex: 1;
      font-weight: 500;
    }

    .exam-schedule {
      margin-top: 20px;
      position: relative;
      z-index: 1;
    }

    .exam-schedule h5 {
      color: #1E3C72;
      font-weight: 600;
      border-left: 4px solid #1E3C72;
      padding-left: 10px;
      margin-bottom: 15px;
    }

    .signature-section {
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
      padding-top: 10px;
      position: relative;
      z-index: 1;
    }

    .signature-box {
      text-align: center;
      width: 150px;
    }

    .signature-line {
      border-top: 1px solid #1E3C72;
      margin-top: 40px;
      padding-top: 5px;
      font-weight: 500;
      color: #1E3C72;
    }

    .instructions {
      font-size: 12px;
      margin-top: 20px;
      padding: 15px;
      border-radius: 4px;
      background: linear-gradient(135deg, rgba(30, 60, 114, 0.05), rgba(42, 82, 152, 0.1));
      position: relative;
      z-index: 1;
    }

    .instructions h6 {
      color: #1E3C72;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .instructions ol {
      padding-left: 20px;
      margin-bottom: 0;
    }

    .instructions li {
      margin-bottom: 5px;
    }

    .barcode {
      text-align: center;
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
      background-color: #f8f9fa;
      border: 1px dashed #1E3C72;
      position: relative;
      z-index: 1;
    }

    .footer {
      text-align: center;
      font-size: 12px;
      margin-top: 20px;
      padding: 10px;
      border-top: 1px solid #e9ecef;
      color: #6c757d;
      position: relative;
      z-index: 1;
    }

    .watermark {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-45deg);
      font-size: 100px;
      color: rgba(30, 60, 114, 0.03);
      font-weight: 700;
      white-space: nowrap;
      pointer-events: none;
      z-index: 0;
    }

    @media print {
      body {
        background-color: #fff;
      }

      .admit-card {
        box-shadow: none;
        margin: 0;
        border: 1px solid #1E3C72;
        page-break-inside: avoid;
      }

      .no-print {
        display: none !important;
      }

      .page-break {
        page-break-after: always;
      }

      table.table-bordered {
        border-color: #1E3C72 !important;
      }

      table.table-bordered th,
      table.table-bordered td {
        border-color: #1E3C72 !important;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Print Controls - Only visible on screen -->
    <div class="no-print bg-light p-4 mb-4 rounded shadow-sm">
      <div class="d-flex justify-content-between align-items-center">
        <h4 class="mb-0 fw-bold text-primary"><i class="fas fa-id-card me-2"></i>Admit Card Preview</h4>
        <div>
          <button onclick="window.print();" class="btn btn-primary">
            <i class="fas fa-print me-1"></i> Print
          </button>
          <a href="{% url 'exams:admit_card_list' %}" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-left me-1"></i> Back to List
          </a>
          <button onclick="downloadPDF();" class="btn btn-success ms-2">
            <i class="fas fa-download me-1"></i> Download PDF
          </button>
        </div>
      </div>
    </div>

    <!-- Admit Card -->
    {% for card in admit_cards %}
    <div class="admit-card p-4">
      <!-- Watermark -->
      <div class="watermark">ADMIT CARD</div>

      <div class="admit-card-header">
        {% if profile.college_logo %}
        <img src="{{ profile.college_logo.url }}" alt="School Logo" class="school-logo">
        {% else %}
        <img src="/static/dist/img/logo.png" alt="School Logo" class="school-logo">
        {% endif %}
        <div class="school-name">{{ profile.college_name|default:"SCHOOL NAME" }}</div>
        <div class="school-address">{{ profile.college_address|default:"123 School Address, City, State, ZIP" }}</div>
        <div class="card-title">Examination Admit Card</div>
      </div>

      <div class="row mt-4">
        <div class="col-md-8">
          <div class="student-info">
            <div class="info-row">
              <div class="info-label">Name:</div>
              <div class="info-value">{{ card.student.fullname }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Roll Number:</div>
              <div class="info-value">{{ card.roll_number }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Class:</div>
              <div class="info-value">{{ card.student.current_class.name }} {{ card.student.current_section|default:"" }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Exam:</div>
              <div class="info-value">{{ card.exam.name }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Session:</div>
              <div class="info-value">{{ card.exam.session.name }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Term:</div>
              <div class="info-value">{{ card.exam.term.name }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Valid Until:</div>
              <div class="info-value">{{ card.exam.end_date|date:"d M, Y" }}</div>
            </div>
          </div>
        </div>
        <div class="col-md-4 text-center">
          <div class="student-photo">
            {% if card.student.passport %}
            <img src="{{ card.student.passport.url }}" alt="Student Photo" style="max-width: 100%; max-height: 100%;">
            {% else %}
            <div class="d-flex flex-column align-items-center justify-content-center h-100">
              <i class="fas fa-user-circle fa-4x text-muted mb-2"></i>
              <span class="text-muted small">Photo Not Available</span>
            </div>
            {% endif %}
          </div>
        </div>
      </div>

      <div class="exam-schedule mt-4">
        <h5>Examination Schedule</h5>
        <table class="table table-bordered table-sm">
          <thead class="table-light">
            <tr>
              <th width="20%">Date</th>
              <th width="30%">Subject</th>
              <th width="25%">Time</th>
              <th width="25%">Venue</th>
            </tr>
          </thead>
          <tbody>
            {% for schedule in exam_schedules %}
            <tr>
              <td>{{ schedule.date|date:"d M, Y" }}</td>
              <td>{{ schedule.subject.name }}</td>
              <td>{{ schedule.start_time|time:"h:i A" }} - {{ schedule.end_time|time:"h:i A" }}</td>
              <td>{{ schedule.venue|default:"TBA" }}</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="4" class="text-center py-3">
                <i class="fas fa-calendar-times text-muted me-2"></i> No schedules available
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <div class="instructions">
        <h6><i class="fas fa-exclamation-circle me-2"></i>Instructions:</h6>
        <ol>
          <li>Candidates must bring this admit card to the examination hall.</li>
          <li>Candidates should reach the examination center at least 30 minutes before the start of the examination.</li>
          <li>No electronic devices are allowed in the examination hall.</li>
          <li>Candidates must follow all instructions given by the invigilators.</li>
          <li>Any form of malpractice will result in disqualification.</li>
        </ol>
      </div>

      <div class="signature-section">
        <div class="signature-box">
          <div class="signature-line">Student's Signature</div>
        </div>
        <div class="signature-box">
          <div class="signature-line">Class Teacher</div>
        </div>
        <div class="signature-box">
          <div class="signature-line">Principal</div>
        </div>
      </div>

      <div class="barcode">
        <i class="fas fa-barcode fa-2x me-2 text-primary"></i>
        <div class="fw-bold">{{ card.roll_number }}</div>
        <div class="small text-muted mt-1">Admit Card ID: {{ card.id }}</div>
      </div>

      <div class="footer">
        <div>This admit card is electronically generated and does not require a seal.</div>
        <div>Generated on: {{ card.generated_on|date:"d M, Y" }}</div>
      </div>
    </div>

    {% if not forloop.last %}
    <div class="page-break"></div>
    {% endif %}
    {% endfor %}
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

  <!-- html2pdf.js for PDF download -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

  <script>
    // Auto-print when page loads
    window.onload = function() {
      // Uncomment to automatically print
      // window.print();
    };

    // Function to download as PDF
    function downloadPDF() {
      // Hide the print controls
      const printControls = document.querySelector('.no-print');
      printControls.style.display = 'none';

      // Get the admit card element
      const element = document.querySelector('.admit-card');

      // Configure html2pdf options
      const opt = {
        margin: [0, 0, 0, 0],
        filename: 'admit-card-{% if admit_cards.0 %}{{ admit_cards.0.student.fullname }}{% else %}{{ admit_card.student.fullname }}{% endif %}.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2, useCORS: true },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
      };

      // Generate PDF
      html2pdf().set(opt).from(element).save().then(function() {
        // Show the print controls again
        printControls.style.display = 'block';
      });
    }
  </script>
</body>
</html>
