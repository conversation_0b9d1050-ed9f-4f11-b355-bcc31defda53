# Generated by Django 5.1.6 on 2025-03-05 05:42

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('students', '0012_alter_student_unique_id'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='student',
            name='unique_id',
        ),
        migrations.AddField(
            model_name='student',
            name='registration_number',
            field=models.CharField(default=1, max_length=200, unique=True),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='student',
            name='Father_mobile_number',
            field=models.CharField(default='0000000000', max_length=15, validators=[django.core.validators.RegexValidator(message="Entered mobile number isn't in a right format!", regex='^[0-9]{10,15}$')]),
        ),
        migrations.AlterField(
            model_name='student',
            name='mobile_number',
            field=models.CharField(blank=True, max_length=13, validators=[django.core.validators.RegexValidator(message="Entered mobile number isn't in a right format!", regex='^[0-9]{10,15}$')]),
        ),
    ]
