{% extends 'base.html' %}
{% load static %}

{% block title %}{{ exam.name }} Documents{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:exam_documents' %}">Exam Documents</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ exam.name }}</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-md-12 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-warning text-white d-flex justify-content-between align-items-center">
          <h4 class="mb-0">{{ exam.name }} Documents</h4>
          <div>
            <a href="{% url 'documents:document_upload' %}?entity_type=exam&exam={{ exam.id }}" class="btn btn-light">
              <i class="fas fa-upload me-2"></i> Upload Document
            </a>
          </div>
        </div>
        <div class="card-body">
          <!-- Exam Information -->
          <div class="row mb-4">
            <div class="col-md-12">
              <div class="card bg-light">
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <h5>{{ exam.name }}</h5>
                      <p><strong>Exam Type:</strong> {{ exam.exam_type.name }}</p>
                      <p><strong>Session / Term:</strong> {{ exam.session }} / {{ exam.term }}</p>
                    </div>
                    <div class="col-md-6">
                      <p><strong>Date:</strong> {{ exam.start_date|date:"d M, Y" }} - {{ exam.end_date|date:"d M, Y" }}</p>
                      <p><strong>Status:</strong> 
                        {% if exam.status == 'pending' %}
                        <span class="badge bg-warning">Pending</span>
                        {% elif exam.status == 'ongoing' %}
                        <span class="badge bg-primary">Ongoing</span>
                        {% elif exam.status == 'completed' %}
                        <span class="badge bg-success">Completed</span>
                        {% else %}
                        <span class="badge bg-danger">Cancelled</span>
                        {% endif %}
                      </p>
                      {% if exam.description %}
                      <p><strong>Description:</strong> {{ exam.description }}</p>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Documents Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>S/N</th>
                  <th>Document Title</th>
                  <th>Document Type</th>
                  <th>Status</th>
                  <th>Upload Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for doc in documents %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ doc.document.title }}</td>
                  <td>{{ doc.document.document_type.name }}</td>
                  <td>
                    {% if doc.document.status == 'approved' %}
                    <span class="badge bg-success">Approved</span>
                    {% elif doc.document.status == 'pending' %}
                    <span class="badge bg-warning">Pending</span>
                    {% elif doc.document.status == 'rejected' %}
                    <span class="badge bg-danger">Rejected</span>
                    {% elif doc.document.status == 'expired' %}
                    <span class="badge bg-secondary">Expired</span>
                    {% else %}
                    <span class="badge bg-info">Draft</span>
                    {% endif %}
                  </td>
                  <td>{{ doc.document.created_at|date:"d M, Y" }}</td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="{% url 'documents:document_detail' doc.document.id %}" class="btn btn-sm btn-outline-primary" title="View">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="{% url 'documents:document_download' doc.document.id %}" class="btn btn-sm btn-outline-success" title="Download">
                        <i class="fas fa-download"></i>
                      </a>
                      <a href="{% url 'documents:document_update' doc.document.id %}" class="btn btn-sm btn-outline-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>
                      <a href="{% url 'documents:document_delete' doc.document.id %}" class="btn btn-sm btn-outline-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="6" class="text-center">No documents found for this exam</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          
          <div class="mt-4">
            <a href="{% url 'documents:exam_documents' %}" class="btn btn-outline-secondary">
              <i class="fas fa-arrow-left me-2"></i> Back to Exams
            </a>
            <a href="{% url 'exams:exam_detail' exam.id %}" class="btn btn-outline-warning">
              <i class="fas fa-clipboard-list me-2"></i> View Exam Details
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block extrascripts %}
<script>
  $(document).ready(function() {
    // Any exam document-specific JavaScript can go here
  });
</script>
{% endblock extrascripts %}
