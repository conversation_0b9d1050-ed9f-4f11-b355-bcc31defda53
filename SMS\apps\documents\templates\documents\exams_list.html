{% extends 'base.html' %}
{% load static %}

{% block title %}Exam Documents{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item active" aria-current="page">Exam Documents</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card shadow-sm">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="card-title">Exam Documents</h2>
            <a href="{% url 'documents:document_upload' %}" class="btn btn-primary">
              <i class="fas fa-upload me-2"></i> Upload Document
            </a>
          </div>
          
          <!-- Search and Filter -->
          <div class="row mb-4">
            <div class="col-md-12">
              <div class="card bg-light">
                <div class="card-body">
                  <form method="get" class="row g-3">
                    <div class="col-md-4">
                      <input type="text" class="form-control" name="search" placeholder="Search by exam name" value="{{ request.GET.search|default:'' }}">
                    </div>
                    <div class="col-md-3">
                      <select class="form-select" name="session">
                        <option value="">All Sessions</option>
                        <!-- Add session options here -->
                      </select>
                    </div>
                    <div class="col-md-3">
                      <select class="form-select" name="status">
                        <option value="">All Status</option>
                        <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>Pending</option>
                        <option value="ongoing" {% if request.GET.status == 'ongoing' %}selected{% endif %}>Ongoing</option>
                        <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>Completed</option>
                        <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                      </select>
                    </div>
                    <div class="col-md-2">
                      <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i> Filter
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Exams Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>S/N</th>
                  <th>Exam Name</th>
                  <th>Session / Term</th>
                  <th>Date</th>
                  <th>Status</th>
                  <th>Documents</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for item in exams_with_docs %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ item.exam.name }}</td>
                  <td>{{ item.exam.session }} / {{ item.exam.term }}</td>
                  <td>{{ item.exam.start_date|date:"d M, Y" }} - {{ item.exam.end_date|date:"d M, Y" }}</td>
                  <td>
                    {% if item.exam.status == 'pending' %}
                    <span class="badge bg-warning">Pending</span>
                    {% elif item.exam.status == 'ongoing' %}
                    <span class="badge bg-primary">Ongoing</span>
                    {% elif item.exam.status == 'completed' %}
                    <span class="badge bg-success">Completed</span>
                    {% else %}
                    <span class="badge bg-danger">Cancelled</span>
                    {% endif %}
                  </td>
                  <td>
                    <span class="badge bg-primary">{{ item.document_count }}</span>
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="{% url 'documents:exam_documents_detail' item.exam.id %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-folder-open me-1"></i> View Documents
                      </a>
                      <a href="{% url 'exams:exam_detail' item.exam.id %}" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-clipboard-list me-1"></i> Exam Details
                      </a>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="7" class="text-center">No exams found</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block extrascripts %}
<script>
  $(document).ready(function() {
    // Any exam documents-specific JavaScript can go here
  });
</script>
{% endblock extrascripts %}
