{% extends 'base.html' %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="#" class="text-decoration-none fw-bold">
          <i class="fas fa-clipboard-list"></i> Results
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-plus-circle"></i> Create Results
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-clipboard-list{% endblock title-icon %}

{% block title %}Create Results{% endblock title %}

{% block subtitle %}Select students to create or update their academic results{% endblock subtitle %}


{% block content-header %}
  <form method="POST">
    {% csrf_token %}
  <div class="card-header">
    <h3 class="card-title">
      <input type="submit" class="btn btn-success" value="Proceed >">
    </h3>

    <div class="card-tools">
      <p class="small btn btn-tool">You can also search by name/class by typing in the search box</p>
    </div>
  </div>
{% endblock content-header %}

{% block content %}

  <table id="studenttable" class="table table-sm table-bordered" data-page-length='100'>
    <thead class="thead-light">
      <tr>
        <th><input type="checkbox" id="selecter"></th>
        <th>Name</th>
        <th>Current Class</th>
      </tr>
    </thead>
    <tbody>
      {% for student in students %}
        <tr>
          <td><input type="checkbox" id="{{ student.id }}" name="students" value="{{ student.id}}"></td>
          <td>{{student}} </td>
          <td>{{student.current_class}}</td>
        </tr>
      {% endfor %}
    </tbody>
  </table>

  <input type="submit" class="btn btn-success mt-2" value="Proceed >">


</form>
{% endblock content %}


{% block morejs %}
<script>
  $('#studenttable').DataTable({
    "ordering": false
  });

  $("#selecter").change(function () {
    if (this.checked) {
      $('input[name="students"]').prop('checked', true);
    }
    else {
      $('input[name="students"]').prop('checked', false);
    }
  });
</script>

{% endblock morejs %}
