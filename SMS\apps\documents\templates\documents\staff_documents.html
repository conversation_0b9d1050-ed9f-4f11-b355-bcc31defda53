{% extends 'base.html' %}
{% load static %}

{% block title %}{{ staff.fullname }}'s Documents{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:staff_documents' %}">Staff Documents</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ staff.fullname }}</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-md-12 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
          <h4 class="mb-0">{{ staff.fullname }}'s Documents</h4>
          <div>
            <a href="{% url 'documents:document_upload' %}?entity_type=staff&staff={{ staff.id }}" class="btn btn-light">
              <i class="fas fa-upload me-2"></i> Upload Document
            </a>
          </div>
        </div>
        <div class="card-body">
          <!-- Staff Information -->
          <div class="row mb-4">
            <div class="col-md-12">
              <div class="card bg-light">
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-2 text-center">
                      {% if staff.passport %}
                      <img src="{{ staff.passport.url }}" alt="{{ staff.fullname }}" class="img-fluid rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                      {% else %}
                      <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white" style="width: 100px; height: 100px; font-size: 2.5rem;">
                        {{ staff.fullname|first }}
                      </div>
                      {% endif %}
                    </div>
                    <div class="col-md-5">
                      <h5>{{ staff.fullname }}</h5>
                      <p><strong>Registration Number:</strong> {{ staff.registration_number }}</p>
                      <p><strong>Subject Specification:</strong> {{ staff.Subject_specification }}</p>
                    </div>
                    <div class="col-md-5">
                      <p><strong>Gender:</strong> {{ staff.get_gender_display }}</p>
                      <p><strong>Status:</strong> {{ staff.get_current_status_display }}</p>
                      <p><strong>Registration Date:</strong> {{ staff.date_of_registration|date:"d M, Y" }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Documents Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>S/N</th>
                  <th>Document Title</th>
                  <th>Document Type</th>
                  <th>Status</th>
                  <th>Upload Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for doc in documents %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ doc.document.title }}</td>
                  <td>{{ doc.document.document_type.name }}</td>
                  <td>
                    {% if doc.document.status == 'approved' %}
                    <span class="badge bg-success">Approved</span>
                    {% elif doc.document.status == 'pending' %}
                    <span class="badge bg-warning">Pending</span>
                    {% elif doc.document.status == 'rejected' %}
                    <span class="badge bg-danger">Rejected</span>
                    {% elif doc.document.status == 'expired' %}
                    <span class="badge bg-secondary">Expired</span>
                    {% else %}
                    <span class="badge bg-info">Draft</span>
                    {% endif %}
                  </td>
                  <td>{{ doc.document.created_at|date:"d M, Y" }}</td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="{% url 'documents:document_detail' doc.document.id %}" class="btn btn-sm btn-outline-primary" title="View">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="{% url 'documents:document_download' doc.document.id %}" class="btn btn-sm btn-outline-success" title="Download">
                        <i class="fas fa-download"></i>
                      </a>
                      <a href="{% url 'documents:document_update' doc.document.id %}" class="btn btn-sm btn-outline-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>
                      <a href="{% url 'documents:document_delete' doc.document.id %}" class="btn btn-sm btn-outline-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="6" class="text-center">No documents found for this staff</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          
          <div class="mt-4">
            <a href="{% url 'documents:staff_documents' %}" class="btn btn-outline-secondary">
              <i class="fas fa-arrow-left me-2"></i> Back to Staff
            </a>
            <a href="{% url 'staff-detail' staff.id %}" class="btn btn-outline-success">
              <i class="fas fa-user me-2"></i> View Staff Profile
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block extrascripts %}
<script>
  $(document).ready(function() {
    // Any staff document-specific JavaScript can go here
  });
</script>
{% endblock extrascripts %}
