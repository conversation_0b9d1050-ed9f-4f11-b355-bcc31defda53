{% extends 'base.html' %}
{% load static %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-graduation-cap"></i> Examination
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:exam_type_list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-list"></i> Exam Types
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-trash"></i> Delete Exam Type
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-trash{% endblock title-icon %}

{% block title %}Delete Exam Type{% endblock title %}

{% block subtitle %}Confirm deletion of exam type{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-md-6 mx-auto">
      <div class="card shadow">
        <div class="card-header bg-danger text-white">
          <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion</h5>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong>Warning:</strong> This action cannot be undone. Are you sure you want to delete this exam type?
          </div>
          
          <div class="card mb-4">
            <div class="card-header bg-light">
              <h6 class="mb-0">Exam Type Details</h6>
            </div>
            <div class="card-body">
              <p><strong>Name:</strong> {{ object.name }}</p>
              <p><strong>Description:</strong> {{ object.description|default:"No description provided" }}</p>
            </div>
          </div>
          
          <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
              <a href="{% url 'exams:exam_type_list' %}" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i> Cancel
              </a>
              <button type="submit" class="btn btn-danger">
                <i class="fas fa-trash me-1"></i> Confirm Delete
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
