{% extends 'TeacherDashboard/base.html' %}
{% load static %}

{% block title %}Holiday Management - Teacher Dashboard{% endblock %}

{% block extra_css %}
<style>
    .holiday-card {
        border-left: 4px solid #ffc107;
        transition: all 0.3s ease;
    }
    .holiday-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .upcoming-holiday {
        border-left-color: #28a745;
    }
    .past-holiday {
        border-left-color: #6c757d;
        opacity: 0.8;
    }
    .holiday-item {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }
    .holiday-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .holiday-date {
        font-size: 1.2em;
        font-weight: bold;
        color: #007bff;
    }
    .holiday-name {
        font-size: 1.1em;
        font-weight: 600;
        margin-bottom: 5px;
    }
    .holiday-description {
        color: #6c757d;
        font-size: 0.9em;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-calendar-alt text-warning"></i>
                        Holiday Management
                    </h2>
                    <p class="text-muted mb-0">Manage school holidays and special days</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addHolidayModal">
                        <i class="fas fa-plus"></i> Add Holiday
                    </button>
                    <a href="{% url 'teacher_attendance_management' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Attendance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x mb-2"></i>
                    <h4>{{ holidays.count }}</h4>
                    <p class="mb-0">Total Holidays</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                    <h4>{{ upcoming_holidays.count }}</h4>
                    <p class="mb-0">Upcoming Holidays</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-minus fa-2x mb-2"></i>
                    <h4>{{ past_holidays.count }}</h4>
                    <p class="mb-0">Past Holidays (This Year)</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Holidays -->
        <div class="col-lg-6">
            <div class="card holiday-card upcoming-holiday">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-plus text-success"></i>
                        Upcoming Holidays
                        <span class="badge bg-success ms-2">{{ upcoming_holidays.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if upcoming_holidays %}
                        {% for holiday in upcoming_holidays %}
                        <div class="holiday-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="holiday-date">{{ holiday.date|date:"M d, Y" }} ({{ holiday.date|date:"l" }})</div>
                                    <div class="holiday-name">{{ holiday.name }}</div>
                                    {% if holiday.description %}
                                        <div class="holiday-description">{{ holiday.description }}</div>
                                    {% endif %}
                                </div>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                            onclick="editHoliday({{ holiday.id }}, '{{ holiday.date|date:"Y-m-d" }}', '{{ holiday.name|escapejs }}', '{{ holiday.description|escapejs }}')"
                                            data-bs-toggle="modal" data-bs-target="#editHolidayModal">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteHoliday({{ holiday.id }}, '{{ holiday.name|escapejs }}', '{{ holiday.date|date:"M d, Y" }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Upcoming Holidays</h5>
                            <p class="text-muted">Add holidays to keep track of school closures.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Past Holidays -->
        <div class="col-lg-6">
            <div class="card holiday-card past-holiday">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-minus text-secondary"></i>
                        Past Holidays (This Year)
                        <span class="badge bg-secondary ms-2">{{ past_holidays.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if past_holidays %}
                        <div style="max-height: 400px; overflow-y: auto;">
                            {% for holiday in past_holidays %}
                            <div class="holiday-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="holiday-date">{{ holiday.date|date:"M d, Y" }} ({{ holiday.date|date:"l" }})</div>
                                        <div class="holiday-name">{{ holiday.name }}</div>
                                        {% if holiday.description %}
                                            <div class="holiday-description">{{ holiday.description }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="editHoliday({{ holiday.id }}, '{{ holiday.date|date:"Y-m-d" }}', '{{ holiday.name|escapejs }}', '{{ holiday.description|escapejs }}')"
                                                data-bs-toggle="modal" data-bs-target="#editHolidayModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteHoliday({{ holiday.id }}, '{{ holiday.name|escapejs }}', '{{ holiday.date|date:"M d, Y" }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-minus fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Past Holidays</h5>
                            <p class="text-muted">Past holidays for this year will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Holiday Modal -->
<div class="modal fade" id="addHolidayModal" tabindex="-1" aria-labelledby="addHolidayModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addHolidayModalLabel">
                    <i class="fas fa-plus text-primary"></i> Add New Holiday
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'teacher_add_holiday' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="holidayDate" class="form-label">Holiday Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="holidayDate" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="holidayName" class="form-label">Holiday Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="holidayName" name="name" 
                               placeholder="e.g., Independence Day" required>
                    </div>
                    <div class="mb-3">
                        <label for="holidayDescription" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="holidayDescription" name="description" rows="3"
                                  placeholder="Additional details about the holiday"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Add Holiday
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Holiday Modal -->
<div class="modal fade" id="editHolidayModal" tabindex="-1" aria-labelledby="editHolidayModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editHolidayModalLabel">
                    <i class="fas fa-edit text-warning"></i> Edit Holiday
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'teacher_edit_holiday' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <input type="hidden" id="editHolidayId" name="holiday_id">
                    <div class="mb-3">
                        <label for="editHolidayDate" class="form-label">Holiday Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="editHolidayDate" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="editHolidayName" class="form-label">Holiday Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editHolidayName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editHolidayDescription" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="editHolidayDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Update Holiday
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Holiday Form (Hidden) -->
<form id="deleteHolidayForm" method="post" action="{% url 'teacher_delete_holiday' %}" style="display: none;">
    {% csrf_token %}
    <input type="hidden" id="deleteHolidayId" name="holiday_id">
</form>
{% endblock %}

{% block extra_js %}
<script>
// Edit holiday function
function editHoliday(id, date, name, description) {
    document.getElementById('editHolidayId').value = id;
    document.getElementById('editHolidayDate').value = date;
    document.getElementById('editHolidayName').value = name;
    document.getElementById('editHolidayDescription').value = description;
}

// Delete holiday function
function deleteHoliday(id, name, date) {
    if (confirm(`Are you sure you want to delete the holiday "${name}" on ${date}?`)) {
        document.getElementById('deleteHolidayId').value = id;
        document.getElementById('deleteHolidayForm').submit();
    }
}

// Set minimum date for holiday date inputs (today)
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('holidayDate').setAttribute('min', today);
    
    // For edit modal, we don't set minimum date as we might need to edit past holidays
});

// Form validation
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        const requiredFields = this.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
{% endblock %}
