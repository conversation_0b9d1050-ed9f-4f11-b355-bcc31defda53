{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-graduation-cap"></i> Examination
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:exam_schedule_list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-calendar-alt"></i> Exam Schedules
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-{% if object %}edit{% else %}plus{% endif %}"></i> 
        {% if object %}Edit{% else %}Create{% endif %} Schedule
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-{% if object %}edit{% else %}plus{% endif %}{% endblock title-icon %}

{% block title %}{% if object %}Edit{% else %}Create{% endif %} Exam Schedule{% endblock title %}

{% block subtitle %}{% if object %}Update existing{% else %}Create new{% endif %} exam schedule{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-lg-10 mx-auto">
      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">
            <i class="fas fa-{% if object %}edit{% else %}plus{% endif %} me-2"></i>
            {% if object %}Edit{% else %}Create{% endif %} Exam Schedule
          </h5>
        </div>
        <div class="card-body">
          <form method="post">
            {% csrf_token %}
            
            <div class="row mb-4">
              <div class="col-md-12">
                <div class="alert alert-info d-flex align-items-center">
                  <i class="fas fa-info-circle fa-2x me-3"></i>
                  <div>
                    <h5 class="alert-heading mb-1">Important Information</h5>
                    <p class="mb-0">Please ensure that the exam schedule does not conflict with regular classes or other exams. The system will check for conflicts before saving.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.exam.id_for_label }}" class="form-label">Exam <span class="text-danger">*</span></label>
                {{ form.exam|add_class:"form-select" }}
                {% if form.exam.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.exam.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
                <div class="form-text">
                  <a href="{% url 'exams:exam_create' %}" target="_blank">
                    <i class="fas fa-plus-circle"></i> Create New Exam
                  </a>
                </div>
              </div>
              <div class="col-md-6">
                <label for="{{ form.subject.id_for_label }}" class="form-label">Subject <span class="text-danger">*</span></label>
                {{ form.subject|add_class:"form-select" }}
                {% if form.subject.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.subject.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.student_class.id_for_label }}" class="form-label">Class <span class="text-danger">*</span></label>
                {{ form.student_class|add_class:"form-select" }}
                {% if form.student_class.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.student_class.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-6">
                <label for="{{ form.section.id_for_label }}" class="form-label">Section</label>
                {{ form.section|add_class:"form-control" }}
                {% if form.section.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.section.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
                <div class="form-text">Leave blank if applicable to all sections</div>
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-4">
                <label for="{{ form.date.id_for_label }}" class="form-label">Date <span class="text-danger">*</span></label>
                {{ form.date|add_class:"form-control" }}
                {% if form.date.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.date.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-4">
                <label for="{{ form.start_time.id_for_label }}" class="form-label">Start Time <span class="text-danger">*</span></label>
                {{ form.start_time|add_class:"form-control" }}
                {% if form.start_time.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.start_time.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-4">
                <label for="{{ form.end_time.id_for_label }}" class="form-label">End Time <span class="text-danger">*</span></label>
                {{ form.end_time|add_class:"form-control" }}
                {% if form.end_time.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.end_time.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">Duration (minutes) <span class="text-danger">*</span></label>
                {{ form.duration_minutes|add_class:"form-control" }}
                {% if form.duration_minutes.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.duration_minutes.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
                <div class="form-text">This will be calculated automatically based on start and end time</div>
              </div>
              <div class="col-md-6">
                <label for="{{ form.venue.id_for_label }}" class="form-label">Venue</label>
                {{ form.venue|add_class:"form-control" }}
                {% if form.venue.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.venue.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
                <div class="form-text">Specify the venue where the exam will be conducted</div>
              </div>
            </div>
            
            <hr class="my-4">
            
            <div class="d-flex justify-content-between">
              <a href="{% url 'exams:exam_schedule_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to List
              </a>
              <div>
                <button type="reset" class="btn btn-light me-2">
                  <i class="fas fa-redo me-1"></i> Reset
                </button>
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-1"></i> {% if object %}Update{% else %}Save{% endif %}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block morejs %}
<script>
  $(document).ready(function() {
    // Auto-calculate duration when start time or end time changes
    function calculateDuration() {
      var startTime = $('#{{ form.start_time.id_for_label }}').val();
      var endTime = $('#{{ form.end_time.id_for_label }}').val();
      
      if (startTime && endTime) {
        var start = new Date('2000-01-01T' + startTime);
        var end = new Date('2000-01-01T' + endTime);
        
        if (end < start) {
          // If end time is before start time, assume it's the next day
          end.setDate(end.getDate() + 1);
        }
        
        var durationMs = end - start;
        var durationMinutes = Math.round(durationMs / 60000);
        
        $('#{{ form.duration_minutes.id_for_label }}').val(durationMinutes);
      }
    }
    
    $('#{{ form.start_time.id_for_label }}, #{{ form.end_time.id_for_label }}').on('change', calculateDuration);
    
    // Validate that the selected date is within the exam's date range
    $('#{{ form.exam.id_for_label }}').on('change', function() {
      var examId = $(this).val();
      if (examId) {
        // This would be implemented with an AJAX call to get exam dates
        // For now, just a placeholder
      }
    });
    
    // Initialize the form with any existing values
    calculateDuration();
  });
</script>
{% endblock morejs %}
