{% extends 'corecode/system_settings_base.html' %}
{% load static %}

{% block settings-icon %}fas fa-tachometer-alt{% endblock settings-icon %}
{% block settings-title %}System Dashboard{% endblock settings-title %}
{% block settings-icon-title %}fas fa-tachometer-alt{% endblock settings-icon-title %}
{% block settings-page-title %}System Settings Dashboard{% endblock settings-page-title %}
{% block settings-subtitle %}Overview of system configuration and status{% endblock settings-subtitle %}

{% block content-icon %}fas fa-tachometer-alt{% endblock content-icon %}
{% block content-title %}System Overview{% endblock content-title %}

{% block settings-content %}
<div class="row mb-4">
  <div class="col-md-12">
    <div class="alert alert-info d-flex align-items-center" role="alert">
      <i class="fas fa-info-circle fa-2x me-3"></i>
      <div>
        <h5 class="alert-heading">Welcome to System Settings</h5>
        <p class="mb-0">This dashboard provides an overview of your system configuration and status. Use the menu on the left to navigate to specific settings.</p>
      </div>
    </div>
  </div>
</div>

<div class="row mb-4">
  <!-- System Status -->
  <div class="col-md-6 mb-4">
    <div class="card h-100 settings-card border-left-success">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h5 class="card-title"><i class="fas fa-heartbeat text-success me-2"></i> System Status</h5>
          <span class="badge bg-success">Online</span>
        </div>
        <div class="system-stats">
          <div class="d-flex justify-content-between mb-2">
            <span>System Uptime:</span>
            <span class="fw-bold">{{ uptime|default:"7 days, 14 hours" }}</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>Database Status:</span>
            <span class="fw-bold text-success">Connected</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>Last Backup:</span>
            <span class="fw-bold">{{ last_backup|default:"2023-04-15 08:30 AM" }}</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>Storage Usage:</span>
            <div class="progress w-50" style="height: 20px;">
              <div class="progress-bar bg-info" role="progressbar" style="width: {{ storage_usage|default:45 }}%;" aria-valuenow="{{ storage_usage|default:45 }}" aria-valuemin="0" aria-valuemax="100">{{ storage_usage|default:45 }}%</div>
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer bg-transparent">
        <a href="#" class="btn btn-sm btn-outline-primary"><i class="fas fa-sync-alt me-1"></i> Refresh Status</a>
      </div>
    </div>
  </div>
  
  <!-- Current Configuration -->
  <div class="col-md-6 mb-4">
    <div class="card h-100 settings-card border-left-primary">
      <div class="card-body">
        <h5 class="card-title"><i class="fas fa-cog text-primary me-2"></i> Current Configuration</h5>
        <div class="current-config">
          <div class="d-flex justify-content-between mb-2">
            <span>College Name:</span>
            <span class="fw-bold">{{ profile.college_name }}</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>Academic Session:</span>
            <span class="fw-bold">{{ current_session }}</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>Current Term:</span>
            <span class="fw-bold">{{ current_term }}</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>System Version:</span>
            <span class="fw-bold">v{{ system_version|default:"2.5.1" }}</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>Last Updated:</span>
            <span class="fw-bold">{{ last_updated|default:"2023-04-10" }}</span>
          </div>
        </div>
      </div>
      <div class="card-footer bg-transparent">
        <a href="{% url 'general_settings' %}" class="btn btn-sm btn-outline-primary"><i class="fas fa-edit me-1"></i> Edit Configuration</a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- Quick Actions -->
  <div class="col-md-12 mb-4">
    <div class="card settings-card">
      <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-bolt text-warning me-2"></i> Quick Actions</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-4">
            <a href="{% url 'general_settings' %}" class="btn btn-outline-primary w-100 py-3 d-flex align-items-center justify-content-between">
              <span><i class="fas fa-server me-2"></i> General Settings</span>
              <i class="fas fa-chevron-right"></i>
            </a>
          </div>
          <div class="col-md-4">
            <a href="{% url 'academic_settings' %}" class="btn btn-outline-success w-100 py-3 d-flex align-items-center justify-content-between">
              <span><i class="fas fa-calendar-alt me-2"></i> Academic Settings</span>
              <i class="fas fa-chevron-right"></i>
            </a>
          </div>
          <div class="col-md-4">
            <a href="{% url 'backup_restore' %}" class="btn btn-outline-warning w-100 py-3 d-flex align-items-center justify-content-between">
              <span><i class="fas fa-file-export me-2"></i> Backup Data</span>
              <i class="fas fa-chevron-right"></i>
            </a>
          </div>
          <div class="col-md-4">
            <a href="{% url 'user_permissions' %}" class="btn btn-outline-danger w-100 py-3 d-flex align-items-center justify-content-between">
              <span><i class="fas fa-users-cog me-2"></i> User Management</span>
              <i class="fas fa-chevron-right"></i>
            </a>
          </div>
          <div class="col-md-4">
            <a href="{% url 'security_logs' %}" class="btn btn-outline-info w-100 py-3 d-flex align-items-center justify-content-between">
              <span><i class="fas fa-shield-alt me-2"></i> Security Settings</span>
              <i class="fas fa-chevron-right"></i>
            </a>
          </div>
          <div class="col-md-4">
            <a href="{% url 'database_management' %}" class="btn btn-outline-secondary w-100 py-3 d-flex align-items-center justify-content-between">
              <span><i class="fas fa-database me-2"></i> Database Tools</span>
              <i class="fas fa-chevron-right"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- System Health -->
  <div class="col-md-6 mb-4">
    <div class="card settings-card h-100">
      <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-chart-line text-success me-2"></i> System Health</h5>
      </div>
      <div class="card-body">
        <canvas id="systemHealthChart" width="400" height="200"></canvas>
      </div>
      <div class="card-footer bg-transparent text-center">
        <small class="text-muted">System performance over the last 7 days</small>
      </div>
    </div>
  </div>
  
  <!-- Recent Activities -->
  <div class="col-md-6 mb-4">
    <div class="card settings-card h-100">
      <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-history text-info me-2"></i> Recent Activities</h5>
      </div>
      <div class="card-body p-0">
        <div class="list-group list-group-flush">
          <div class="list-group-item">
            <div class="d-flex w-100 justify-content-between">
              <h6 class="mb-1">System Backup Completed</h6>
              <small>3 hours ago</small>
            </div>
            <p class="mb-1 text-muted">Automatic backup completed successfully</p>
          </div>
          <div class="list-group-item">
            <div class="d-flex w-100 justify-content-between">
              <h6 class="mb-1">User Settings Updated</h6>
              <small>Yesterday</small>
            </div>
            <p class="mb-1 text-muted">Admin updated user permission settings</p>
          </div>
          <div class="list-group-item">
            <div class="d-flex w-100 justify-content-between">
              <h6 class="mb-1">Academic Term Changed</h6>
              <small>2 days ago</small>
            </div>
            <p class="mb-1 text-muted">Current term updated to {{ current_term }}</p>
          </div>
          <div class="list-group-item">
            <div class="d-flex w-100 justify-content-between">
              <h6 class="mb-1">Database Optimization</h6>
              <small>1 week ago</small>
            </div>
            <p class="mb-1 text-muted">Scheduled database maintenance completed</p>
          </div>
        </div>
      </div>
      <div class="card-footer bg-transparent">
        <a href="{% url 'security_logs' %}" class="btn btn-sm btn-outline-primary">View All Activities</a>
      </div>
    </div>
  </div>
</div>
{% endblock settings-content %}

{% block settings-js %}
<script>
  // System Health Chart
  $(document).ready(function() {
    var ctx = document.getElementById('systemHealthChart').getContext('2d');
    var myChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
        datasets: [{
          label: 'Response Time (ms)',
          data: [120, 115, 125, 130, 110, 105, 115],
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.4,
          fill: true
        }, {
          label: 'CPU Usage (%)',
          data: [35, 40, 45, 50, 45, 40, 35],
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'top',
          },
          tooltip: {
            mode: 'index',
            intersect: false,
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  });
</script>
{% endblock settings-js %}
