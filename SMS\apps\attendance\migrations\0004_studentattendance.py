# Generated by Django 4.1.2 on 2025-03-20 09:19

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("attendance", "0003_delete_studentattendance"),
    ]

    operations = [
        migrations.CreateModel(
            name="StudentAttendance",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "registration_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Registration Number"
                    ),
                ),
                (
                    "fullname",
                    models.Char<PERSON>ield(max_length=100, verbose_name="Full Name"),
                ),
                (
                    "gender",
                    models.CharField(
                        choices=[("Male", "Male"), ("Female", "Female")],
                        max_length=10,
                        verbose_name="Gender",
                    ),
                ),
                (
                    "parent_number",
                    models.Char<PERSON>ield(
                        max_length=15, verbose_name="Parent's Contact Number"
                    ),
                ),
                ("address", models.TextField(verbose_name="Address")),
                (
                    "current_class",
                    models.Char<PERSON><PERSON>(max_length=50, verbose_name="Current Class"),
                ),
                ("section", models.Char<PERSON>ield(max_length=10, verbose_name="Section")),
                (
                    "attendance_status",
                    models.CharField(
                        choices=[("Present", "Present"), ("Absent", "Absent")],
                        default="Absent",
                        max_length=10,
                        verbose_name="Attendance Status",
                    ),
                ),
                ("date", models.DateField(auto_now_add=True, verbose_name="Date")),
            ],
        ),
    ]
