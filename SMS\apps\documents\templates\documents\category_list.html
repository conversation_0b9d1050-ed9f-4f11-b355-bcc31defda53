{% extends 'base.html' %}
{% load static %}

{% block title %}Document Categories{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item active" aria-current="page">Document Categories</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card shadow-sm">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="card-title">Document Categories</h2>
            <a href="{% url 'documents:category_create' %}" class="btn btn-primary">
              <i class="fas fa-plus me-2"></i> Add Category
            </a>
          </div>
          
          <!-- Categories Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>S/N</th>
                  <th>Category Name</th>
                  <th>Description</th>
                  <th>Document Types</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for category in categories %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ category.name }}</td>
                  <td>{{ category.description|default:"-" }}</td>
                  <td>
                    <span class="badge bg-primary">{{ category.document_types.count }}</span>
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="{% url 'documents:category_update' category.id %}" class="btn btn-sm btn-outline-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>
                      <a href="{% url 'documents:category_delete' category.id %}" class="btn btn-sm btn-outline-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="5" class="text-center">No categories found</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
