{% extends 'base.html' %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="#" class="text-decoration-none fw-bold">
          <i class="fas fa-clipboard-list"></i> Results
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-eye"></i> View Results
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title %}View Results{% endblock title %}

{% block fullcard %}

  {% for key, result in results.items %}
    <div class="card">
      <div class="card-header">
        {{result.student}}
      </div>
      <div class="card-body">
        <table class="table table-bordered table-sm">
          <thead class="thead-light">
            <tr>
              <th></th>
              <th>Subject</th>
              <th>Test Score</th>
              <th>Exam Score</th>
              <th>Total Score</th>
              <th>Grade</th>
            </tr>
          </thead>
          <tbody>
            {% for subject in result.subjects %}
              <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ subject.subject }}</td>
                <td>{{ subject.test_score }}</td>
                <td>{{ subject.exam_score }}</td>
                <td>{{ subject.total_score }}</td>
                <td>{{ subject.grade }}</td>
              </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="2"></td>
              <td>{{ result.test_total }}</td>
              <td>{{ result.exam_total }}</td>
              <td>{{ result.total_total }}</td>
              <td></td>
            </tr>
          </tfoot>
        </table>




      </div>
    </div>
  {% endfor %}

{% endblock fullcard %}
