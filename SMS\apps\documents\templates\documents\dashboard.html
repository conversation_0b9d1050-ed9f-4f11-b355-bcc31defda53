{% extends 'base.html' %}
{% load static %}

{% block title %}Document Management{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">Document Management</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card shadow-sm">
        <div class="card-body">
          <h2 class="card-title">Document Management</h2>
          <p class="card-text">Manage all types of documents in one place. Upload, organize, and download documents for students, staff, and exams.</p>
          <div class="row mt-4">
            <div class="col-md-3">
              <a href="{% url 'documents:document_upload' %}" class="btn btn-primary btn-block">
                <i class="fas fa-upload me-2"></i> Upload Document
              </a>
            </div>
            <div class="col-md-3">
              <a href="{% url 'documents:document_list' %}" class="btn btn-outline-primary btn-block">
                <i class="fas fa-list me-2"></i> All Documents
              </a>
            </div>
            <div class="col-md-3">
              <a href="{% url 'documents:category_list' %}" class="btn btn-outline-primary btn-block">
                <i class="fas fa-folder me-2"></i> Categories
              </a>
            </div>
            <div class="col-md-3">
              <a href="{% url 'documents:type_list' %}" class="btn btn-outline-primary btn-block">
                <i class="fas fa-tags me-2"></i> Document Types
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Document Statistics -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-primary text-white shadow-sm">
        <div class="card-body">
          <h5 class="card-title">Total Documents</h5>
          <h2 class="display-4">{{ document_stats.total_documents }}</h2>
          <p class="card-text">All documents in the system</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-success text-white shadow-sm">
        <div class="card-body">
          <h5 class="card-title">Approved</h5>
          <h2 class="display-4">{{ document_stats.approved_documents }}</h2>
          <p class="card-text">Approved documents</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-warning text-white shadow-sm">
        <div class="card-body">
          <h5 class="card-title">Pending</h5>
          <h2 class="display-4">{{ document_stats.pending_documents }}</h2>
          <p class="card-text">Pending approval</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-danger text-white shadow-sm">
        <div class="card-body">
          <h5 class="card-title">Expired</h5>
          <h2 class="display-4">{{ document_stats.expired_documents }}</h2>
          <p class="card-text">Expired documents</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Document Categories -->
  <div class="row mb-4">
    <div class="col-md-4">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Student Documents</h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            <div class="display-4 text-primary">
              <i class="fas fa-user-graduate"></i>
            </div>
            <h2 class="mt-2">{{ student_docs_count }}</h2>
            <p class="text-muted">Total student documents</p>
          </div>
          <div class="d-grid gap-2">
            <a href="{% url 'documents:student_documents' %}" class="btn btn-outline-primary">
              <i class="fas fa-folder-open me-2"></i> View Student Documents
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">Staff Documents</h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            <div class="display-4 text-success">
              <i class="fas fa-chalkboard-teacher"></i>
            </div>
            <h2 class="mt-2">{{ staff_docs_count }}</h2>
            <p class="text-muted">Total staff documents</p>
          </div>
          <div class="d-grid gap-2">
            <a href="{% url 'documents:staff_documents' %}" class="btn btn-outline-success">
              <i class="fas fa-folder-open me-2"></i> View Staff Documents
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-warning text-white">
          <h5 class="mb-0">Exam Documents</h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            <div class="display-4 text-warning">
              <i class="fas fa-file-alt"></i>
            </div>
            <h2 class="mt-2">{{ exam_docs_count }}</h2>
            <p class="text-muted">Total exam documents</p>
          </div>
          <div class="d-grid gap-2">
            <a href="{% url 'documents:exam_documents' %}" class="btn btn-outline-warning">
              <i class="fas fa-folder-open me-2"></i> View Exam Documents
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Documents -->
  <div class="row">
    <div class="col-md-12">
      <div class="card shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">Recent Documents</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>S/N</th>
                  <th>Title</th>
                  <th>Type</th>
                  <th>Status</th>
                  <th>Date Added</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for document in recent_documents %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ document.title }}</td>
                  <td>{{ document.document_type.name }}</td>
                  <td>
                    {% if document.status == 'approved' %}
                    <span class="badge bg-success">Approved</span>
                    {% elif document.status == 'pending' %}
                    <span class="badge bg-warning">Pending</span>
                    {% elif document.status == 'rejected' %}
                    <span class="badge bg-danger">Rejected</span>
                    {% elif document.status == 'expired' %}
                    <span class="badge bg-secondary">Expired</span>
                    {% else %}
                    <span class="badge bg-info">Draft</span>
                    {% endif %}
                  </td>
                  <td>{{ document.created_at|date:"d M, Y" }}</td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="{% url 'documents:document_detail' document.id %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="{% url 'documents:document_download' document.id %}" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-download"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="6" class="text-center">No documents found</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          {% if recent_documents %}
          <div class="text-end mt-3">
            <a href="{% url 'documents:document_list' %}" class="btn btn-outline-primary">
              View All Documents
            </a>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block extrascripts %}
<script>
  $(document).ready(function() {
    // Any dashboard-specific JavaScript can go here
  });
</script>
{% endblock extrascripts %}
