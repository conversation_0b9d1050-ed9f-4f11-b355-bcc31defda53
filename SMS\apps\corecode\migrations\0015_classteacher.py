# Generated by Django 5.2 on 2025-04-21 07:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "corecode",
            "0014_subject_code_subject_created_at_subject_department_and_more",
        ),
        ("staffs", "0017_rename_fullnaame_staff_fullname"),
    ]

    operations = [
        migrations.CreateModel(
            name="ClassTeacher",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("section", models.CharField(blank=True, max_length=10, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "student_class",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="class_teachers",
                        to="corecode.studentclass",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assigned_classes",
                        to="staffs.staff",
                    ),
                ),
            ],
            options={
                "verbose_name": "Class Teacher",
                "verbose_name_plural": "Class Teachers",
                "ordering": ["student_class__name", "section"],
                "unique_together": {("student_class", "section")},
            },
        ),
    ]
