{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-graduation-cap"></i> Examination
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-chart-bar"></i> Results
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-chart-bar{% endblock title-icon %}

{% block title %}Examination Results{% endblock title %}

{% block subtitle %}View and publish examination results{% endblock subtitle %}

{% block content %}
<div class="container-fluid exams-container">
  <!-- Filter Card -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Results</h5>
        </div>
        <div class="card-body">
          <form method="get" id="filterForm" class="row g-3">
            <div class="col-md-3">
              <label for="exam" class="form-label">Exam</label>
              <select name="exam" id="exam" class="form-select">
                <option value="">-- All Exams --</option>
                {% for exam in exams %}
                <option value="{{ exam.id }}" {% if selected_exam == exam.id %}selected{% endif %}>
                  {{ exam.name }} ({{ exam.session.name }} {{ exam.term.name }})
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-3">
              <label for="student_class" class="form-label">Class</label>
              <select name="student_class" id="student_class" class="form-select">
                <option value="">-- All Classes --</option>
                {% for class in classes %}
                <option value="{{ class.id }}" {% if selected_class == class.id %}selected{% endif %}>
                  {{ class.name }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-3">
              <label for="section" class="form-label">Section</label>
              <input type="text" name="section" id="section" class="form-control" value="{{ selected_section }}" placeholder="Enter section">
            </div>
            <div class="col-md-3">
              <label for="status" class="form-label">Status</label>
              <select name="status" id="status" class="form-select">
                <option value="">-- All Statuses --</option>
                <option value="draft" {% if selected_status == 'draft' %}selected{% endif %}>Draft</option>
                <option value="finalized" {% if selected_status == 'finalized' %}selected{% endif %}>Finalized</option>
                <option value="published" {% if selected_status == 'published' %}selected{% endif %}>Published</option>
              </select>
            </div>
            <div class="col-md-12 d-flex justify-content-end">
              <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i> Apply Filter
              </button>
              <a href="{% url 'exams:results' %}" class="btn btn-secondary">
                <i class="fas fa-redo me-1"></i> Reset
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Results List -->
  <div class="row">
    <div class="col-md-12">
      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Examination Results</h5>
          <div>
            <button type="button" class="btn btn-light btn-sm" id="publishBtn">
              <i class="fas fa-upload me-1"></i> Publish Selected
            </button>
            <button type="button" class="btn btn-light btn-sm ms-2" id="printBtn">
              <i class="fas fa-print me-1"></i> Print Selected
            </button>
            <button type="button" class="btn btn-light btn-sm ms-2" id="exportBtn">
              <i class="fas fa-download me-1"></i> Export
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover table-striped" id="resultsTable">
              <thead class="table-light">
                <tr>
                  <th>
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" id="selectAll">
                      <label class="form-check-label" for="selectAll"></label>
                    </div>
                  </th>
                  <th>S/N</th>
                  <th>Exam</th>
                  <th>Class</th>
                  <th>Section</th>
                  <th>Students</th>
                  <th>Pass %</th>
                  <th>Status</th>
                  <th>Last Updated</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for result in results %}
                <tr>
                  <td>
                    <div class="form-check">
                      <input class="form-check-input result-checkbox" type="checkbox" value="{{ result.id }}" id="result{{ result.id }}">
                      <label class="form-check-label" for="result{{ result.id }}"></label>
                    </div>
                  </td>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ result.exam_name }}</td>
                  <td>{{ result.class_name }}</td>
                  <td>{{ result.section|default:"-" }}</td>
                  <td>{{ result.student_count }}</td>
                  <td>{{ result.pass_percentage }}%</td>
                  <td>
                    {% if result.status == 'draft' %}
                      <span class="badge bg-warning">Draft</span>
                    {% elif result.status == 'finalized' %}
                      <span class="badge bg-primary">Finalized</span>
                    {% elif result.status == 'published' %}
                      <span class="badge bg-success">Published</span>
                    {% endif %}
                  </td>
                  <td>{{ result.updated_at|date:"d M, Y" }}</td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="{% url 'exams:result_detail' result.id %}" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="{% url 'exams:result_print' result.id %}" class="btn btn-sm btn-outline-primary" target="_blank">
                        <i class="fas fa-print"></i>
                      </a>
                      {% if result.status != 'published' %}
                      <a href="{% url 'exams:result_publish' result.id %}" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-upload"></i>
                      </a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="10" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                      <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                      <h5 class="text-muted">No results found</h5>
                      <p class="text-muted">Enter marks for exams to generate results</p>
                      <a href="{% url 'exams:marks_entry' %}" class="btn btn-primary mt-2">
                        <i class="fas fa-clipboard-check me-1"></i> Enter Marks
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Result Analysis -->
  <div class="row mt-4">
    <div class="col-md-12">
      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Result Analysis</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6 mb-4">
              <div class="card border h-100">
                <div class="card-header bg-light">
                  <h6 class="mb-0">Performance by Class</h6>
                </div>
                <div class="card-body">
                  <canvas id="classChart" height="250"></canvas>
                </div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card border h-100">
                <div class="card-header bg-light">
                  <h6 class="mb-0">Performance by Subject</h6>
                </div>
                <div class="card-body">
                  <canvas id="subjectChart" height="250"></canvas>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <div class="card border">
                <div class="card-header bg-light">
                  <h6 class="mb-0">Top Performers</h6>
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-hover table-striped">
                      <thead class="table-light">
                        <tr>
                          <th>Rank</th>
                          <th>Student</th>
                          <th>Class</th>
                          <th>Total Marks</th>
                          <th>Percentage</th>
                          <th>Grade</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% for student in top_performers %}
                        <tr>
                          <td>{{ forloop.counter }}</td>
                          <td>{{ student.name }}</td>
                          <td>{{ student.class_name }} {{ student.section|default:"" }}</td>
                          <td>{{ student.total_marks }}</td>
                          <td>{{ student.percentage }}%</td>
                          <td>
                            <span class="badge bg-success">{{ student.grade }}</span>
                          </td>
                        </tr>
                        {% empty %}
                        <tr>
                          <td colspan="6" class="text-center">No data available</td>
                        </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Export Modal -->
  <div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title" id="exportModalLabel">Export Results</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="exportForm">
            <div class="mb-3">
              <label class="form-label">Export Format</label>
              <div class="d-flex">
                <div class="form-check me-3">
                  <input class="form-check-input" type="radio" name="exportFormat" id="formatExcel" value="excel" checked>
                  <label class="form-check-label" for="formatExcel">
                    Excel
                  </label>
                </div>
                <div class="form-check me-3">
                  <input class="form-check-input" type="radio" name="exportFormat" id="formatPDF" value="pdf">
                  <label class="form-check-label" for="formatPDF">
                    PDF
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="exportFormat" id="formatCSV" value="csv">
                  <label class="form-check-label" for="formatCSV">
                    CSV
                  </label>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Export Options</label>
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="includeDetails" checked>
                <label class="form-check-label" for="includeDetails">
                  Include detailed marks
                </label>
              </div>
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="includeStats" checked>
                <label class="form-check-label" for="includeStats">
                  Include statistics
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="includeCharts">
                <label class="form-check-label" for="includeCharts">
                  Include charts (PDF only)
                </label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="exportSubmitBtn">
            <i class="fas fa-download me-1"></i> Export
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block morejs %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
  $(document).ready(function() {
    $('#resultsTable').DataTable({
      "paging": true,
      "lengthChange": true,
      "searching": true,
      "ordering": true,
      "info": true,
      "autoWidth": false,
      "responsive": true,
      "columnDefs": [
        { "orderable": false, "targets": [0, 9] }
      ],
      "language": {
        "emptyTable": "No results found",
        "zeroRecords": "No matching records found",
        "info": "Showing _START_ to _END_ of _TOTAL_ entries",
        "infoEmpty": "Showing 0 to 0 of 0 entries",
        "infoFiltered": "(filtered from _MAX_ total entries)",
        "search": "Search:",
        "paginate": {
          "first": "First",
          "last": "Last",
          "next": "Next",
          "previous": "Previous"
        }
      }
    });

    // Select all checkbox
    $('#selectAll').on('change', function() {
      $('.result-checkbox').prop('checked', $(this).prop('checked'));
    });

    // Publish button click
    $('#publishBtn').on('click', function() {
      var selectedIds = getSelectedIds();
      if (selectedIds.length === 0) {
        alert('Please select at least one result to publish.');
        return;
      }

      if (confirm('Are you sure you want to publish the selected results? This will make them visible to students and parents.')) {
        // This would be implemented with AJAX in a real application
        alert('Publish functionality would be implemented with AJAX in a real application.');
      }
    });

    // Print button click
    $('#printBtn').on('click', function() {
      var selectedIds = getSelectedIds();
      if (selectedIds.length === 0) {
        alert('Please select at least one result to print.');
        return;
      }

      // Open print page with selected IDs
      var url = "{% url 'exams:result_print_bulk' %}?ids=" + selectedIds.join(',');
      window.open(url, '_blank');
    });

    // Export button click
    $('#exportBtn').on('click', function() {
      $('#exportModal').modal('show');
    });

    // Export submit button click
    $('#exportSubmitBtn').on('click', function() {
      // This would be implemented with AJAX in a real application
      alert('Export functionality would be implemented with AJAX in a real application.');
      $('#exportModal').modal('hide');
    });

    // Function to get selected result IDs
    function getSelectedIds() {
      var selectedIds = [];
      $('.result-checkbox:checked').each(function() {
        selectedIds.push($(this).val());
      });
      return selectedIds;
    }

    // Initialize charts
    var classCtx = document.getElementById('classChart').getContext('2d');
    var subjectCtx = document.getElementById('subjectChart').getContext('2d');

    var classChart = new Chart(classCtx, {
      type: 'bar',
      data: {
        labels: ['Class 1', 'Class 2', 'Class 3', 'Class 4', 'Class 5'],
        datasets: [{
          label: 'Pass Percentage',
          data: [85, 75, 90, 65, 80],
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      },
      options: {
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: function(value) {
                return value + '%';
              }
            }
          }
        },
        plugins: {
          legend: {
            display: false
          }
        }
      }
    });

    var subjectChart = new Chart(subjectCtx, {
      type: 'bar',
      data: {
        labels: ['Mathematics', 'Science', 'English', 'History', 'Geography'],
        datasets: [{
          label: 'Average Marks',
          data: [75, 82, 68, 70, 65],
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }]
      },
      options: {
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: function(value) {
                return value + '%';
              }
            }
          }
        },
        plugins: {
          legend: {
            display: false
          }
        }
      }
    });
  });
</script>
{% endblock morejs %}
