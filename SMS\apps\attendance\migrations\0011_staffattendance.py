# Generated by Django 5.2 on 2025-04-24 06:37

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("attendance", "0010_holiday_attendance_holiday_name_and_more"),
        ("staffs", "0018_staff_passport"),
    ]

    operations = [
        migrations.CreateModel(
            name="StaffAttendance",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Present", "Present"),
                            ("Absent", "Absent"),
                            ("Leave", "Leave"),
                            ("Holiday", "Holiday"),
                            ("Sunday", "Sunday"),
                        ],
                        default="Absent",
                        max_length=10,
                    ),
                ),
                ("date", models.DateField(default=django.utils.timezone.now)),
                ("comment", models.TextField(blank=True, null=True)),
                ("is_holiday", models.BooleanField(default=False)),
                (
                    "holiday_name",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "staff",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendance",
                        to="staffs.staff",
                    ),
                ),
            ],
            options={
                "verbose_name": "Staff Attendance",
                "verbose_name_plural": "Staff Attendance Records",
                "unique_together": {("staff", "date")},
            },
        ),
    ]
