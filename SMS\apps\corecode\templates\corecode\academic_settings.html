{% extends 'corecode/system_settings_base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block settings-icon %}fas fa-calendar-alt{% endblock settings-icon %}
{% block settings-title %}Academic Settings{% endblock settings-title %}
{% block settings-icon-title %}fas fa-calendar-alt{% endblock settings-icon-title %}
{% block settings-page-title %}Academic Settings{% endblock settings-page-title %}
{% block settings-subtitle %}Configure academic sessions and terms{% endblock settings-subtitle %}

{% block content-icon %}fas fa-calendar-alt{% endblock content-icon %}
{% block content-title %}Academic Configuration{% endblock content-title %}

{% block settings-content %}
<div class="row mb-4">
  <div class="col-md-12">
    <div class="alert alert-info d-flex align-items-center" role="alert">
      <i class="fas fa-info-circle fa-2x me-3"></i>
      <div>
        <h5 class="alert-heading">Academic Settings</h5>
        <p class="mb-0">Configure academic sessions, terms, and related settings for your institution.</p>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <!-- Current Session and Term -->
    <div class="card mb-4 settings-card">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i> Current Academic Period</h5>
      </div>
      <div class="card-body">
        <form method="POST" id="currentSessionForm">
          {% csrf_token %}
          <div class="row">
            <div class="col-md-6 mb-3">
              <label class="form-label fw-bold">Current Session</label>
              {{ form.current_session|add_class:"form-select" }}
              <div class="text-muted small mt-1">{{ form.current_session.help_text|safe }}</div>
              <div class="text-danger small">{{ form.current_session.errors }}</div>
            </div>
            <div class="col-md-6 mb-3">
              <label class="form-label fw-bold">Current Term</label>
              {{ form.current_term|add_class:"form-select" }}
              <div class="text-muted small mt-1">{{ form.current_term.help_text|safe }}</div>
              <div class="text-danger small">{{ form.current_term.errors }}</div>
            </div>
          </div>
          <div class="d-flex justify-content-end">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save me-2"></i> Update Current Period
            </button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- Academic Sessions Management -->
    <div class="card mb-4 settings-card">
      <div class="card-header bg-success text-white">
        <h5 class="mb-0"><i class="fas fa-calendar me-2"></i> Academic Sessions</h5>
      </div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-12">
            <form method="POST" id="sessionForm" class="mb-4">
              {% csrf_token %}
              <div class="row align-items-end">
                <div class="col-md-8">
                  <label class="form-label fw-bold">New Session Name</label>
                  <input type="text" name="name" class="form-control" placeholder="e.g. 2023/2024" required>
                </div>
                <div class="col-md-4">
                  <button type="submit" class="btn btn-success w-100">
                    <i class="fas fa-plus me-2"></i> Add Session
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
        
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-light">
              <tr>
                <th>Session Name</th>
                <th>Status</th>
                <th class="text-end">Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for session in sessions %}
              <tr>
                <td>{{ session.name }}</td>
                <td>
                  {% if session.current %}
                  <span class="badge bg-success">Current</span>
                  {% else %}
                  <span class="badge bg-secondary">Inactive</span>
                  {% endif %}
                </td>
                <td class="text-end">
                  <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary edit-session" data-id="{{ session.id }}" data-name="{{ session.name }}">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-session" data-id="{{ session.id }}" {% if session.current %}disabled{% endif %}>
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="3" class="text-center">No sessions available</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- Academic Terms Management -->
    <div class="card mb-4 settings-card">
      <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i> Academic Terms</h5>
      </div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-12">
            <form method="POST" id="termForm" class="mb-4">
              {% csrf_token %}
              <div class="row align-items-end">
                <div class="col-md-8">
                  <label class="form-label fw-bold">New Term Name</label>
                  <input type="text" name="name" class="form-control" placeholder="e.g. First Term" required>
                </div>
                <div class="col-md-4">
                  <button type="submit" class="btn btn-info w-100 text-white">
                    <i class="fas fa-plus me-2"></i> Add Term
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
        
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-light">
              <tr>
                <th>Term Name</th>
                <th>Status</th>
                <th class="text-end">Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for term in terms %}
              <tr>
                <td>{{ term.name }}</td>
                <td>
                  {% if term.current %}
                  <span class="badge bg-success">Current</span>
                  {% else %}
                  <span class="badge bg-secondary">Inactive</span>
                  {% endif %}
                </td>
                <td class="text-end">
                  <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary edit-term" data-id="{{ term.id }}" data-name="{{ term.name }}">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-term" data-id="{{ term.id }}" {% if term.current %}disabled{% endif %}>
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="3" class="text-center">No terms available</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- Academic Calendar Settings -->
    <div class="card mb-4 settings-card">
      <div class="card-header bg-warning text-dark">
        <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i> Academic Calendar</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label fw-bold">Academic Year Start Date</label>
            <input type="date" class="form-control" name="academic_start_date" value="2023-04-01">
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label fw-bold">Academic Year End Date</label>
            <input type="date" class="form-control" name="academic_end_date" value="2024-03-31">
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label fw-bold">Week Start Day</label>
            <select class="form-select" name="week_start_day">
              <option value="monday" selected>Monday</option>
              <option value="sunday">Sunday</option>
            </select>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label fw-bold">Default Term Duration (weeks)</label>
            <input type="number" class="form-control" name="term_duration" value="12" min="1" max="24">
          </div>
        </div>
        
        <div class="row mt-3">
          <div class="col-md-12">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="autoAdvanceTerms" checked>
              <label class="form-check-label fw-bold" for="autoAdvanceTerms">Automatically Advance Terms</label>
            </div>
            <div class="text-muted small mb-3">System will automatically advance to the next term at the end of the current term</div>
            
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="lockPreviousTerm">
              <label class="form-check-label fw-bold" for="lockPreviousTerm">Lock Previous Term Data</label>
            </div>
            <div class="text-muted small">Prevent modifications to data from previous terms</div>
          </div>
        </div>
        
        <div class="d-flex justify-content-end mt-3">
          <button type="button" class="btn btn-primary">
            <i class="fas fa-save me-2"></i> Save Calendar Settings
          </button>
        </div>
      </div>
    </div>
    
    <!-- Form Navigation -->
    <div class="d-flex justify-content-between">
      <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='{% url 'system_settings_dashboard' %}'">
        <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
      </button>
    </div>
  </div>
</div>

<!-- Edit Session Modal -->
<div class="modal fade" id="editSessionModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title"><i class="fas fa-edit me-2"></i> Edit Session</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="editSessionForm">
          <input type="hidden" id="editSessionId">
          <div class="mb-3">
            <label class="form-label fw-bold">Session Name</label>
            <input type="text" class="form-control" id="editSessionName" required>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="saveSessionBtn">Save Changes</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Term Modal -->
<div class="modal fade" id="editTermModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-info text-white">
        <h5 class="modal-title"><i class="fas fa-edit me-2"></i> Edit Term</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="editTermForm">
          <input type="hidden" id="editTermId">
          <div class="mb-3">
            <label class="form-label fw-bold">Term Name</label>
            <input type="text" class="form-control" id="editTermName" required>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-info text-white" id="saveTermBtn">Save Changes</button>
      </div>
    </div>
  </div>
</div>
{% endblock settings-content %}

{% block settings-js %}
<script>
  $(document).ready(function() {
    // Initialize modals
    var sessionModal = new bootstrap.Modal(document.getElementById('editSessionModal'));
    var termModal = new bootstrap.Modal(document.getElementById('editTermModal'));
    
    // Edit Session
    $('.edit-session').on('click', function() {
      const id = $(this).data('id');
      const name = $(this).data('name');
      
      $('#editSessionId').val(id);
      $('#editSessionName').val(name);
      
      sessionModal.show();
    });
    
    // Save Session Changes
    $('#saveSessionBtn').on('click', function() {
      const id = $('#editSessionId').val();
      const name = $('#editSessionName').val();
      
      if (name.trim() === '') {
        toastr.error('Session name cannot be empty');
        return;
      }
      
      // Here you would normally send an AJAX request to update the session
      // For demo purposes, we'll just show a success message
      toastr.success('Session updated successfully');
      sessionModal.hide();
    });
    
    // Delete Session
    $('.delete-session').on('click', function() {
      const id = $(this).data('id');
      
      if (confirm('Are you sure you want to delete this session? This action cannot be undone.')) {
        // Here you would normally send an AJAX request to delete the session
        // For demo purposes, we'll just show a success message
        toastr.success('Session deleted successfully');
      }
    });
    
    // Edit Term
    $('.edit-term').on('click', function() {
      const id = $(this).data('id');
      const name = $(this).data('name');
      
      $('#editTermId').val(id);
      $('#editTermName').val(name);
      
      termModal.show();
    });
    
    // Save Term Changes
    $('#saveTermBtn').on('click', function() {
      const id = $('#editTermId').val();
      const name = $('#editTermName').val();
      
      if (name.trim() === '') {
        toastr.error('Term name cannot be empty');
        return;
      }
      
      // Here you would normally send an AJAX request to update the term
      // For demo purposes, we'll just show a success message
      toastr.success('Term updated successfully');
      termModal.hide();
    });
    
    // Delete Term
    $('.delete-term').on('click', function() {
      const id = $(this).data('id');
      
      if (confirm('Are you sure you want to delete this term? This action cannot be undone.')) {
        // Here you would normally send an AJAX request to delete the term
        // For demo purposes, we'll just show a success message
        toastr.success('Term deleted successfully');
      }
    });
  });
</script>
{% endblock settings-js %}
