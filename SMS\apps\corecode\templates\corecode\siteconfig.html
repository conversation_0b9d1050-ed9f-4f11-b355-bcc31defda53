{% extends 'base.html' %}
{% load widget_tweaks %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-cogs"></i> System Settings
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title %}System Settings{% endblock title %}

{% block content %}
<div class="container ">
  {% comment %} <h2 class="mb-4 text-center">{{ title }}</h2> {% endcomment %}
  <form method="POST" enctype="multipart/form-data" class="p-4 border rounded bg-light shadow" id="siteConfigForm">
    {% csrf_token %}
    {{ form.non_field_errors }}
    <div class="row">
      {% for field in form %}
        <div class="col-md-6 mb-3">
          <label class="form-label font-weight-bold">{{ field.label }}</label>
          {{ field|add_class:"form-control" }}
          <div class="text-danger small">{{ field.errors }}</div>
        </div>
      {% endfor %}
    </div>
    <div class="text-center mt-4">
      <button type="submit" class="btn btn-primary px-5">Save</button>
    </div>
  </form>
</div>
{% endblock content %}
