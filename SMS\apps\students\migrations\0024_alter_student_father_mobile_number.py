# Generated by Django 4.1.2 on 2025-03-27 05:01

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("students", "0023_student_section"),
    ]

    operations = [
        migrations.AlterField(
            model_name="student",
            name="Father_mobile_number",
            field=models.CharField(
                max_length=15,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Entered mobile number isn't in a right format!",
                        regex="^[0-9]{10,15}$",
                    )
                ],
            ),
        ),
    ]
