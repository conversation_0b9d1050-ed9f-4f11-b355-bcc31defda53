{% extends 'base.html' %}
{% load static %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'fees:fee_list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-money-bill-wave"></i> Fee Management
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-history"></i> Payment History
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-history{% endblock title-icon %}

{% block title %}Payment History{% endblock title %}

{% block subtitle %}View complete payment records for {{ student.fullname }}{% endblock subtitle %}

{% block page-actions %}
<div class="d-flex">
  <button onclick="window.print()" class="btn btn-outline-primary me-2">
    <i class="fas fa-print me-1"></i> Print
  </button>
  <a href="{% url 'fees:generate_complete_history' student.id %}" class="btn btn-primary">
    <i class="fas fa-download me-1"></i> Download PDF
  </a>
</div>
{% endblock page-actions %}

{% block content %}
<div class="container mt-4 receipt-container">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">Payment History - {{ student.fullname }}</h4>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Student Information</h5>
                    <p><strong>Name:</strong> {{ student.fullname }}</p>
                    <p><strong>Registration No:</strong> {{ student.registration_number }}</p>
                    <p><strong>Class:</strong> {{ student.current_class }}</p>
                    <p><strong>Section:</strong> {{ student.section }}</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Payment Summary</h5>
                    <p><strong>Total Payments:</strong> {{ payments|length }}</p>
                    <p><strong>Total Amount Paid:</strong> ₹{{ total_paid|floatformat:2 }}</p>
                    <p><strong>Generated On:</strong> {% now "d M Y H:i" %}</p>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover table-bordered">
                    <thead class="table-dark">
                        <tr>
                            <th>S/N</th>
                            <th>Date</th>
                            <th>Amount (₹)</th>
                            <th>Category</th>
                            <th>Payment Method</th>
                            <th>Transaction ID</th>
                            <th>Status</th>
                            <th class="no-print">Receipt</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>{{ payment.date|date:"d-m-Y" }}</td>
                            <td>{{ payment.amount|floatformat:2 }}</td>
                            <td>{{ payment.fee_category }}</td>
                            <td>{{ payment.payment_method }}</td>
                            <td>
                                {% if payment.transaction_id %}
                                    {{ payment.transaction_id }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge {% if payment.status == 'Paid' %}bg-success{% else %}bg-warning{% endif %}">
                                    {{ payment.status }}
                                </span>
                            </td>
                            <td class="no-print">
                                <div class="btn-group">
                                    <a href="{% url 'fees:generate_receipt' payment.id %}?format=html" class="btn btn-sm btn-info" target="_blank">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{% url 'fees:generate_receipt' payment.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download"></i> Download
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">No payment records found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-secondary">
                            <td colspan="2"><strong>Total</strong></td>
                            <td><strong>₹{{ total_paid|floatformat:2 }}</strong></td>
                            <td colspan="5"></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="d-flex justify-content-between mt-3 no-print">
                <div>
                    <a href="{% url 'fees:fee_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Fee List
                    </a>
                </div>
                <div>
                    <a href="{% url 'fees:add_fee_payment' student.id %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Add New Payment
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .receipt-container, .receipt-container * {
            visibility: visible;
        }
        .receipt-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none;
        }
    }
</style>
{% endblock %}
