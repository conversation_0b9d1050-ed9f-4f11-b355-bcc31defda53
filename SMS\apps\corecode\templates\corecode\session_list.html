{% extends 'base.html' %}
{% load widget_tweaks %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="#" class="text-decoration-none fw-bold">
          <i class="fas fa-copy"></i> Management
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-calendar-alt"></i> Academic Sessions
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-calendar-alt{% endblock title-icon %}

{% block title %}Academic Sessions{% endblock title %}

{% block subtitle %}Manage academic years and sessions{% endblock subtitle %}


{% block breadcrumb %}
{% endblock breadcrumb %}

{% block page-actions %}
  <a class="btn btn-primary" href="#" id="addSessionBtn">
    <i class="fas fa-plus me-2"></i> Add New Session
  </a>
{% endblock page-actions %}


{% block content %}
  <div class="row">
    <div class="col-sm-12">
      <div class="card shadow-sm">
        <div class="card-header bg-light">
          <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i> Session List
            <span class="badge bg-primary ms-2">{{ object_list|length }}</span>
          </h5>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover table-striped" id="sessionTable">
              <thead class="table-light">
                <tr>
                  <th width="5%" class="text-center">#</th>
                  <th width="60%">Session Name</th>
                  <th width="20%" class="text-center">Status</th>
                  <th width="15%" class="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for object in object_list %}
                <tr class="session-row" data-session-id="{{ object.id }}" data-session-name="{{ object }}" data-session-current="{{ object.current|yesno:'true,false' }}">
                  <td class="text-center">{{ forloop.counter }}</td>
                  <td>
                    <span class="fw-medium">{{ object }}</span>
                  </td>
                  <td class="text-center">
                    {% if object.current == True %}
                      <span class="badge bg-success">Current</span>
                    {% else %}
                      <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <div class="btn-group btn-group-sm">
                      <button type="button" class="btn btn-outline-primary edit-session-btn" data-session-id="{{ object.id }}" title="Edit Session">
                        <i class="fa fa-edit"></i>
                      </button>
                      <a href="{% url 'session-delete' object.id %}" class="btn btn-outline-danger" title="Delete Session" {% if object.current %}disabled{% endif %}>
                        <i class="fa fa-trash-alt"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="4" class="text-center py-4">
                    <div class="empty-state">
                      <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                      <h5>No Sessions Found</h5>
                      <p class="text-muted">Start by adding your first academic session using the 'Add New Session' button.</p>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>


  <!-- Edit Session Modal -->
  <div class="modal fade" id="editSessionModal" tabindex="-1" aria-labelledby="editSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <form id="editSessionForm">
          <div class="modal-header bg-primary text-white">
            <h5 class="modal-title" id="editSessionModalLabel">
              <i class="fas fa-edit me-2"></i> Edit Session
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            {% csrf_token %}
            <input type="hidden" id="editSessionId" name="session_id">
            <div class="mb-3">
              <label for="editSessionName" class="form-label fw-bold">Session Name</label>
              <input type="text" class="form-control" id="editSessionName" name="name" required>
              <div class="invalid-feedback">Please enter a session name.</div>
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="editSessionCurrent" name="current">
              <label class="form-check-label" for="editSessionCurrent">Set as current session</label>
            </div>
          </div>
          <div class="modal-footer bg-light">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i> Cancel
            </button>
            <button type="submit" class="btn btn-primary" id="saveEditSessionBtn">
              <i class="fas fa-save me-1"></i> Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Add New Session Modal -->
  <div class="modal fade" id="modal1" tabindex="-1" aria-labelledby="addSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <form action="{% url 'session-create' %}" method="POST" id="addSessionForm">
          <div class="modal-header bg-primary text-white">
            <h5 class="modal-title" id="addSessionModalLabel">
              <i class="fas fa-plus-circle me-2"></i> Add New Session
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            {% csrf_token %}
            <div class="alert alert-info">
              <i class="fas fa-info-circle me-2"></i> Enter the academic session name (e.g., "2023/2024").
            </div>
            {% for field in form %}
            <div class="mb-3">
              <label for="{{ field.id_for_label }}" class="form-label fw-bold">{{ field.label }}</label>
              {{ field|add_class:"form-control" }}
              <div class="form-text">{{ field.help_text }}</div>
              <div class="invalid-feedback">{{ field.errors }}</div>
            </div>
            {% endfor %}
          </div>
          <div class="modal-footer bg-light">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i> Cancel
            </button>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save me-1"></i> Save Session
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

{% endblock content %}

{% block morejs %}
<script>
  $(document).ready(function() {
    // Setup CSRF token for AJAX requests
    var csrftoken = $('input[name="csrfmiddlewaretoken"]').val();

    $.ajaxSetup({
      beforeSend: function(xhr, settings) {
        if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
          xhr.setRequestHeader("X-CSRFToken", csrftoken);
        }
      }
    });

    // Handle the Add Session button click
    $('#addSessionBtn').on('click', function(e) {
      e.preventDefault();
      var modal = new bootstrap.Modal(document.getElementById('modal1'));
      modal.show();
    });

    // Edit session button click
    $('.edit-session-btn').on('click', function() {
      var sessionId = $(this).data('session-id');
      var sessionRow = $('.session-row[data-session-id="' + sessionId + '"]');
      var sessionName = sessionRow.data('session-name');
      var sessionCurrent = sessionRow.data('session-current') === 'true';

      // Populate the edit form
      $('#editSessionId').val(sessionId);
      $('#editSessionName').val(sessionName);
      $('#editSessionCurrent').prop('checked', sessionCurrent);

      // Show the edit modal
      var editModal = new bootstrap.Modal(document.getElementById('editSessionModal'));
      editModal.show();
    });

    // Handle edit session form submission
    $('#editSessionForm').on('submit', function(e) {
      e.preventDefault();

      var sessionId = $('#editSessionId').val();
      var sessionName = $('#editSessionName').val();
      var sessionCurrent = $('#editSessionCurrent').is(':checked');

      if (!sessionName.trim()) {
        $('#editSessionName').addClass('is-invalid');
        return false;
      }

      // Show loading state
      var $btn = $('#saveEditSessionBtn');
      var originalHtml = $btn.html();
      $btn.html('<i class="fas fa-spinner fa-spin me-1"></i> Saving...');
      $btn.prop('disabled', true);

      // Make AJAX call to update session
      $.ajax({
        url: '/api/session/' + sessionId + '/update/',
        type: 'POST',
        data: {
          name: sessionName,
          current: sessionCurrent,
          csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val()
        },
        success: function(response) {
          // Close modal
          var editModal = bootstrap.Modal.getInstance(document.getElementById('editSessionModal'));
          editModal.hide();

          // Show success message
          alert('Session updated successfully!');

          // Update the UI
          var row = $('.session-row[data-session-id="' + sessionId + '"]');
          row.data('session-name', sessionName);
          row.data('session-current', sessionCurrent ? 'true' : 'false');
          row.find('td:nth-child(2) span').text(sessionName);

          // Update status badge
          if (sessionCurrent) {
            row.find('td:nth-child(3) span').removeClass('bg-secondary').addClass('bg-success').text('Current');
            // Update other rows to show as inactive
            $('.session-row').not(row).each(function() {
              $(this).data('session-current', 'false');
              $(this).find('td:nth-child(3) span').removeClass('bg-success').addClass('bg-secondary').text('Inactive');
            });
          } else {
            row.find('td:nth-child(3) span').removeClass('bg-success').addClass('bg-secondary').text('Inactive');
          }

          // Reset button
          $btn.html(originalHtml);
          $btn.prop('disabled', false);
        },
        error: function(xhr) {
          var errorMsg = 'Failed to update session';
          if (xhr.responseJSON && xhr.responseJSON.error) {
            errorMsg = xhr.responseJSON.error;
          }
          alert(errorMsg);

          // Reset button
          $btn.html(originalHtml);
          $btn.prop('disabled', false);
        }
      });
    });

    // Form validation
    $('#addSessionForm').on('submit', function(e) {
      var form = $(this);
      var nameField = form.find('input[name="name"]');
      var isValid = true;

      if (!nameField.val().trim()) {
        nameField.addClass('is-invalid');
        nameField.siblings('.invalid-feedback').text('Session name is required');
        isValid = false;
      } else {
        nameField.removeClass('is-invalid');
      }

      return isValid;
    });
  });
</script>
{% endblock morejs %}
