# Generated by Django 5.1.6 on 2025-03-05 10:44

import django.core.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('staffs', '0013_rename_subject_specification_staff_subject_specification_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='staff',
            old_name='subject_specification',
            new_name='Subject_specification',
        ),
        migrations.RemoveField(
            model_name='staff',
            name='registration_number',
        ),
        migrations.AlterField(
            model_name='staff',
            name='date_of_birth',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='staff',
            name='mobile_number',
            field=models.CharField(blank=True, max_length=13, validators=[django.core.validators.RegexValidator(message="Entered mobile number isn't in a right format!", regex='^[0-9]{10,15}$')]),
        ),
    ]
