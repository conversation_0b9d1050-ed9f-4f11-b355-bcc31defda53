{% extends 'base.html' %}
{% load static %}

{% block title-icon %}fas fa-users{% endblock title-icon %}

{% block title %}Non-Teaching Staffs{% endblock title %}

{% block subtitle %}View and manage all non-teaching staff members{% endblock subtitle %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'non-teaching-staffs-list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-users"></i> Non-Teaching Staffs
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-list"></i> List
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block page-actions %}
<div class="d-flex gap-2">
  <a class="btn btn-primary rounded-3 shadow-sm" href="{% url 'non-teaching-staffs-create' %}">
    <i class="fas fa-plus me-2"></i> Add New Staff
  </a>
  <button type="button" class="btn btn-outline-secondary rounded-3 shadow-sm" id="refreshStaffList">
    <i class="fas fa-sync-alt me-2"></i> Refresh
  </button>
  <div class="dropdown">
    <button class="btn btn-outline-success rounded-3 shadow-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
      <i class="fas fa-download me-2"></i> Export
    </button>
    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
      <li><a class="dropdown-item" href="#" id="exportCSV"><i class="fas fa-file-csv me-2"></i> Export as CSV</a></li>
      <li><a class="dropdown-item" href="#" id="exportPDF"><i class="fas fa-file-pdf me-2"></i> Export as PDF</a></li>
      <li><a class="dropdown-item" href="#" id="exportExcel"><i class="fas fa-file-excel me-2"></i> Export as Excel</a></li>
    </ul>
  </div>
</div>
{% endblock page-actions %}

{% block content %}
<div class="row mb-3">
  <div class="col-md-12">
    <div class="card shadow-sm">
      <div class="card-header bg-light">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i> Non-Teaching Staff List
            <span class="badge bg-primary ms-2" id="totalStaffCount">{{ object_list|length }}</span>
          </h5>
          <div class="d-flex align-items-center">
            <div class="input-group input-group-sm me-2" style="width: 200px;">
              <span class="input-group-text" id="basic-addon1"><i class="fas fa-search"></i></span>
              <input type="text" class="form-control" id="staffSearchInput" placeholder="Search staff..." aria-label="Search">
            </div>
            <div class="dropdown me-2">
              <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="statusFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-filter me-1"></i> Status
              </button>
              <ul class="dropdown-menu" aria-labelledby="statusFilterDropdown">
                <li><a class="dropdown-item filter-status" href="#" data-status="all">All</a></li>
                <li><a class="dropdown-item filter-status" href="#" data-status="active">Active</a></li>
                <li><a class="dropdown-item filter-status" href="#" data-status="inactive">Inactive</a></li>
              </ul>
            </div>
            <div class="dropdown">
              <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="genderFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-venus-mars me-1"></i> Gender
              </button>
              <ul class="dropdown-menu" aria-labelledby="genderFilterDropdown">
                <li><a class="dropdown-item filter-gender" href="#" data-gender="all">All</a></li>
                <li><a class="dropdown-item filter-gender" href="#" data-gender="male">Male</a></li>
                <li><a class="dropdown-item filter-gender" href="#" data-gender="female">Female</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table id="stafftable" class="table table-hover table-striped mb-0">
            <thead class="table-light">
              <tr>
                <th width="5%" class="text-center">#</th>
                <th width="20%">Full Name</th>
                <th width="10%" class="text-center">Gender</th>
                <th width="15%">Phone Number</th>
                <th width="25%">Job Role</th>
                <th width="10%" class="text-center">Status</th>
                <th width="15%" class="text-center">Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for staff in object_list %}
                <tr class='clickable-row' data-href="{% url 'non-teaching-staffs-detail' staff.id %}" data-status="{{ staff.current_status }}" data-gender="{{ staff.gender }}">
                  <td class="text-center">{{ forloop.counter }}</td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="staff-avatar me-2">
                        <i class="fas fa-user-circle text-primary"></i>
                      </div>
                      <div>
                        <span class="fw-medium">{{ staff.fullname }}</span>
                      </div>
                    </div>
                  </td>
                  <td class="text-center">
                    {% if staff.gender == 'male' %}
                      <span class="badge bg-info text-dark"><i class="fas fa-mars me-1"></i> {{ staff.get_gender_display }}</span>
                    {% else %}
                      <span class="badge bg-danger"><i class="fas fa-venus me-1"></i> {{ staff.get_gender_display }}</span>
                    {% endif %}
                  </td>
                  <td>{{ staff.mobile_number|default:"Not provided" }}</td>
                  <td>
                    <span class="badge bg-secondary me-1"><i class="fas fa-briefcase me-1"></i></span>
                    {{ staff.job_role|default:"Not specified" }}
                  </td>
                  <td class="text-center">
                    {% if staff.current_status == 'active' %}
                      <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i> {{ staff.get_current_status_display }}</span>
                    {% else %}
                      <span class="badge bg-secondary"><i class="fas fa-times-circle me-1"></i> {{ staff.get_current_status_display }}</span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <div class="btn-group btn-group-sm">
                      <a href="{% url 'non-teaching-staffs-detail' staff.id %}" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="View Details">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="{% url 'non-teaching-staffs-update' staff.id %}" class="btn btn-outline-warning" data-bs-toggle="tooltip" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>
                      <a href="{% url 'non-teaching-staffs-delete' staff.id %}" class="btn btn-outline-danger" data-bs-toggle="tooltip" title="Delete">
                        <i class="fas fa-trash"></i>
                      </a>
                    </div>
                  </td>
                </tr>
              {% empty %}
                <tr>
                  <td colspan="7" class="text-center py-4">
                    <div class="empty-state">
                      <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                      <h5>No Non-Teaching Staff Records Found</h5>
                      <p class="text-muted">Start by adding your first non-teaching staff member using the 'Add New Staff' button.</p>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
      <div class="card-footer bg-light">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <small class="text-muted">Showing <span id="visibleStaffCount">{{ object_list|length }}</span> of {{ object_list|length }} staff members</small>
          </div>
          <div>
            <button type="button" class="btn btn-sm btn-outline-primary" id="viewAllStaff">
              <i class="fas fa-eye me-1"></i> View All
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block morejs %}
<script>
  $(document).ready(function() {
    // Initialize DataTable with enhanced options
    var staffTable = $('#stafftable').DataTable({
      "paging": true,
      "lengthChange": true,
      "searching": true,
      "ordering": true,
      "info": true,
      "autoWidth": false,
      "responsive": true,
      "pageLength": 10,
      "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
      "language": {
        "emptyTable": "No staff records found",
        "zeroRecords": "No matching records found",
        "info": "Showing _START_ to _END_ of _TOTAL_ entries",
        "infoEmpty": "Showing 0 to 0 of 0 entries",
        "infoFiltered": "(filtered from _MAX_ total entries)",
        "search": "Search:",
        "paginate": {
          "first": "First",
          "last": "Last",
          "next": "Next",
          "previous": "Previous"
        }
      },
      "dom": '<"top"fl>rt<"bottom"ip><"clear">',
      "select": false,
      "columnDefs": [
        { "orderable": false, "targets": 6 } // Disable sorting on action column
      ]
    });

    // Make rows clickable except for the action buttons column
    $('#stafftable tbody').on('click', 'tr td:not(:last-child)', function() {
      window.location.href = $(this).parent().data('href');
    });

    // Custom search functionality
    $('#staffSearchInput').on('keyup', function() {
      staffTable.search(this.value).draw();
      updateVisibleCount();
    });

    // Status filter
    $('.filter-status').on('click', function(e) {
      e.preventDefault();
      var status = $(this).data('status');

      if (status === 'all') {
        staffTable.column(5).search('').draw();
      } else {
        staffTable.column(5).search(status, true, false).draw();
      }

      $('#statusFilterDropdown').text($(this).text());
      updateVisibleCount();
    });

    // Gender filter
    $('.filter-gender').on('click', function(e) {
      e.preventDefault();
      var gender = $(this).data('gender');

      if (gender === 'all') {
        staffTable.column(2).search('').draw();
      } else {
        staffTable.column(2).search(gender, true, false).draw();
      }

      $('#genderFilterDropdown').text($(this).text());
      updateVisibleCount();
    });

    // View all button
    $('#viewAllStaff').on('click', function() {
      staffTable.search('').columns().search('').draw();
      $('#statusFilterDropdown').text('Status');
      $('#genderFilterDropdown').text('Gender');
      $('#staffSearchInput').val('');
      updateVisibleCount();
    });

    // Refresh button
    $('#refreshStaffList').on('click', function() {
      location.reload();
    });

    // Export buttons functionality
    $('#exportCSV').on('click', function(e) {
      e.preventDefault();
      alert('CSV Export functionality will be implemented here');
    });

    $('#exportPDF').on('click', function(e) {
      e.preventDefault();
      alert('PDF Export functionality will be implemented here');
    });

    $('#exportExcel').on('click', function(e) {
      e.preventDefault();
      alert('Excel Export functionality will be implemented here');
    });

    // Function to update visible count
    function updateVisibleCount() {
      var visibleRows = staffTable.rows({search:'applied'}).count();
      $('#visibleStaffCount').text(visibleRows);
    }

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add subtle animation to rows
    $('#stafftable tbody tr').each(function(index) {
      $(this).css('opacity', '0');
      setTimeout(() => {
        $(this).animate({opacity: 1}, 200);
      }, 50 * index);
    });
  });
</script>
{% endblock morejs %}
