{% extends 'exams/base_exams.html' %}
{% load static %}

{% block exam-breadcrumb %}
<li class="breadcrumb-item">
  <a href="{% url 'exams:document_management' %}"><i class="fas fa-file-archive"></i> Document Management</a>
</li>
<li class="breadcrumb-item active" aria-current="page">
  <i class="fas fa-download"></i> Document Templates
</li>
{% endblock exam-breadcrumb %}

{% block title-icon %}fas fa-download{% endblock title-icon %}

{% block title %}Document Templates{% endblock title %}

{% block subtitle %}Download standard templates for examination documents{% endblock subtitle %}

{% block page-actions %}
<div class="btn-group">
  <a href="{% url 'exams:document_management' %}" class="btn btn-outline-primary">
    <i class="fas fa-arrow-left me-2"></i> Back to Document Center
  </a>
  <a href="{% url 'exams:document_archive' %}" class="btn btn-outline-secondary">
    <i class="fas fa-archive me-2"></i> Document Archive
  </a>
  <a href="{% url 'exams:document_generate' doc_type='bulk' %}" class="btn btn-primary">
    <i class="fas fa-copy me-2"></i> Bulk Generate
  </a>
</div>
{% endblock page-actions %}

{% block content %}
<div class="container-fluid exams-container">
  <!-- Templates Section -->
  <div class="card border-0 rounded-3 shadow-sm mb-4">
    <div class="card-header bg-gradient-primary text-white rounded-top">
      <h5 class="mb-0 fw-bold"><i class="fas fa-download me-2"></i>Available Templates</h5>
    </div>
    <div class="card-body p-4">
      <div class="row g-4">
        {% for template in templates %}
        <div class="col-md-4">
          <div class="document-card h-100">
            <div class="card-header">
              <h5 class="document-title mb-0">{{ template.name }}</h5>
            </div>
            <div class="card-body">
              <div class="text-center mb-3">
                <div class="document-icon text-{{ template.color }}">
                  <i class="fas fa-{{ template.icon }}"></i>
                </div>
              </div>
              <p class="document-meta">{{ template.description }}</p>
              <div class="d-flex justify-content-between align-items-center mb-3">
                <span class="badge bg-{{ template.color }}">{{ template.file_type }}</span>
                <span class="text-muted small">{{ template.file_size }}</span>
              </div>
              <div class="document-actions">
                <a href="{% url 'exams:document_download' 'template' %}?name={{ template.name|slugify }}" class="btn btn-{{ template.color }} w-100">
                  <i class="fas fa-download me-2"></i> Download Template
                </a>
              </div>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
  </div>

  <!-- Template Usage Guide -->
  <div class="card border-0 rounded-3 shadow-sm">
    <div class="card-header bg-gradient-info text-white rounded-top">
      <h5 class="mb-0 fw-bold"><i class="fas fa-info-circle me-2"></i>Template Usage Guide</h5>
    </div>
    <div class="card-body p-4">
      <div class="row">
        <div class="col-md-6">
          <div class="mb-4">
            <h5 class="fw-bold"><i class="fas fa-id-card text-warning me-2"></i>Admit Card Template</h5>
            <p>The admit card template is designed for printing student examination entry permits. It includes:</p>
            <ul>
              <li>Student information section</li>
              <li>Examination details</li>
              <li>Schedule table</li>
              <li>QR code for verification</li>
              <li>Signature fields</li>
            </ul>
            <p><strong>Recommended:</strong> Print on A4 size paper in portrait orientation.</p>
          </div>
          
          <div class="mb-4">
            <h5 class="fw-bold"><i class="fas fa-file-alt text-danger me-2"></i>Question Paper Template</h5>
            <p>The question paper template provides a standardized format for creating examination papers. Features include:</p>
            <ul>
              <li>Properly formatted header with institution details</li>
              <li>Exam information section</li>
              <li>Instructions for students</li>
              <li>Structured sections for different question types</li>
              <li>Footer with page numbers</li>
            </ul>
            <p><strong>Recommended:</strong> Edit in Microsoft Word or compatible word processor.</p>
          </div>
          
          <div class="mb-4">
            <h5 class="fw-bold"><i class="fas fa-edit text-success me-2"></i>Answer Sheet Template</h5>
            <p>The answer sheet template is designed for student responses during examinations. It includes:</p>
            <ul>
              <li>Student information fields</li>
              <li>Examination details</li>
              <li>Structured answer spaces</li>
              <li>Page numbering</li>
              <li>Space for examiner marks</li>
            </ul>
            <p><strong>Recommended:</strong> Print on A4 size paper in portrait orientation.</p>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="mb-4">
            <h5 class="fw-bold"><i class="fas fa-file-pdf text-warning me-2"></i>Report Card Template</h5>
            <p>The report card template provides a comprehensive format for student performance reports. Features include:</p>
            <ul>
              <li>Student and class information</li>
              <li>Subject-wise marks table</li>
              <li>Grade calculation</li>
              <li>Performance graphs</li>
              <li>Teacher and principal signature fields</li>
              <li>Attendance summary</li>
            </ul>
            <p><strong>Recommended:</strong> Print on A4 size paper in portrait orientation.</p>
          </div>
          
          <div class="mb-4">
            <h5 class="fw-bold"><i class="fas fa-clipboard-check text-info me-2"></i>Attendance Sheet Template</h5>
            <p>The attendance sheet template is designed for tracking student presence during examinations. It includes:</p>
            <ul>
              <li>Exam and subject information</li>
              <li>Student list with roll numbers</li>
              <li>Signature columns</li>
              <li>Invigilator details</li>
              <li>Summary section</li>
            </ul>
            <p><strong>Recommended:</strong> Print on A4 size paper in landscape orientation.</p>
          </div>
          
          <div class="mb-4">
            <h5 class="fw-bold"><i class="fas fa-file-excel text-success me-2"></i>Marks Entry Template</h5>
            <p>The marks entry template is an Excel spreadsheet for efficient entry and calculation of student marks. Features include:</p>
            <ul>
              <li>Pre-formatted columns for student details</li>
              <li>Subject-wise mark entry cells</li>
              <li>Automatic calculation of totals and percentages</li>
              <li>Grade assignment based on marks</li>
              <li>Data validation to prevent errors</li>
            </ul>
            <p><strong>Recommended:</strong> Use with Microsoft Excel or compatible spreadsheet software.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block morecss %}
<style>
  /* Custom styles for document cards */
  .document-card {
    transition: all 0.3s ease;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
  }
  
  .document-card:hover {
    transform: translateY(-7px);
    box-shadow: 0 0.5rem 2rem 0 rgba(33, 40, 50, 0.2);
  }
  
  .document-card .card-header {
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.05), rgba(42, 82, 152, 0.1));
    border-bottom: 1px solid rgba(33, 40, 50, 0.1);
    padding: 1rem 1.25rem;
  }
  
  .document-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
  }
  
  .document-card:hover .document-icon {
    transform: scale(1.1);
  }
  
  .document-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
  }
  
  .document-meta {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 1.5rem;
  }
  
  .document-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
  }
</style>
{% endblock morecss %}
