# Generated by Django 5.1.6 on 2025-03-04 11:02

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('students', '0004_student_mobile_number'),
    ]

    operations = [
        migrations.AddField(
            model_name='student',
            name='aadhar',
            field=models.CharField(blank=True, max_length=12, validators=[django.core.validators.RegexValidator(code='invalid_aadhar', message='Aadhaar number must be exactly 12 digits.', regex='^\\d{12}$')]),
        ),
    ]
