# Generated by Django 4.1.2 on 2025-03-21 10:34

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "corecode",
            "0008_siteconfig_college_address_siteconfig_college_email_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="CollegeProfile",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("college_name", models.CharField(max_length=255)),
                ("college_address", models.TextField()),
                ("college_email", models.EmailField(max_length=254)),
                ("college_phone", models.CharField(max_length=15)),
                (
                    "college_logo",
                    models.ImageField(
                        blank=True, null=True, upload_to="college_logos/"
                    ),
                ),
                ("established_year", models.PositiveIntegerField()),
                ("principal_name", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                (
                    "college_type",
                    models.<PERSON>r<PERSON><PERSON>(
                        choices=[
                            ("Government", "Government"),
                            ("Private", "Private"),
                            ("Semi-Government", "Semi-Government"),
                        ],
                        max_length=50,
                    ),
                ),
                ("admin_email", models.EmailField(max_length=254)),
                ("admin_contact", models.CharField(max_length=15)),
                ("facebook_link", models.URLField(blank=True, null=True)),
                ("twitter_link", models.URLField(blank=True, null=True)),
                ("linkedin_link", models.URLField(blank=True, null=True)),
            ],
        ),
    ]
