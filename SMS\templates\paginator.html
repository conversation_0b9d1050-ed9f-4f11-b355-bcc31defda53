
{% if page_obj.has_other_pages %}
<div aria-label="pagination">
  <ul class="pagination justify-content-center">
    {% if page_obj.has_previous %}
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.previous_page_number }}" tabindex="-1">Previous</a>
      </li>
    {% endif %}

    {% for page in page_obj.paginator.page_range %}

      {% if page_obj.number == page %}
        <li class="page-item active" aria-current="page">
          <span class="page-link">{{ page }}<span class="sr-only">(current)</span>
          </span>
        </li>
      {% else %}
        <li class="page-item"><a class="page-link" href="?page={{ page }}">{{ page }}</a></li>
      {% endif %}

    {% endfor %}

    {% if page_obj.has_next %}
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
      </li>
      {% endif %}
  </ul>
</div>

{% endif %}
