{% extends 'corecode/system_settings_base.html' %}
{% load static %}

{% block settings-icon %}fas fa-users-cog{% endblock settings-icon %}
{% block settings-title %}User Permissions{% endblock settings-title %}
{% block settings-icon-title %}fas fa-users-cog{% endblock settings-icon-title %}
{% block settings-page-title %}User Permissions{% endblock settings-page-title %}
{% block settings-subtitle %}Manage user roles and permissions{% endblock settings-subtitle %}

{% block content-icon %}fas fa-users-cog{% endblock content-icon %}
{% block content-title %}User Management{% endblock content-title %}

{% block settings-content %}
<div class="row mb-4">
  <div class="col-md-12">
    <div class="alert alert-info d-flex align-items-center" role="alert">
      <i class="fas fa-info-circle fa-2x me-3"></i>
      <div>
        <h5 class="alert-heading">User Management</h5>
        <p class="mb-0">Configure user roles, permissions, and access levels for system users.</p>
      </div>
    </div>
  </div>
</div>

<!-- User List -->
<div class="card mb-4">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="mb-0"><i class="fas fa-users me-2"></i>System Users</h5>
    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
      <i class="fas fa-plus me-2"></i>Add User
    </button>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Username</th>
            <th>Email</th>
            <th>Role</th>
            <th>Status</th>
            <th>Last Login</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for user in users %}
          <tr>
            <td>
              <div class="d-flex align-items-center">
                <img src="{% static 'img/default-avatar.png' %}" alt="Avatar" class="rounded-circle me-2" style="width: 32px;">
                {{ user.username }}
              </div>
            </td>
            <td>{{ user.email }}</td>
            <td>
              <span class="badge bg-primary">{{ user.groups.first.name|default:"No Role" }}</span>
            </td>
            <td>
              {% if user.is_active %}
              <span class="badge bg-success">Active</span>
              {% else %}
              <span class="badge bg-danger">Inactive</span>
              {% endif %}
            </td>
            <td>{{ user.last_login|date:"M d, Y H:i" }}</td>
            <td>
              <button class="btn btn-sm btn-outline-primary me-1" onclick="editUser({{ user.id }})">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ user.id }})">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Role Management -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="mb-0"><i class="fas fa-user-tag me-2"></i>Role Management</h5>
    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addRoleModal">
      <i class="fas fa-plus me-2"></i>Add Role
    </button>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-4 mb-3">
        <div class="card h-100">
          <div class="card-body">
            <h6 class="card-title">Administrator</h6>
            <p class="card-text text-muted">Full system access and management capabilities</p>
            <div class="mt-3">
              <button class="btn btn-sm btn-outline-primary">Edit Permissions</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4 mb-3">
        <div class="card h-100">
          <div class="card-body">
            <h6 class="card-title">Teacher</h6>
            <p class="card-text text-muted">Access to classes, grades, and student information</p>
            <div class="mt-3">
              <button class="btn btn-sm btn-outline-primary">Edit Permissions</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4 mb-3">
        <div class="card h-100">
          <div class="card-body">
            <h6 class="card-title">Staff</h6>
            <p class="card-text text-muted">Limited access to administrative functions</p>
            <div class="mt-3">
              <button class="btn btn-sm btn-outline-primary">Edit Permissions</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Add New User</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="addUserForm">
          <div class="mb-3">
            <label class="form-label">Username</label>
            <input type="text" class="form-control" required>
          </div>
          <div class="mb-3">
            <label class="form-label">Email</label>
            <input type="email" class="form-control" required>
          </div>
          <div class="mb-3">
            <label class="form-label">Role</label>
            <select class="form-select">
              <option>Administrator</option>
              <option>Teacher</option>
              <option>Staff</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary">Add User</button>
      </div>
    </div>
  </div>
</div>
{% endblock settings-content %}

{% block page-scripts %}
<script>
function editUser(userId) {
  // Implement edit user functionality
  console.log('Edit user:', userId);
}

function deleteUser(userId) {
  if (confirm('Are you sure you want to delete this user?')) {
    // Implement delete user functionality
    console.log('Delete user:', userId);
  }
}

$(document).ready(function() {
  // Initialize tooltips
  $('[data-toggle="tooltip"]').tooltip();
});
</script>
{% endblock page-scripts %}