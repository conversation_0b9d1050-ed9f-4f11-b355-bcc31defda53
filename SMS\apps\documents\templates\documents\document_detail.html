{% extends 'base.html' %}
{% load static %}

{% block title %}{{ document.title }}{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:document_list' %}">All Documents</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ document.title }}</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-md-12 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h4 class="mb-0">Document Details</h4>
          <div>
            <a href="{% url 'documents:document_download' document.id %}" class="btn btn-light">
              <i class="fas fa-download me-2"></i> Download
            </a>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-8">
              <h2>{{ document.title }}</h2>
              <div class="mb-4">
                {% if document.status == 'approved' %}
                <span class="badge bg-success">Approved</span>
                {% elif document.status == 'pending' %}
                <span class="badge bg-warning">Pending Approval</span>
                {% elif document.status == 'rejected' %}
                <span class="badge bg-danger">Rejected</span>
                {% elif document.status == 'expired' %}
                <span class="badge bg-secondary">Expired</span>
                {% else %}
                <span class="badge bg-info">Draft</span>
                {% endif %}
                
                {% if document.content_type %}
                  {% if document.content_type.model == 'student' %}
                    <span class="badge bg-primary">Student Document</span>
                  {% elif document.content_type.model == 'staff' %}
                    <span class="badge bg-success">Staff Document</span>
                  {% elif document.content_type.model == 'exam' %}
                    <span class="badge bg-warning">Exam Document</span>
                  {% endif %}
                {% else %}
                  <span class="badge bg-secondary">General Document</span>
                {% endif %}
              </div>
              
              <div class="row mb-4">
                <div class="col-md-6">
                  <p><strong>Document Type:</strong> {{ document.document_type.name }}</p>
                  {% if document.document_number %}
                  <p><strong>Document Number:</strong> {{ document.document_number }}</p>
                  {% endif %}
                  <p><strong>Upload Date:</strong> {{ document.created_at|date:"d M, Y" }}</p>
                  {% if document.created_by %}
                  <p><strong>Uploaded By:</strong> {{ document.created_by }}</p>
                  {% endif %}
                </div>
                <div class="col-md-6">
                  <p><strong>Version:</strong> {{ document.version }}</p>
                  {% if document.expiry_date %}
                  <p><strong>Expiry Date:</strong> {{ document.expiry_date|date:"d M, Y" }}</p>
                  {% endif %}
                  {% if document.tags %}
                  <p><strong>Tags:</strong> 
                    {% for tag in document.tags.split|slice:":5" %}
                    <span class="badge bg-info">{{ tag }}</span>
                    {% endfor %}
                  </p>
                  {% endif %}
                </div>
              </div>
              
              {% if document.description %}
              <div class="mb-4">
                <h5>Description</h5>
                <p>{{ document.description }}</p>
              </div>
              {% endif %}
              
              {% if entity %}
              <div class="mb-4">
                <h5>
                  {% if entity_type == 'student' %}
                  Student Information
                  {% elif entity_type == 'staff' %}
                  Staff Information
                  {% elif entity_type == 'exam' %}
                  Exam Information
                  {% endif %}
                </h5>
                <div class="card">
                  <div class="card-body">
                    {% if entity_type == 'student' %}
                    <p><strong>Name:</strong> {{ entity.fullname }}</p>
                    <p><strong>Registration Number:</strong> {{ entity.registration_number }}</p>
                    <p><strong>Class:</strong> {{ entity.current_class.name }} {{ entity.section }}</p>
                    {% elif entity_type == 'staff' %}
                    <p><strong>Name:</strong> {{ entity.fullname }}</p>
                    <p><strong>Registration Number:</strong> {{ entity.registration_number }}</p>
                    {% elif entity_type == 'exam' %}
                    <p><strong>Exam Name:</strong> {{ entity.name }}</p>
                    <p><strong>Session:</strong> {{ entity.session }}</p>
                    <p><strong>Term:</strong> {{ entity.term }}</p>
                    <p><strong>Status:</strong> {{ entity.get_status_display }}</p>
                    {% endif %}
                  </div>
                </div>
              </div>
              {% endif %}
            </div>
            
            <div class="col-md-4">
              <div class="card mb-4">
                <div class="card-header bg-light">
                  <h5 class="mb-0">Document Preview</h5>
                </div>
                <div class="card-body text-center">
                  {% if document.file.url|lower|slice:"-4:" == ".pdf" %}
                  <div class="document-preview pdf-preview">
                    <i class="fas fa-file-pdf fa-5x text-danger mb-3"></i>
                    <p>PDF Document</p>
                  </div>
                  {% elif document.file.url|lower|slice:"-4:" == ".doc" or document.file.url|lower|slice:"-5:" == ".docx" %}
                  <div class="document-preview doc-preview">
                    <i class="fas fa-file-word fa-5x text-primary mb-3"></i>
                    <p>Word Document</p>
                  </div>
                  {% elif document.file.url|lower|slice:"-4:" == ".jpg" or document.file.url|lower|slice:"-4:" == ".png" or document.file.url|lower|slice:"-5:" == ".jpeg" %}
                  <div class="document-preview image-preview">
                    <img src="{{ document.file.url }}" alt="{{ document.title }}" class="img-fluid mb-3" style="max-height: 300px;">
                    <p>Image Document</p>
                  </div>
                  {% else %}
                  <div class="document-preview generic-preview">
                    <i class="fas fa-file fa-5x text-secondary mb-3"></i>
                    <p>Document</p>
                  </div>
                  {% endif %}
                  
                  <a href="{{ document.file.url }}" target="_blank" class="btn btn-outline-primary mt-3">
                    <i class="fas fa-eye me-2"></i> View Original
                  </a>
                </div>
              </div>
              
              <div class="card">
                <div class="card-header bg-light">
                  <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                  <div class="d-grid gap-2">
                    <a href="{% url 'documents:document_download' document.id %}" class="btn btn-success">
                      <i class="fas fa-download me-2"></i> Download Document
                    </a>
                    <a href="{% url 'documents:document_update' document.id %}" class="btn btn-warning">
                      <i class="fas fa-edit me-2"></i> Edit Document
                    </a>
                    <a href="{% url 'documents:document_delete' document.id %}" class="btn btn-danger">
                      <i class="fas fa-trash me-2"></i> Delete Document
                    </a>
                    <a href="{% url 'documents:document_list' %}" class="btn btn-outline-secondary">
                      <i class="fas fa-arrow-left me-2"></i> Back to Documents
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block extrascripts %}
<script>
  $(document).ready(function() {
    // Any document-specific JavaScript can go here
  });
</script>
{% endblock extrascripts %}
