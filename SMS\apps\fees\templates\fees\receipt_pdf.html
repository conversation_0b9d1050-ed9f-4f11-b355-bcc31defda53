<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Fee Receipt - {{ payment.student.fullname }}</title>
    <style>
        @page {
            size: A4;
            margin: 0.5cm;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #fff;
            font-size: 9pt;
        }
        .receipt-container {
            width: 100%;
            position: relative;
            background-color: #fff;
        }
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 80px;
            color: rgba(200, 200, 200, 0.15);
            z-index: -1;
            white-space: nowrap;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo-section {
            display: flex;
            align-items: center;
        }
        .logo {
            width: 50px;
            height: 50px;
            margin-right: 8px;
        }
        .school-info {
            text-align: right;
        }
        .receipt-title {
            text-align: center;
            margin: 5px 0;
            color: #3498db;
            font-size: 14pt;
            font-weight: bold;
            text-transform: uppercase;
        }
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px;
            background-color: #f9f9f9;
            border-radius: 3px;
        }
        .student-info, .payment-info {
            width: 48%;
        }
        .info-title {
            color: #3498db;
            border-bottom: 1px solid #ddd;
            padding-bottom: 3px;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 10pt;
        }
        .info-row {
            margin-bottom: 2px;
            display: flex;
            font-size: 8pt;
        }
        .info-label {
            font-weight: bold;
            width: 110px;
        }
        .info-value {
            flex: 1;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 3px;
            text-align: left;
            font-size: 8pt;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        .text-end {
            text-align: right;
        }
        .amount-cell {
            font-weight: bold;
            text-align: right;
        }
        .total-row {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .footer-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        .footer {
            width: 60%;
            font-size: 9pt;
        }
        .thank-you {
            margin-bottom: 3px;
            font-weight: bold;
        }
        .disclaimer {
            font-size: 8pt;
            color: #777;
            margin-top: 3px;
        }
        .signature {
            width: 35%;
            text-align: center;
        }
        .signature-line {
            border-top: 1px solid #333;
            width: 150px;
            margin: 0 auto;
            padding-top: 3px;
        }
        .signature-text {
            font-size: 9pt;
            font-weight: bold;
            margin-top: 3px;
        }
        .generation-info {
            font-size: 7pt;
            color: #999;
            text-align: center;
            margin-top: 8px;
            font-style: italic;
        }
        .badge {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 8pt;
            font-weight: bold;
        }
        .badge-success {
            background-color: #2ecc71;
            color: white;
        }
        .badge-warning {
            background-color: #f39c12;
            color: white;
        }
        .school-name {
            margin: 0;
            color: #3498db;
            font-size: 12pt;
            font-weight: bold;
        }
        .school-address {
            margin: 2px 0;
            font-size: 8pt;
        }
        .school-contact {
            margin: 0;
            font-size: 8pt;
        }
        .college-details {
            font-size: 7pt;
            margin: 1px 0;
        }
        .pending-note {
            font-size: 8pt;
            color: #e74c3c;
            font-style: italic;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Watermark -->
        <div class="watermark">PAID</div>

        <!-- Header with logo and school info (more organized) -->
        <div class="header">
            <div class="logo-section">
                {% if profile.college_logo %}
                <img src="{{ profile.college_logo.url }}" alt="School Logo" class="logo">
                {% endif %}
                <div>
                    <h2 class="school-name">{{ profile.college_name|default:"School Management System" }}</h2>
                    <p class="school-address" style="margin:0;">{{ profile.college_address|default:"123 School Street, City" }}</p>
                    <p class="school-contact" style="margin:0;">{{ profile.college_phone|default:"Phone" }} | {{ profile.college_email|default:"Email" }}</p>
                    <p class="college-details" style="margin:0;">Est: {{ profile.established_year|default:"" }} | {{ profile.college_type|default:"" }} | Principal: {{ profile.principal_name|default:"" }}</p>
                </div>
            </div>
            <div class="school-info" style="text-align:right; font-size:8pt;">
                <p style="margin:1px 0;"><strong>FEE RECEIPT</strong></p>
                <p style="margin:1px 0;">{{ profile.college_name|default:"School Management System" }}</p>
                <p style="margin:1px 0;">{{ current_session|default:"Current Session" }}</p>
            </div>
        </div>

        <!-- Student and Payment Information in a better organized layout -->
        <div class="info-section" style="display:flex; justify-content:space-between; background-color:#f9f9f9; padding:5px; border-radius:3px; margin-bottom:8px;">
            <!-- Left column: Student Information -->
            <div class="student-info" style="width:48%;">
                <div class="info-title" style="color:#3498db; border-bottom:1px solid #ddd; padding-bottom:3px; margin-bottom:5px; font-weight:bold; font-size:10pt;">Student Information</div>
                <table style="border:none; margin-bottom:0; width:100%;">
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; width:35%; font-weight:bold; font-size:8pt;">Name:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.student.fullname }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Registration No:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.student.registration_number }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Class:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.student.current_class }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Section:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.student.section }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Gender:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.student.gender|title }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Father's Name:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.student.Father_name }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Contact:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.student.Father_mobile_number }}</td>
                    </tr>
                </table>
            </div>

            <!-- Right column: Payment Details -->
            <div class="payment-info" style="width:48%;">
                <div class="info-title" style="color:#3498db; border-bottom:1px solid #ddd; padding-bottom:3px; margin-bottom:5px; font-weight:bold; font-size:10pt;">Payment Details</div>
                <table style="border:none; margin-bottom:0; width:100%;">
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; width:40%; font-weight:bold; font-size:8pt;">Receipt Number:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.id }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Payment Date:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.date|date:"d M Y" }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Payment Method:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.payment_method }}</td>
                    </tr>
                    {% if payment.transaction_id %}
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Transaction ID:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.transaction_id }}</td>
                    </tr>
                    {% endif %}
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Status:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">
                            <span class="badge {% if payment.status == 'Paid' %}badge-success{% else %}badge-warning{% endif %}">
                                {{ payment.status }}
                            </span>
                        </td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Fee Category:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ payment.fee_category }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Session:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ current_session|default:"Current Session" }}</td>
                    </tr>
                    <tr style="border:none;">
                        <td style="border:none; padding:1px; font-weight:bold; font-size:8pt;">Term:</td>
                        <td style="border:none; padding:1px; font-size:8pt;">{{ current_term|default:"Current Term" }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Payment Table (more organized) -->
        <div style="border:1px solid #ddd; border-radius:3px; margin-bottom:5px;">
            <table style="margin-bottom:0; width:100%;">
                <thead>
                    <tr>
                        <th style="width:8%; padding:3px; background-color:#3498db; color:white; font-size:8pt; text-align:center;">S/N</th>
                        <th style="width:47%; padding:3px; background-color:#3498db; color:white; font-size:8pt;">Fee Category</th>
                        <th style="width:20%; padding:3px; background-color:#3498db; color:white; font-size:8pt; text-align:center;">Date</th>
                        <th style="width:25%; padding:3px; background-color:#3498db; color:white; font-size:8pt; text-align:right;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding:3px; text-align:center; font-size:8pt; border-bottom:1px solid #eee;">1</td>
                        <td style="padding:3px; font-size:8pt; border-bottom:1px solid #eee;">{{ payment.fee_category }}</td>
                        <td style="padding:3px; text-align:center; font-size:8pt; border-bottom:1px solid #eee;">{{ payment.date|date:"d-m-Y" }}</td>
                        <td style="padding:3px; text-align:right; font-size:8pt; border-bottom:1px solid #eee;">₹{{ payment.amount|floatformat:2 }}</td>
                    </tr>
                    <tr class="total-row" style="background-color:#f5f5f5;">
                        <td colspan="3" style="padding:3px; text-align:right; font-weight:bold; font-size:8pt;">Total Amount</td>
                        <td style="padding:3px; text-align:right; font-weight:bold; font-size:8pt;">₹{{ payment.amount|floatformat:2 }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pending Fees Note (if any) -->
        {% if payment.student.get_due_fee > 0 %}
        <div class="pending-note" style="margin:2px 0; padding:3px; background-color:#fff3cd; border:1px solid #ffeeba; border-radius:3px; font-size:7pt;">
            <strong>Note:</strong> Pending fees: ₹{{ payment.student.get_due_fee|floatformat:2 }}. Please clear all dues before due date to avoid late fees.
        </div>
        {% endif %}

        <!-- Footer with signature (more organized) -->
        <div style="margin-top:10px; border-top:1px dashed #ddd; padding-top:5px;">
            <table style="width:100%; border:none;">
                <tr style="border:none;">
                    <td style="width:60%; border:none; vertical-align:top;">
                        <p style="margin:1px 0; font-size:8pt; font-weight:bold;">Thank you for your payment!</p>
                        <p style="margin:1px 0; font-size:7pt;">This is a computer-generated receipt and does not require a signature.</p>
                        <p style="margin:1px 0; font-size:7pt;">For queries: {{ profile.admin_email|default:profile.college_email }} | {{ profile.admin_contact|default:profile.college_phone }}</p>
                    </td>
                    <td style="width:40%; border:none; text-align:center; vertical-align:bottom;">
                        <div style="border-top:1px solid #333; width:120px; display:inline-block; margin-bottom:3px;"></div>
                        <p style="margin:0; font-size:8pt; font-weight:bold;">Authorized Signature</p>
                    </td>
                </tr>
            </table>

            <div style="text-align:center; margin-top:5px; font-size:7pt; color:#999; border-top:1px dashed #ddd; padding-top:3px;">
                Generated on: {{ generation_date|date:"d M Y H:i" }} | Valid Receipt | {{ profile.college_name|default:"School Management System" }}
            </div>
        </div>
    </div>
</body>
</html>
