{% extends 'base.html' %}
{% load widget_tweaks %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="#" class="text-decoration-none fw-bold">
          <i class="fas fa-clipboard-list"></i> Results
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-edit"></i> Update Results
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-edit{% endblock title-icon %}

{% block title %}Update Results{% endblock title %}

{% block subtitle %}Edit and update student academic results{% endblock subtitle %}


{% block content %}
<form method="POST">
  {% csrf_token %}
  {{ formset.management_form}}

  <table id="studenttable" class="table table-sm">
    <thead>
      <tr>
        <th></th>
        <th>Name</th>
        <th>Subject</th>
        <th>Test Score</th>
        <th>Exam Score</th>
        <th>Total</th>
        <th>Delete</th>
        <th>Class</th>
      </tr>
    </thead>
    <tbody>
      {% for form in formset %}
      {{ form.id}}
        <tr>
          <td>{{forloop.counter }}</td>
          <td>{{form.instance.student }}</td>
          <td>{{form.instance.subject}}</td>
          <td style="width: 10%;">{{form.test_score | add_class:"form-control form-control-sm"}}</td>
          <td style="width: 10%;">{{form.exam_score | add_class:"form-control form-control-sm"}}</td>
          <td>{{form.instance.total_score}}</td>
          <td>{{form.DELETE}}</td>
          <td>{{form.instance.current_class}}</td>

        </tr>
      {% endfor %}
    </tbody>
  </table>

  <input type="submit" class="btn btn-success" value="Save">
  </form>

{% endblock content %}
