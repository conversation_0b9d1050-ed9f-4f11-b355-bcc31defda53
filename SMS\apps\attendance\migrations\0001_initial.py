# Generated by Django 4.1.2 on 2025-03-12 05:06

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Student",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("registration_number", models.Char<PERSON><PERSON>(max_length=50, unique=True)),
                ("fullname", models.Char<PERSON>ield(max_length=100)),
                (
                    "gender",
                    models.Char<PERSON>ield(
                        choices=[("Male", "Male"), ("Female", "Female")], max_length=10
                    ),
                ),
                ("parent_number", models.Char<PERSON>ield(max_length=15)),
                ("address", models.TextField()),
                ("current_class", models.CharField(max_length=50)),
                ("section", models.Char<PERSON>ield(max_length=10)),
                (
                    "attendance_status",
                    models.Char<PERSON>ield(
                        choices=[("Present", "Present"), ("Absent", "Absent")],
                        default="Absent",
                        max_length=10,
                    ),
                ),
                ("date", models.DateField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="StudentBulkUpload",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("csv_file", models.FileField(upload_to="uploads/")),
                ("uploaded_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
