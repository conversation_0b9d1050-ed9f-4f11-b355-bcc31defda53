{% extends 'base.html' %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'student-list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-user-graduate"></i> Students
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'student-list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-list"></i> List
        </a>
      </li>
      <li class="breadcrumb-item active text-danger" aria-current="page">
        <i class="fas fa-trash-alt"></i> Delete
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title %} Delete Confirmation - {{ object }} {% endblock title %}

{% block content %}
<style>
    /* Glassmorphism effect */
    .glass-card {
        background: rgba(255, 255, 255, 0.15);
        border-radius: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        max-width: 500px;
    }

    /* Warning icon animation */
    .warning-icon {
        animation: pulse 1.5s infinite alternate;
    }

    @keyframes pulse {
        0% { transform: scale(1); color: #dc3545; }
        100% { transform: scale(1.2); color: #ff0000; }
    }

    /* Button hover effects */
    .btn-danger:hover {
        background-color: #c82333 !important;
        transform: scale(1.05);
        transition: 0.3s ease-in-out;
    }

    .btn-secondary:hover {
        background-color: #6c757d !important;
        transform: scale(1.05);
        transition: 0.3s ease-in-out;
    }
</style>

<div class="container d-flex justify-content-center align-items-center" >
    <div class="glass-card text-center">
        <h4 class="text-danger">
            <i class="fa fa-exclamation-triangle warning-icon"></i> Confirm Deletion
        </h4>

        <div class="alert alert-warning mt-3" role="alert">
            <p>Are you sure you want to delete <strong>{{ object }}</strong>?</p>
            <p class="text-danger"><strong>All associated data will be permanently removed!</strong></p>

            {% if related_data %}
            <div class="mt-3">
                <p><strong>The following related data will also be deleted:</strong></p>
                <ul class="text-start">
                    {% if related_data.fee_payments > 0 %}
                    <li>{{ related_data.fee_payments }} Fee Payment record(s)</li>
                    {% endif %}

                    {% if related_data.pending_fees > 0 %}
                    <li>{{ related_data.pending_fees }} Pending Fee record(s)</li>
                    {% endif %}

                    {% if related_data.admit_cards > 0 %}
                    <li>{{ related_data.admit_cards }} Admit Card(s)</li>
                    {% endif %}

                    {% if related_data.exam_attendance > 0 %}
                    <li>{{ related_data.exam_attendance }} Exam Attendance record(s)</li>
                    {% endif %}

                    {% if related_data.marks > 0 %}
                    <li>{{ related_data.marks }} Mark record(s)</li>
                    {% endif %}

                    {% if related_data.attendance > 0 %}
                    <li>{{ related_data.attendance }} Attendance record(s)</li>
                    {% endif %}

                    {% if related_data.documents > 0 %}
                    <li>{{ related_data.documents }} Document record(s)</li>
                    {% endif %}
                </ul>
            </div>
            {% endif %}
        </div>

        <form action="" method="POST">
            {% csrf_token %}
            <div class="d-flex justify-content-center gap-3 mt-3">
                <a href="{% url 'student-list' %}" class="btn btn-secondary">
                    <i class="fa fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fa fa-trash-alt"></i> Confirm Delete
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock content %}
