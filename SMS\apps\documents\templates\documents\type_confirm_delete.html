{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Document Type{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:type_list' %}">Document Types</a></li>
        <li class="breadcrumb-item active" aria-current="page">Delete Document Type</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-md-8 mx-auto">
      <div class="card shadow-sm">
        <div class="card-header bg-danger text-white">
          <h4 class="mb-0">Delete Document Type</h4>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <h5 class="alert-heading">Warning!</h5>
            <p>Are you sure you want to delete the document type "<strong>{{ object.name }}</strong>"?</p>
            <p>This action cannot be undone. All documents associated with this type will also be affected.</p>
            {% if object.documents.count > 0 %}
            <p class="mb-0"><strong>This document type has {{ object.documents.count }} document(s) associated with it.</strong></p>
            {% endif %}
          </div>
          
          <div class="card mb-4">
            <div class="card-header bg-light">
              <h5 class="mb-0">Document Type Information</h5>
            </div>
            <div class="card-body">
              <p><strong>Name:</strong> {{ object.name }}</p>
              <p><strong>Entity Type:</strong> {{ object.get_entity_type_display }}</p>
              <p><strong>Category:</strong> {{ object.category.name }}</p>
              <p><strong>Required:</strong> {% if object.required %}Yes{% else %}No{% endif %}</p>
              {% if object.description %}
              <p><strong>Description:</strong> {{ object.description }}</p>
              {% endif %}
            </div>
          </div>
          
          <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-end">
              <a href="{% url 'documents:type_list' %}" class="btn btn-outline-secondary me-2">Cancel</a>
              <button type="submit" class="btn btn-danger">
                <i class="fas fa-trash me-2"></i> Confirm Delete
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
