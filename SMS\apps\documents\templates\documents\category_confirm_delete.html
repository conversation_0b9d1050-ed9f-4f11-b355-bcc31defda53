{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Category{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:category_list' %}">Document Categories</a></li>
        <li class="breadcrumb-item active" aria-current="page">Delete Category</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-md-8 mx-auto">
      <div class="card shadow-sm">
        <div class="card-header bg-danger text-white">
          <h4 class="mb-0">Delete Category</h4>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <h5 class="alert-heading">Warning!</h5>
            <p>Are you sure you want to delete the category "<strong>{{ object.name }}</strong>"?</p>
            <p>This action cannot be undone. All document types associated with this category will also be deleted.</p>
            {% if object.document_types.count > 0 %}
            <p class="mb-0"><strong>This category has {{ object.document_types.count }} document type(s) associated with it.</strong></p>
            {% endif %}
          </div>
          
          <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-end">
              <a href="{% url 'documents:category_list' %}" class="btn btn-outline-secondary me-2">Cancel</a>
              <button type="submit" class="btn btn-danger">
                <i class="fas fa-trash me-2"></i> Confirm Delete
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
