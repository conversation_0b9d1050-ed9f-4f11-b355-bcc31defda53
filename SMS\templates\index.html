{% extends 'base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-tachometer-alt"></i> Dashboard
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-tachometer-alt{% endblock title-icon %}

{% block title %}Dashboard{% endblock title %}

{% block subtitle %}Welcome to the school management system dashboard{% endblock subtitle %}

{% block page-actions %}
<a href="{% url 'current-session' %}" class="btn btn-sm btn-outline-primary">
  <i class="fas fa-calendar-alt"></i> Current Session: {{ current_session }}
</a>
{% endblock page-actions %}

{% block content %}
<div class="container mt-0">
  {% comment %} <h2 class="text-center mb-4">Admin Dashboard</h2>/ {% endcomment %}
  <div class="row g-4">
      <!-- Dashboard Cards -->
      <div class="col-md-3">
          <div class="dashboard-card text-center shadow p-3 bg-white rounded">
              <div class="dashboard-card-icon bg-primary">
                  <i class="fas fa-user-graduate"></i>
              </div>
              <h5>Total Students</h5>
              <h3 class="text-primary">{{ total_students }}</h3>
              <p class="text-muted">Active Students</p>
          </div>
      </div>
      <div class="col-md-3">
          <div class="dashboard-card text-center shadow p-3 bg-white rounded">
              <div class="dashboard-card-icon bg-success">
                  <i class="fas fa-chalkboard-teacher"></i>
              </div>
              <h5>Total Teachers</h5>
              <h3 class="text-success">{{ total_teachers }}</h3>
              <p class="text-muted">Teaching Staff</p>
          </div>
      </div>
      <div class="col-md-3">
          <div class="dashboard-card text-center shadow p-3 bg-white rounded">
              <div class="dashboard-card-icon bg-warning">
                  <i class="fas fa-money-bill-wave"></i>
              </div>
              <h5>Fees Collected</h5>
              <h3 class="text-warning">₹{{ fees_collected_month|floatformat:0|intcomma }}</h3>
              <p class="text-muted">This Month</p>
          </div>
      </div>
      <div class="col-md-3">
          <div class="dashboard-card text-center shadow p-3 bg-white rounded">
              <div class="dashboard-card-icon bg-danger">
                  <i class="fas fa-exclamation-circle"></i>
              </div>
              <h5>Pending Dues</h5>
              <h3 class="text-danger">₹{{ total_pending|floatformat:0|intcomma }}</h3>
              <p class="text-muted">Unpaid Fees</p>
          </div>
      </div>
  </div>

  <!-- Charts Section -->
  <div class="row mt-4 g-4">
      <div class="col-md-6">
          <div class="chart-container shadow p-3 bg-white rounded">
              <div class="d-flex justify-content-between align-items-center mb-3">
                  <h5 class="mb-0"><i class="fas fa-chart-line text-primary me-2"></i> Attendance Rate</h5>
                  <div class="chart-actions">
                      <button class="btn btn-sm btn-outline-primary active" id="attendanceLineBtn" onclick="changeChartType('attendanceChart', 'line')"><i class="fas fa-chart-line"></i></button>
                      <button class="btn btn-sm btn-outline-primary" id="attendanceBarBtn" onclick="changeChartType('attendanceChart', 'bar')"><i class="fas fa-chart-bar"></i></button>
                  </div>
              </div>
              <div class="chart-wrapper">
                  <canvas id="attendanceChart"></canvas>
              </div>
          </div>
      </div>
      <div class="col-md-6">
          <div class="chart-container shadow p-3 bg-white rounded">
              <div class="d-flex justify-content-between align-items-center mb-3">
                  <h5 class="mb-0"><i class="fas fa-chart-bar text-success me-2"></i> Fees Collection</h5>
                  <div class="chart-actions">
                      <button class="btn btn-sm btn-outline-success active" id="feesBarBtn" onclick="changeChartType('feesChart', 'bar')"><i class="fas fa-chart-bar"></i></button>
                      <button class="btn btn-sm btn-outline-success" id="feesLineBtn" onclick="changeChartType('feesChart', 'line')"><i class="fas fa-chart-line"></i></button>
                  </div>
              </div>
              <div class="chart-wrapper">
                  <canvas id="feesChart"></canvas>
              </div>
          </div>
      </div>
  </div>

  <!-- Recent Activities -->
  <div class="row mt-4">
      <div class="col-md-6">
          <div class="recent-activities shadow p-3 bg-white rounded h-100">
              <div class="d-flex justify-content-between align-items-center mb-3">
                  <h4 class="mb-0"><i class="fas fa-bell text-primary me-2"></i> Recent Admissions</h4>
                  <a href="{% url 'student-list' %}" class="btn btn-sm btn-outline-primary">View All</a>
              </div>
              <div class="table-responsive">
                  <table class="table table-hover">
                      <thead>
                          <tr>
                              <th>S/N</th>
                              <th>Student Name</th>
                              <th>Class</th>
                              <th>Date</th>
                          </tr>
                      </thead>
                      <tbody>
                          {% for student in recent_students %}
                          <tr>
                              <td>{{ forloop.counter }}</td>
                              <td>
                                  <a href="{% url 'student-detail' student.id %}" class="text-decoration-none">
                                      {{ student.fullname }}
                                  </a>
                              </td>
                              <td>{{ student.current_class }} {{ student.section }}</td>
                              <td>{{ student.date_of_admission|date:"d M, Y" }}</td>
                          </tr>
                          {% empty %}
                          <tr>
                              <td colspan="4" class="text-center">No recent admissions</td>
                          </tr>
                          {% endfor %}
                      </tbody>
                  </table>
              </div>
          </div>
      </div>
      <div class="col-md-6">
          <div class="recent-activities shadow p-3 bg-white rounded h-100">
              <div class="d-flex justify-content-between align-items-center mb-3">
                  <h4 class="mb-0"><i class="fas fa-money-bill-wave text-success me-2"></i> Recent Payments</h4>
                  <a href="{% url 'fees:fee_list' %}" class="btn btn-sm btn-outline-success">View All</a>
              </div>
              <div class="table-responsive">
                  <table class="table table-hover">
                      <thead>
                          <tr>
                              <th>S/N</th>
                              <th>Student</th>
                              <th>Amount</th>
                              <th>Date</th>
                          </tr>
                      </thead>
                      <tbody>
                          {% for payment in recent_payments %}
                          <tr>
                              <td>{{ forloop.counter }}</td>
                              <td>
                                  <a href="{% url 'student-detail' payment.student.id %}" class="text-decoration-none">
                                      {{ payment.student.fullname }}
                                  </a>
                              </td>
                              <td>₹{{ payment.amount|floatformat:0|intcomma }}</td>
                              <td>{{ payment.date|date:"d M, Y" }}</td>
                          </tr>
                          {% empty %}
                          <tr>
                              <td colspan="4" class="text-center">No recent payments</td>
                          </tr>
                          {% endfor %}
                      </tbody>
                  </table>
              </div>
          </div>
      </div>
  </div>

  <!-- Quick Stats Section -->
  <div class="row mt-4">
      <div class="col-md-6">
          <div class="top-performers shadow p-3 bg-white rounded h-100">
              <div class="d-flex justify-content-between align-items-center mb-3">
                  <h4 class="mb-0"><i class="fas fa-chart-pie text-info me-2"></i> Class Distribution</h4>
                  <a href="{% url 'classes' %}" class="btn btn-sm btn-outline-info">Manage Classes</a>
              </div>
              <div class="chart-wrapper">
                  <canvas id="classDistributionChart"></canvas>
              </div>
          </div>
      </div>
      <div class="col-md-6">
          <div class="top-performers shadow p-3 bg-white rounded h-100">
              <div class="d-flex justify-content-between align-items-center mb-3">
                  <h4 class="mb-0"><i class="fas fa-calendar-check text-purple me-2"></i> Upcoming Events</h4>
                  <button class="btn btn-sm btn-outline-purple" data-bs-toggle="modal" data-bs-target="#addEventModal">
                      <i class="fas fa-plus"></i> Add Event
                  </button>
              </div>
              <div class="upcoming-events">
                  <div class="event-card mb-3 p-3 border-start border-5 border-primary rounded bg-light">
                      <div class="d-flex justify-content-between">
                          <h5 class="mb-1">Parent-Teacher Meeting</h5>
                          <span class="badge bg-primary">15 Mar</span>
                      </div>
                      <p class="text-muted mb-0">Annual parent-teacher meeting for all classes</p>
                  </div>
                  <div class="event-card mb-3 p-3 border-start border-5 border-success rounded bg-light">
                      <div class="d-flex justify-content-between">
                          <h5 class="mb-1">Annual Sports Day</h5>
                          <span class="badge bg-success">25 Mar</span>
                      </div>
                      <p class="text-muted mb-0">Annual sports competition for all students</p>
                  </div>

              </div>
          </div>
      </div>
  </div>

  <!-- Quick Actions & System Status -->
  <div class="row mt-4">
      <div class="col-md-4">
          <div class="quick-actions shadow p-3 bg-white rounded h-100">
              <div class="d-flex justify-content-between align-items-center mb-3">
                  <h4 class="mb-0"><i class="fas fa-bolt text-warning me-2"></i> Quick Actions</h4>
              </div>
              <div class="row g-2">
                  <div class="col-6">
                      <a href="{% url 'student-create' %}" class="btn btn-light border w-100 py-3 text-start">
                          <i class="fas fa-user-plus text-primary"></i>
                          <span class="ms-2">New Admission</span>
                      </a>
                  </div>
                  <div class="col-6">
                      <a href="{% url 'fees:fee_list' %}" class="btn btn-light border w-100 py-3 text-start">
                          <i class="fas fa-money-bill-wave text-success"></i>
                          <span class="ms-2">Collect Fees</span>
                      </a>
                  </div>
                  <div class="col-6">
                      <a href="{% url 'attendance:attendance_list' %}" class="btn btn-light border w-100 py-3 text-start">
                          <i class="fas fa-clipboard-check text-info"></i>
                          <span class="ms-2">Attendance</span>
                      </a>
                  </div>

                  <div class="col-6">
                      <a href="{% url 'student-list' %}" class="btn btn-light border w-100 py-3 text-start">
                          <i class="fas fa-search text-secondary"></i>
                          <span class="ms-2">Find Student</span>
                      </a>
                  </div>
                  <div class="col-6">
                      <a href="{% url 'configs' %}" class="btn btn-light border w-100 py-3 text-start">
                          <i class="fas fa-cog text-dark"></i>
                          <span class="ms-2">Settings</span>
                      </a>
                  </div>
                  <div class="col-6">
                      <a href="{% url 'backup_restore' %}" class="btn btn-light border w-100 py-3 text-start">
                          <i class="fas fa-file-export text-purple"></i>
                          <span class="ms-2">Backup & Restore</span>
                      </a>
                  </div>
              </div>
          </div>
      </div>
      <div class="col-md-8">
          <div class="system-status shadow p-3 bg-white rounded h-100">
              <div class="d-flex justify-content-between align-items-center mb-3">
                  <h4 class="mb-0"><i class="fas fa-info-circle text-info me-2"></i> System Information</h4>
                  <span class="badge bg-success">System Online</span>
              </div>
              <div class="row g-3">
                  <div class="col-md-6">
                      <div class="card border-0 bg-light">
                          <div class="card-body">
                              <h6 class="card-title"><i class="fas fa-calendar me-2 text-primary"></i> Current Session</h6>
                              <p class="card-text fw-bold">{{ current_session }}</p>
                          </div>
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="card border-0 bg-light">
                          <div class="card-body">
                              <h6 class="card-title"><i class="fas fa-clock me-2 text-success"></i> Current Term</h6>
                              <p class="card-text fw-bold">{{ current_term }}</p>
                          </div>
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="card border-0 bg-light">
                          <div class="card-body">
                              <h6 class="card-title"><i class="fas fa-school me-2 text-warning"></i> School Name</h6>
                              <p class="card-text fw-bold">{{ profile.college_name|default:"MySchool" }}</p>
                          </div>
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="card border-0 bg-light">
                          <div class="card-body">
                              <h6 class="card-title"><i class="fas fa-user-tie me-2 text-danger"></i> Principal</h6>
                              <p class="card-text fw-bold">{{ profile.principal_name|default:"Principal" }}</p>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
      // Chart configuration
      Chart.defaults.font.family = "'Poppins', sans-serif";
      Chart.defaults.font.size = 12;
      Chart.defaults.plugins.tooltip.padding = 10;
      Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      Chart.defaults.plugins.tooltip.titleFont = { size: 14, weight: 'bold' };
      Chart.defaults.plugins.tooltip.bodyFont = { size: 13 };
      Chart.defaults.plugins.tooltip.displayColors = true;
      Chart.defaults.plugins.tooltip.usePointStyle = true;
      Chart.defaults.elements.point.radius = 4;
      Chart.defaults.elements.point.hoverRadius = 6;

      // Store chart instances
      window.charts = {};

      // Attendance Chart
      const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
      window.charts.attendanceChart = new Chart(attendanceCtx, {
          type: 'line',
          data: {
              labels: {{ attendance_months|safe }},
              datasets: [{
                  label: 'Attendance Rate (%)',
                  data: {{ attendance_data|safe }},
                  borderColor: '#007bff',
                  backgroundColor: 'rgba(0,123,255,0.2)',
                  fill: true,
                  tension: 0.4,
                  borderWidth: 2,
                  pointBackgroundColor: '#ffffff',
                  pointBorderColor: '#007bff',
                  pointBorderWidth: 2
              }]
          },
          options: {
              responsive: true,
              maintainAspectRatio: true,
              plugins: {
                  legend: { display: false },
                  tooltip: {
                      callbacks: {
                          label: function(context) {
                              return context.dataset.label + ': ' + context.parsed.y + '%';
                          }
                      }
                  }
              },
              scales: {
                  y: {
                      beginAtZero: true,
                      max: 100,
                      ticks: {
                          callback: function(value) {
                              return value + '%';
                          }
                      },
                      grid: {
                          drawBorder: false,
                          color: 'rgba(0, 0, 0, 0.05)'
                      }
                  },
                  x: {
                      grid: {
                          drawBorder: false,
                          display: false
                      }
                  }
              }
          }
      });

      // Fees Collection Chart
      const feesCtx = document.getElementById('feesChart').getContext('2d');
      const feeData = {{ fee_data|safe }};

      // Ensure fee data is properly formatted as numbers
      const formattedFeeData = feeData.map(value => parseFloat(value) || 0);

      window.charts.feesChart = new Chart(feesCtx, {
          type: 'bar',
          data: {
              labels: {{ months|safe }},
              datasets: [{
                  label: 'Fees Collected (₹)',
                  data: formattedFeeData,
                  backgroundColor: [
                      'rgba(40, 167, 69, 0.7)',  // success
                      'rgba(255, 193, 7, 0.7)',  // warning
                      'rgba(220, 53, 69, 0.7)',  // danger
                      'rgba(23, 162, 184, 0.7)', // info
                      'rgba(102, 16, 242, 0.7)', // purple
                      'rgba(111, 66, 193, 0.7)'  // indigo
                  ],
                  borderColor: [
                      'rgb(40, 167, 69)',
                      'rgb(255, 193, 7)',
                      'rgb(220, 53, 69)',
                      'rgb(23, 162, 184)',
                      'rgb(102, 16, 242)',
                      'rgb(111, 66, 193)'
                  ],
                  borderWidth: 1,
                  borderRadius: 4,
                  hoverOffset: 4
              }]
          },
          options: {
              responsive: true,
              maintainAspectRatio: true,
              plugins: {
                  legend: { display: false },
                  tooltip: {
                      callbacks: {
                          label: function(context) {
                              return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString('en-IN');
                          }
                      }
                  }
              },
              scales: {
                  y: {
                      beginAtZero: true,
                      ticks: {
                          callback: function(value) {
                              return '₹' + value.toLocaleString('en-IN');
                          }
                      },
                      grid: {
                          drawBorder: false,
                          color: 'rgba(0, 0, 0, 0.05)'
                      }
                  },
                  x: {
                      grid: {
                          drawBorder: false,
                          display: false
                      }
                  }
              }
          }
      });

      // Class Distribution Chart
      const classDistCtx = document.getElementById('classDistributionChart').getContext('2d');
      window.charts.classDistributionChart = new Chart(classDistCtx, {
          type: 'doughnut',
          data: {
              labels: {{ class_labels|safe }},
              datasets: [{
                  data: {{ class_data|safe }},
                  backgroundColor: [
                      'rgba(40, 167, 69, 0.7)',  // success
                      'rgba(0, 123, 255, 0.7)',  // primary
                      'rgba(255, 193, 7, 0.7)',  // warning
                      'rgba(220, 53, 69, 0.7)',  // danger
                      'rgba(23, 162, 184, 0.7)', // info
                      'rgba(108, 117, 125, 0.7)' // secondary
                  ],
                  borderColor: [
                      'rgb(40, 167, 69)',
                      'rgb(0, 123, 255)',
                      'rgb(255, 193, 7)',
                      'rgb(220, 53, 69)',
                      'rgb(23, 162, 184)',
                      'rgb(108, 117, 125)'
                  ],
                  borderWidth: 1,
                  hoverOffset: 4
              }]
          },
          options: {
              responsive: true,
              maintainAspectRatio: true,
              plugins: {
                  legend: {
                      position: 'right',
                      labels: {
                          boxWidth: 15,
                          padding: 15,
                          font: {
                              size: 12
                          }
                      }
                  },
                  tooltip: {
                      callbacks: {
                          label: function(context) {
                              const label = context.label || '';
                              const value = context.parsed || 0;
                              const total = context.dataset.data.reduce((acc, data) => acc + data, 0);
                              const percentage = Math.round((value / total) * 100);
                              return `${label}: ${value} students (${percentage}%)`;
                          }
                      }
                  }
              },
              cutout: '60%',
              radius: '90%'
          }
      });

      // Function to change chart type
      window.changeChartType = function(chartId, newType) {
          console.log('Changing chart type for:', chartId, 'to:', newType);

          try {
              // Get the chart instance
              const chart = window.charts[chartId];

              if (!chart) {
                  console.error('Chart not found:', chartId);
                  return;
              }

              // Save current data and options
              const data = chart.data;
              const options = chart.options;

              // Destroy current chart
              chart.destroy();

              // Create new chart with new type
              const ctx = document.getElementById(chartId).getContext('2d');
              window.charts[chartId] = new Chart(ctx, {
                  type: newType,
                  data: data,
                  options: options
              });

              // Update button states
              if (chartId === 'attendanceChart') {
                  if (newType === 'line') {
                      document.getElementById('attendanceLineBtn').classList.add('active');
                      document.getElementById('attendanceBarBtn').classList.remove('active');
                  } else {
                      document.getElementById('attendanceLineBtn').classList.remove('active');
                      document.getElementById('attendanceBarBtn').classList.add('active');
                  }
              } else if (chartId === 'feesChart') {
                  if (newType === 'bar') {
                      document.getElementById('feesBarBtn').classList.add('active');
                      document.getElementById('feesLineBtn').classList.remove('active');
                  } else {
                      document.getElementById('feesBarBtn').classList.remove('active');
                      document.getElementById('feesLineBtn').classList.add('active');
                  }
              }

              console.log('Chart changed successfully');
          } catch (error) {
              console.error('Error changing chart type:', error);
          }
      };
  });
</script>

{% endblock content %}
