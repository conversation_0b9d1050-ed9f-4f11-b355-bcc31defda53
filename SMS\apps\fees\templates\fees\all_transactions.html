{% extends 'base.html' %}
{% load static %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'fees:fee_list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-money-bill-wave"></i> Fee Management
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-exchange-alt"></i> All Transactions
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-exchange-alt{% endblock title-icon %}

{% block title %}All Fee Transactions{% endblock title %}

{% block subtitle %}View and manage all fee transactions in the system{% endblock subtitle %}

{% block page-actions %}
<a href="{% url 'fees:fee_list' %}" class="btn btn-outline-primary me-2">
  <i class="fas fa-arrow-left me-1"></i> Back to Fee Management
</a>
<button type="button" class="btn btn-primary" onclick="window.print();">
  <i class="fas fa-print me-1"></i> Print Report
</button>
{% endblock page-actions %}

{% block content %}
<div class="container-fluid py-3 fees-container">
    <!-- Stats Cards -->
    <div class="row mb-3">
        <div class="col-md-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Transactions</div>
                            <div class="h1 mb-0 font-weight-bold text-gray-800">{{ total_transactions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-receipt fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Amount Collected</div>
                            <div class="h1 mb-0 font-weight-bold text-gray-800">₹{{ total_amount|floatformat:2 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Current Date</div>
                            <div class="h3 mb-0 font-weight-bold text-gray-800">{{ now|date:"d M, Y" }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Card -->
    <div class="card shadow-sm mb-3">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Transactions</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-2">
                <div class="col-md-3">
                    <label class="form-label">Payment Method</label>
                    <select name="payment_method" class="form-select">
                        <option value="">All Methods</option>
                        {% for method_code, method_name in payment_methods %}
                            <option value="{{ method_code }}" {% if filter_params.payment_method == method_code %}selected{% endif %}>{{ method_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">All Statuses</option>
                        {% for status_code, status_name in payment_statuses %}
                            <option value="{{ status_code }}" {% if filter_params.status == status_code %}selected{% endif %}>{{ status_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">From Date</label>
                    <input type="date" name="date_from" class="form-control" value="{{ filter_params.date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">To Date</label>
                    <input type="date" name="date_to" class="form-control" value="{{ filter_params.date_to }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Class</label>
                    <select name="class_name" class="form-select" id="class-filter">
                        <option value="">-- All Classes --</option>
                        {% for class_obj in classes %}
                            <option value="{{ class_obj.id }}" {% if filter_params.class_name == class_obj.id|stringformat:'i' %}selected{% endif %}>{{ class_obj.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Section</label>
                    <select name="section" class="form-select" id="section-filter">
                        <option value="">-- All Sections --</option>
                        {% if filter_params.section %}
                            <option value="{{ filter_params.section }}" selected>{{ filter_params.section }}</option>
                        {% endif %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Fee Category</label>
                    <input type="text" name="fee_category" class="form-control" placeholder="e.g. Regular, Special" value="{{ filter_params.fee_category }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <input type="text" name="search" class="form-control" placeholder="Student name, registration number, transaction ID..." value="{{ filter_params.search }}">
                </div>
                <div class="col-md-12 text-end mt-3">
                    <a href="{% url 'fees:all_transactions' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-redo me-1"></i> Reset Filters
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Transaction List</h5>
            <span class="badge bg-primary rounded-pill px-3 py-2">{{ page_obj.paginator.count }} Results</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-primary">
                        <tr>
                            <th width="5%"><i class="fas fa-hashtag me-1"></i> S/N</th>
                            <th width="15%"><i class="fas fa-user-graduate me-1"></i> Student</th>
                            <th width="15%"><i class="fas fa-tag me-1"></i> Transaction Details</th>
                            <th width="10%"><i class="fas fa-money-bill-wave me-1"></i> Amount</th>
                            <th width="15%"><i class="fas fa-calendar-alt me-1"></i> Date & Method</th>
                            <th width="10%"><i class="fas fa-check-circle me-1"></i> Status</th>
                            <th width="10%"><i class="fas fa-info-circle me-1"></i> Category</th>
                            <th width="20%"><i class="fas fa-cogs me-1"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in page_obj %}
                        <tr>
                            <td class="text-center">{{ forloop.counter0|add:page_obj.start_index }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-{% if payment.status == 'Paid' %}success{% else %}warning{% endif %} text-white me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; font-size: 16px;">
                                        {{ payment.student.fullname|slice:":1" }}
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ payment.student.fullname }}</h6>
                                        <small class="text-muted">{{ payment.student.current_class }} {{ payment.student.section }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="d-block fw-bold">ID: {{ payment.id }}</span>
                                {% if payment.transaction_id %}
                                <small class="text-muted d-block">Trans ID: {{ payment.transaction_id }}</small>
                                {% endif %}
                            </td>
                            <td class="fw-bold text-{% if payment.status == 'Paid' %}success{% else %}warning{% endif %}">
                                ₹{{ payment.amount|floatformat:2 }}
                            </td>
                            <td>
                                <span class="d-block">{{ payment.date|date:"d M, Y" }}</span>
                                <small class="badge bg-secondary">{{ payment.payment_method }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{% if payment.status == 'Paid' %}success{% else %}warning{% endif %} px-3 py-2">
                                    {{ payment.status }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info px-3 py-2">
                                    {{ payment.fee_category }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'fees:generate_receipt' payment.id %}?format=html" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i> View
                                    </a>
                                    <a href="{% url 'fees:generate_receipt' payment.id %}" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-download me-1"></i> Receipt
                                    </a>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="{% url 'student-detail' pk=payment.student.id %}"><i class="fas fa-user me-2"></i>Student Profile</a></li>
                                            <li><a class="dropdown-item" href="{% url 'fees:student_fee_history' student_id=payment.student.id %}"><i class="fas fa-history me-2"></i>Payment History</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="py-5">
                                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                    <h5>No Transactions Found</h5>
                                    <p class="text-muted">No fee payment transactions match your filter criteria.</p>
                                    <a href="{% url 'fees:all_transactions' %}" class="btn btn-primary mt-2"><i class="fas fa-sync-alt me-2"></i>Clear Filters</a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        {% if page_obj.paginator.num_pages > 1 %}
        <div class="card-footer bg-light">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% for key, value in filter_params.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in filter_params.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item"><a class="page-link" href="?page={{ num }}{% for key, value in filter_params.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a></li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in filter_params.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in filter_params.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<style>
    /* Dashboard Card Styles */
    .card {
        transition: all 0.3s ease;
        border-radius: 5px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        opacity: 1;
        transform: translateY(0);
        margin-bottom: 10px !important;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.25) !important;
    }

    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }

    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }

    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }

    .text-primary {
        color: #4e73df !important;
    }

    .text-success {
        color: #1cc88a !important;
    }

    .text-info {
        color: #36b9cc !important;
    }

    .text-gray-800 {
        color: #5a5c69 !important;
    }

    .text-xs {
        font-size: 0.7rem;
    }

    .font-weight-bold {
        font-weight: 700 !important;
    }

    .no-gutters {
        margin-right: 0;
        margin-left: 0;
    }

    .no-gutters > .col,
    .no-gutters > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
    }

    @media print {
        .card {
            border: none !important;
            box-shadow: none !important;
            margin-bottom: 5px !important;
        }

        .card-header, .btn, .page-item, .badge {
            display: none !important;
        }

        .table-responsive {
            overflow-x: visible !important;
        }

        .card-body {
            padding: 0 !important;
        }

        body {
            font-size: 12px !important;
        }

        .container-fluid {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
        }

        .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        .col, [class*="col-"] {
            padding-left: 2px !important;
            padding-right: 2px !important;
        }
    }
</style>
{% endblock content %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation effects for cards
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 * index);
        });

        // Add counter animation for numbers
        const animateCounter = (element, target, duration = 1500) => {
            let start = 0;
            const increment = target / (duration / 16);
            const timer = setInterval(() => {
                start += increment;
                if (start >= target) {
                    clearInterval(timer);
                    element.textContent = target.toLocaleString();
                } else {
                    element.textContent = Math.floor(start).toLocaleString();
                }
            }, 16);
        };

        // Animate transaction count
        const transactionCount = document.querySelector('.border-left-primary .h1');
        if (transactionCount) {
            const targetValue = parseInt(transactionCount.textContent.replace(/,/g, ''));
            transactionCount.textContent = '0';
            animateCounter(transactionCount, targetValue);
        }

        // Animate total amount
        const totalAmount = document.querySelector('.border-left-success .h1');
        if (totalAmount) {
            const amountText = totalAmount.textContent;
            const currencySymbol = amountText.charAt(0);
            const targetValue = parseFloat(amountText.substring(1).replace(/,/g, ''));
            totalAmount.textContent = currencySymbol + '0';

            // Custom animation for currency values
            let start = 0;
            const increment = targetValue / (1500 / 16);
            const timer = setInterval(() => {
                start += increment;
                if (start >= targetValue) {
                    clearInterval(timer);
                    totalAmount.textContent = currencySymbol + targetValue.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                } else {
                    totalAmount.textContent = currencySymbol + start.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                }
            }, 16);
        }

        // Class-section filter functionality is handled by the global class-section-filter.js script
    });
</script>
{% endblock extra_js %}
