# Generated by Django 5.1.3 on 2025-07-21 04:51

from django.db import migrations
import random


def populate_registration_numbers(apps, schema_editor):
    Staff = apps.get_model('staffs', 'Staff')

    for index, staff in enumerate(Staff.objects.filter(registration_number__isnull=True), start=1):
        # Generate unique registration number
        year_suffix = "25"  # Default year
        if staff.date_of_registration:
            year_suffix = str(staff.date_of_registration.year)[-2:]

        # Create unique number based on index to avoid duplicates
        unique_number = f"{index:04d}"  # 4-digit number with leading zeros
        registration_number = f"{year_suffix}{unique_number}"

        staff.registration_number = registration_number
        staff.save()


def reverse_populate_registration_numbers(apps, schema_editor):
    # This is a data migration, so we don't need to reverse it
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('staffs', '0021_remove_staff_user_staff_registration_number'),
    ]

    operations = [
        migrations.RunPython(populate_registration_numbers, reverse_populate_registration_numbers),
    ]
