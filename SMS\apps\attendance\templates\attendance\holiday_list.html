{% extends 'base.html' %}
{% load widget_tweaks %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'attendance:attendance_list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-calendar-check"></i> Attendance
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-calendar-minus"></i> Holiday Management
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-calendar-minus{% endblock title-icon %}

{% block title %}Holiday Management{% endblock title %}

{% block subtitle %}Configure and manage holidays for attendance tracking{% endblock subtitle %}

{% block page-actions %}
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addHolidayModal">
  <i class="fas fa-plus-circle me-2"></i> Add New Holiday
</button>
{% endblock page-actions %}


{% block content %}
<div class="container mt-4">
 

  <!-- Holiday Stats -->
  <div class="row g-3 mb-4">
    <div class="col-md-3">
      <div class="card border-0 h-100">
        <div class="card-body p-0">
          <div class="p-4 rounded-3 shadow-sm h-100 d-flex flex-column justify-content-between" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white;">
            <div class="d-flex justify-content-between">
              <h5 class="mb-0">Total Holidays</h5>
              <div class="rounded-circle bg-white p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                <i class="fas fa-calendar-minus text-info"></i>
              </div>
            </div>
            <div>
              <p class="display-5 mb-0">{{ holidays.count }}</p>
              <small class="text-white-50">Configured holidays</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 h-100">
        <div class="card-body p-0">
          <div class="p-4 rounded-3 shadow-sm h-100 d-flex flex-column justify-content-between" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white;">
            <div class="d-flex justify-content-between">
              <h5 class="mb-0">Sundays</h5>
              <div class="rounded-circle bg-white p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                <i class="fas fa-calendar-day text-secondary"></i>
              </div>
            </div>
            <div>
              <p class="display-5 mb-0">52</p>
              <small class="text-white-50">Auto-managed</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 h-100">
        <div class="card-body p-0">
          <div class="p-4 rounded-3 shadow-sm h-100 d-flex flex-column justify-content-between" style="background: linear-gradient(135deg, #28a745 0%, #218838 100%); color: white;">
            <div class="d-flex justify-content-between">
              <h5 class="mb-0">Upcoming</h5>
              <div class="rounded-circle bg-white p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                <i class="fas fa-calendar-alt text-success"></i>
              </div>
            </div>
            <div>
              <p class="display-5 mb-0">{{ upcoming_holidays.count }}</p>
              <small class="text-white-50">Next 30 days</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 h-100">
        <div class="card-body p-0">
          <div class="p-4 rounded-3 shadow-sm h-100 d-flex flex-column justify-content-between" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white;">
            <div class="d-flex justify-content-between">
              <h5 class="mb-0">Past</h5>
              <div class="rounded-circle bg-white p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                <i class="fas fa-history text-danger"></i>
              </div>
            </div>
            <div>
              <p class="display-5 mb-0">{{ past_holidays.count }}</p>
              <small class="text-white-50">This year</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Holiday List -->
  <div class="card shadow-sm border-0 mb-4">
    <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
      <h5 class="mb-0"><i class="fas fa-list me-2 text-primary"></i>Holiday List</h5>
      <div class="d-flex align-items-center">
        <div class="search-box position-relative me-3">
          <input type="text" id="holiday-search" class="form-control form-control-sm" placeholder="Search holidays..." style="width: 200px; padding-left: 30px;">
          <i class="fas fa-search position-absolute" style="left: 10px; top: 9px; color: #aaa;"></i>
        </div>
        <div class="dropdown">
          <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-filter me-1"></i> Filter
          </button>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filterDropdown">
            <li><a class="dropdown-item" href="#" data-filter="all">All Holidays</a></li>
            <li><a class="dropdown-item" href="#" data-filter="upcoming">Upcoming Holidays</a></li>
            <li><a class="dropdown-item" href="#" data-filter="past">Past Holidays</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#" data-filter="this-month">This Month</a></li>
            <li><a class="dropdown-item" href="#" data-filter="next-month">Next Month</a></li>
          </ul>
        </div>
      </div>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table id="holiday-table" class="table table-hover mb-0">
          <thead class="table-light">
            <tr>
              <th class="text-center" style="width: 50px;">S/N</th>
              <th style="width: 120px;">Date</th>
              <th>Holiday Name</th>
              <th>Description</th>
              <th class="text-center" style="width: 150px;">Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for holiday in holidays %}
            <tr class="holiday-row {% if holiday.date < today %}past-holiday{% elif holiday.date > today %}upcoming-holiday{% else %}today-holiday{% endif %}">
              <td class="text-center fw-bold">{{ forloop.counter }}</td>
              <td>
                <div class="d-flex align-items-center">
                  <div class="calendar-icon me-2 text-center" style="min-width: 40px;">
                    <div class="bg-light rounded-top px-1" style="font-size: 10px; border: 1px solid #ddd; border-bottom: none;">
                      {{ holiday.date|date:"M" }}
                    </div>
                    <div class="bg-white rounded-bottom fw-bold" style="font-size: 16px; border: 1px solid #ddd; border-top: none;">
                      {{ holiday.date|date:"d" }}
                    </div>
                  </div>
                  <div>
                    <div class="fw-bold">{{ holiday.date|date:"l" }}</div>
                    <div class="small text-muted">{{ holiday.date|date:"Y" }}</div>
                    {% if holiday.date == today %}
                      <span class="badge bg-info rounded-pill">Today</span>
                    {% elif holiday.date < today %}
                      <span class="badge bg-secondary rounded-pill">Past</span>
                    {% else %}
                      <span class="badge bg-success rounded-pill">Upcoming</span>
                    {% endif %}
                  </div>
                </div>
              </td>
              <td>
                <span class="fw-bold">{{ holiday.name }}</span>
              </td>
              <td>
                {{ holiday.description|default:"No description provided" }}
              </td>
              <td class="text-center">
                <button type="button" class="btn btn-sm btn-outline-primary edit-holiday-btn"
                        data-id="{{ holiday.id }}"
                        data-date="{{ holiday.date|date:'Y-m-d' }}"
                        data-name="{{ holiday.name }}"
                        data-description="{{ holiday.description|default:'' }}">
                  <i class="fas fa-edit me-1"></i> Edit
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger delete-holiday-btn"
                        data-id="{{ holiday.id }}"
                        data-name="{{ holiday.name }}">
                  <i class="fas fa-trash-alt me-1"></i> Delete
                </button>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="5" class="text-center py-5">
                <div class="py-5">
                  <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                  <h5>No Holidays Found</h5>
                  <p class="text-muted">No holidays have been added yet.</p>
                  <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addHolidayModal">
                    <i class="fas fa-plus-circle me-1"></i> Add First Holiday
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Add Holiday Modal -->
  <div class="modal fade" id="addHolidayModal" tabindex="-1" aria-labelledby="addHolidayModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <form id="add-holiday-form" method="POST" action="{% url 'attendance:add_holiday' %}">
          {% csrf_token %}
          <div class="modal-header bg-info text-white">
            <h5 class="modal-title" id="addHolidayModalLabel"><i class="fas fa-plus-circle me-2"></i>Add New Holiday</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="holiday-date" class="form-label">Holiday Date <span class="text-danger">*</span></label>
              <input type="date" class="form-control" id="holiday-date" name="date" required>
              <div class="form-text">Select the date for this holiday.</div>
            </div>
            <div class="mb-3">
              <label for="holiday-name" class="form-label">Holiday Name <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="holiday-name" name="name" required>
              <div class="form-text">Enter the name or title of the holiday.</div>
            </div>
            <div class="mb-3">
              <label for="holiday-description" class="form-label">Description (Optional)</label>
              <textarea class="form-control" id="holiday-description" name="description" rows="3"></textarea>
              <div class="form-text">Provide additional details about this holiday if needed.</div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-info text-white">
              <i class="fas fa-save me-1"></i> Save Holiday
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Edit Holiday Modal -->
  <div class="modal fade" id="editHolidayModal" tabindex="-1" aria-labelledby="editHolidayModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <form id="edit-holiday-form" method="POST" action="{% url 'attendance:edit_holiday' %}">
          {% csrf_token %}
          <input type="hidden" id="edit-holiday-id" name="holiday_id">
          <div class="modal-header bg-primary text-white">
            <h5 class="modal-title" id="editHolidayModalLabel"><i class="fas fa-edit me-2"></i>Edit Holiday</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="edit-holiday-date" class="form-label">Holiday Date <span class="text-danger">*</span></label>
              <input type="date" class="form-control" id="edit-holiday-date" name="date" required>
              <div class="form-text">Update the date for this holiday.</div>
            </div>
            <div class="mb-3">
              <label for="edit-holiday-name" class="form-label">Holiday Name <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="edit-holiday-name" name="name" required>
              <div class="form-text">Update the name or title of the holiday.</div>
            </div>
            <div class="mb-3">
              <label for="edit-holiday-description" class="form-label">Description (Optional)</label>
              <textarea class="form-control" id="edit-holiday-description" name="description" rows="3"></textarea>
              <div class="form-text">Update additional details about this holiday if needed.</div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save me-1"></i> Update Holiday
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Delete Holiday Modal -->
  <div class="modal fade" id="deleteHolidayModal" tabindex="-1" aria-labelledby="deleteHolidayModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <form id="delete-holiday-form" method="POST" action="{% url 'attendance:delete_holiday' %}">
          {% csrf_token %}
          <input type="hidden" id="delete-holiday-id" name="holiday_id">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="deleteHolidayModalLabel"><i class="fas fa-trash-alt me-2"></i>Delete Holiday</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="text-center mb-4">
              <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
              <h5>Are you sure you want to delete this holiday?</h5>
              <p class="text-muted" id="delete-holiday-name-display"></p>
              <div class="alert alert-warning">
                <i class="fas fa-info-circle me-2"></i> This action cannot be undone. Any attendance records associated with this holiday will remain but will no longer be marked as holidays.
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-danger">
              <i class="fas fa-trash-alt me-1"></i> Delete Holiday
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Django Messages -->
  {% if messages %}
  <div class="messages-container mb-4">
    {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
      {% if message.tags == 'success' %}
        <i class="fas fa-check-circle me-2"></i>
      {% elif message.tags == 'error' or message.tags == 'danger' %}
        <i class="fas fa-exclamation-circle me-2"></i>
      {% elif message.tags == 'warning' %}
        <i class="fas fa-exclamation-triangle me-2"></i>
      {% elif message.tags == 'info' %}
        <i class="fas fa-info-circle me-2"></i>
      {% endif %}
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endfor %}
  </div>
  {% endif %}
</div>
{% endblock content %}

{% block morejs %}
<script>
  $(document).ready(function() {
    // Initialize DataTable with search functionality disabled (we'll use our own)
    $('#holiday-table').DataTable({
      "searching": false,
      "paging": false,
      "info": false,
      "order": [[1, 'asc']] // Sort by date column ascending
    });

    // Holiday search functionality
    $('#holiday-search').on('keyup', function() {
      const searchText = $(this).val().toLowerCase();
      $('.holiday-row').each(function() {
        const holidayName = $(this).find('td:nth-child(3)').text().toLowerCase();
        const holidayDate = $(this).find('td:nth-child(2)').text().toLowerCase();
        const holidayDesc = $(this).find('td:nth-child(4)').text().toLowerCase();

        if (holidayName.includes(searchText) || holidayDate.includes(searchText) || holidayDesc.includes(searchText)) {
          $(this).show();
        } else {
          $(this).hide();
        }
      });
    });

    // Filter dropdown functionality
    $('.dropdown-item').on('click', function(e) {
      e.preventDefault();
      const filter = $(this).data('filter');

      // Show all rows first
      $('.holiday-row').show();

      // Apply filter
      if (filter === 'upcoming') {
        $('.past-holiday').hide();
      } else if (filter === 'past') {
        $('.upcoming-holiday, .today-holiday').hide();
      } else if (filter === 'this-month') {
        // This would require additional logic to filter by current month
        // For now, we'll just show a message
        alert('This filter would show holidays for the current month');
      } else if (filter === 'next-month') {
        // This would require additional logic to filter by next month
        // For now, we'll just show a message
        alert('This filter would show holidays for the next month');
      }

      // Update dropdown button text
      $('#filterDropdown').html('<i class="fas fa-filter me-1"></i> ' + $(this).text());
    });

    // Edit holiday button click
    $('.edit-holiday-btn').on('click', function() {
      const id = $(this).data('id');
      const date = $(this).data('date');
      const name = $(this).data('name');
      const description = $(this).data('description');

      // Populate the edit form
      $('#edit-holiday-id').val(id);
      $('#edit-holiday-date').val(date);
      $('#edit-holiday-name').val(name);
      $('#edit-holiday-description').val(description);

      // Show the modal
      $('#editHolidayModal').modal('show');
    });

    // Delete holiday button click
    $('.delete-holiday-btn').on('click', function() {
      const id = $(this).data('id');
      const name = $(this).data('name');

      // Populate the delete form
      $('#delete-holiday-id').val(id);
      $('#delete-holiday-name-display').text(name);

      // Show the modal
      $('#deleteHolidayModal').modal('show');
    });

    // Add loading indicators to form submissions
    $('#add-holiday-form, #edit-holiday-form, #delete-holiday-form').on('submit', function() {
      // Show loading state
      const submitBtn = $(this).find('button[type="submit"]');
      const originalBtnText = submitBtn.html();
      submitBtn.html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Processing...');
      submitBtn.prop('disabled', true);

      // The form will submit normally (not via AJAX)
      return true;
    });

    // Date validation for add holiday form
    $('#holiday-date').on('change', function() {
      const selectedDate = new Date($(this).val());
      const today = new Date();

      // Check if the selected date is a Sunday
      if (selectedDate.getDay() === 0) { // 0 is Sunday in JavaScript
        alert('Note: Sundays are automatically treated as holidays in the system. You do not need to add them manually.');
      }
    });
  });
</script>
{% endblock morejs %}
