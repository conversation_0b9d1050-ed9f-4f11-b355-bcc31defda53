{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-graduation-cap"></i> Examination
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'exams:question_paper_list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-file-alt"></i> Question Papers
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-{% if object %}edit{% else %}upload{% endif %}"></i> 
        {% if object %}Edit{% else %}Upload{% endif %} Question Paper
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-{% if object %}edit{% else %}upload{% endif %}{% endblock title-icon %}

{% block title %}{% if object %}Edit{% else %}Upload{% endif %} Question Paper{% endblock title %}

{% block subtitle %}{% if object %}Update existing{% else %}Upload new{% endif %} question paper{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-lg-10 mx-auto">
      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">
            <i class="fas fa-{% if object %}edit{% else %}upload{% endif %} me-2"></i>
            {% if object %}Edit{% else %}Upload{% endif %} Question Paper
          </h5>
        </div>
        <div class="card-body">
          <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="row mb-4">
              <div class="col-md-12">
                <div class="alert alert-info d-flex align-items-center">
                  <i class="fas fa-info-circle fa-2x me-3"></i>
                  <div>
                    <h5 class="alert-heading mb-1">Important Information</h5>
                    <p class="mb-0">Upload question papers in PDF, DOC, or DOCX format. Maximum file size is 10MB. For auto-generation, upload an Excel file with questions.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.exam.id_for_label }}" class="form-label">Exam <span class="text-danger">*</span></label>
                {{ form.exam|add_class:"form-select" }}
                {% if form.exam.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.exam.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-6">
                <label for="{{ form.subject.id_for_label }}" class="form-label">Subject <span class="text-danger">*</span></label>
                {{ form.subject|add_class:"form-select" }}
                {% if form.subject.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.subject.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.student_class.id_for_label }}" class="form-label">Class <span class="text-danger">*</span></label>
                {{ form.student_class|add_class:"form-select" }}
                {% if form.student_class.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.student_class.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-6">
                <label for="{{ form.section.id_for_label }}" class="form-label">Section</label>
                {{ form.section|add_class:"form-control" }}
                {% if form.section.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.section.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
                <div class="form-text">Leave blank if applicable to all sections</div>
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.total_marks.id_for_label }}" class="form-label">Total Marks <span class="text-danger">*</span></label>
                {{ form.total_marks|add_class:"form-control" }}
                {% if form.total_marks.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.total_marks.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-6">
                <label for="{{ form.passing_marks.id_for_label }}" class="form-label">Passing Marks <span class="text-danger">*</span></label>
                {{ form.passing_marks|add_class:"form-control" }}
                {% if form.passing_marks.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.passing_marks.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="{{ form.generation_type.id_for_label }}" class="form-label">Generation Type <span class="text-danger">*</span></label>
                {{ form.generation_type|add_class:"form-select" }}
                {% if form.generation_type.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.generation_type.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              <div class="col-md-6">
                <label for="{{ form.created_by.id_for_label }}" class="form-label">Created By</label>
                {{ form.created_by|add_class:"form-select" }}
                {% if form.created_by.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.created_by.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
                <div class="form-text">Leave blank to use current user</div>
              </div>
            </div>
            
            <div class="row mb-4">
              <div class="col-md-12">
                <label for="{{ form.file.id_for_label }}" class="form-label">Question Paper File {% if not object %}<span class="text-danger">*</span>{% endif %}</label>
                <div class="input-group">
                  {{ form.file|add_class:"form-control" }}
                  {% if object and object.file %}
                  <a href="{{ object.file.url }}" class="btn btn-outline-secondary" target="_blank">
                    <i class="fas fa-eye me-1"></i> View Current
                  </a>
                  {% endif %}
                </div>
                {% if form.file.errors %}
                  <div class="text-danger small mt-1">
                    {% for error in form.file.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
                <div class="form-text">
                  {% if object %}
                  Upload a new file only if you want to replace the existing one.
                  {% else %}
                  Upload question paper in PDF, DOC, DOCX format or Excel file for auto-generation.
                  {% endif %}
                </div>
              </div>
            </div>
            
            <hr class="my-4">
            
            <div class="d-flex justify-content-between">
              <a href="{% url 'exams:question_paper_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to List
              </a>
              <div>
                <button type="reset" class="btn btn-light me-2">
                  <i class="fas fa-redo me-1"></i> Reset
                </button>
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-1"></i> {% if object %}Update{% else %}Upload{% endif %}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block morejs %}
<script>
  $(document).ready(function() {
    // Show/hide fields based on generation type
    $('#{{ form.generation_type.id_for_label }}').on('change', function() {
      var type = $(this).val();
      if (type === 'auto') {
        $('#fileHelpText').text('Upload Excel file with questions for auto-generation.');
      } else {
        $('#fileHelpText').text('Upload question paper in PDF, DOC, or DOCX format.');
      }
    });
    
    // Validate passing marks is less than total marks
    $('#{{ form.total_marks.id_for_label }}, #{{ form.passing_marks.id_for_label }}').on('change', function() {
      var totalMarks = parseInt($('#{{ form.total_marks.id_for_label }}').val()) || 0;
      var passingMarks = parseInt($('#{{ form.passing_marks.id_for_label }}').val()) || 0;
      
      if (passingMarks > totalMarks) {
        alert('Passing marks cannot be greater than total marks.');
        $('#{{ form.passing_marks.id_for_label }}').val(Math.floor(totalMarks * 0.4)); // Set to 40% by default
      }
    });
    
    // Trigger initial change event
    $('#{{ form.generation_type.id_for_label }}').trigger('change');
  });
</script>
{% endblock morejs %}
