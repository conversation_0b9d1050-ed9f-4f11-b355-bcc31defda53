{% extends 'base.html' %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'staff-list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-chalkboard-teacher"></i> Teaching Staffs
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'staff-list' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-list"></i> List
        </a>
      </li>
      <li class="breadcrumb-item active text-danger" aria-current="page">
        <i class="fas fa-trash-alt"></i> Delete
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title %} Delete Confirmation - {{ object }} {% endblock title %}

{% block content %}
<style>
    /* Glassmorphism effect */
    .glass-card {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        max-width: 500px;
    }

    /* Animated warning icon */
    .warning-icon {
        animation: pulse 1.5s infinite alternate;
        font-size: 24px;
    }

    @keyframes pulse {
        0% { transform: scale(1); color: #dc3545; }
        100% { transform: scale(1.2); color: #ff0000; }
    }

    /* Button hover effects */
    .btn-danger:hover {
        background-color: #c82333 !important;
        transform: scale(1.05);
        transition: 0.3s ease-in-out;
    }

    .btn-secondary:hover {
        background-color: #6c757d !important;
        transform: scale(1.05);
        transition: 0.3s ease-in-out;
    }
</style>

<div class="container d-flex justify-content-center align-items-center" >
    <div class="glass-card text-center">
        <h4 class="text-danger">
            <i class="bi bi-exclamation-triangle-fill warning-icon"></i> Confirm Deletion
        </h4>

        <div class="alert alert-warning mt-3" role="alert">
            <p>Are you sure you want to delete <strong>{{ object }}</strong>?</p>
            <p class="text-danger"><strong>All associated data will be permanently removed!</strong></p>
        </div>

        <form action="" method="POST">
            {% csrf_token %}
            <div class="d-flex justify-content-center gap-3 mt-3">
                <a href="{% url 'staff-list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Cancel
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="bi bi-trash"></i> Confirm Delete
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock content %}
