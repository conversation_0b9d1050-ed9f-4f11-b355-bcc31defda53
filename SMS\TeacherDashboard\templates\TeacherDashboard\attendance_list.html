{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-calendar-check"></i> Attendance
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-calendar-check{% endblock title-icon %}
{% block title %}Attendance Records{% endblock title %}
{% block subtitle %}View attendance records for your classes{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>Attendance Records</h5>
        </div>
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <a href="{% url 'teacher_attendance_mark' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Mark Attendance
              </a>
            </div>
          </div>
          
          {% if attendance_records %}
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead class="table-dark">
                  <tr>
                    <th>Date</th>
                    <th>Student</th>
                    <th>Class</th>
                    <th>Status</th>
                    <th>Marked By</th>
                  </tr>
                </thead>
                <tbody>
                  {% for record in attendance_records %}
                  <tr>
                    <td>{{ record.date|date:"d M Y" }}</td>
                    <td>{{ record.student.fullname }}</td>
                    <td>{{ record.student.current_class|default:"N/A" }}</td>
                    <td>
                      <span class="badge {% if record.status == 'present' %}bg-success{% else %}bg-danger{% endif %}">
                        {{ record.status|title }}
                      </span>
                    </td>
                    <td>{{ record.marked_by|default:"System" }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Attendance Records</h5>
              <p class="text-muted">No attendance records found.</p>
              <a href="{% url 'teacher_attendance_mark' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Mark Attendance
              </a>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
