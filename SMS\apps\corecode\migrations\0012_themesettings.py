# Generated by Django 5.2 on 2025-04-17 09:06

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("corecode", "0011_alter_academicsession_id_alter_academicterm_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ThemeSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "primary_color",
                    models.CharField(
                        choices=[
                            ("#1E3C72", "Default Blue"),
                            ("#2C3E50", "Midnight Blue"),
                            ("#27AE60", "Emerald Green"),
                            ("#E74C3C", "Pomegranate Red"),
                            ("#8E44AD", "Wisteria Purple"),
                            ("#D35400", "Pumpkin Orange"),
                        ],
                        default="#1E3C72",
                        max_length=7,
                    ),
                ),
                ("secondary_color", models.Char<PERSON>ield(default="#ffc107", max_length=7)),
                ("accent_color", models.Char<PERSON><PERSON>(default="#17a2b8", max_length=7)),
                ("sidebar_bg", models.Char<PERSON>ield(default="#1E3C72", max_length=7)),
                (
                    "primary_font",
                    models.CharField(
                        choices=[
                            ("Poppins", "Poppins"),
                            ("Roboto", "Roboto"),
                            ("Open Sans", "Open Sans"),
                            ("Lato", "Lato"),
                            ("Montserrat", "Montserrat"),
                        ],
                        default="Poppins",
                        max_length=50,
                    ),
                ),
                (
                    "secondary_font",
                    models.CharField(
                        choices=[
                            ("Poppins", "Poppins"),
                            ("Roboto", "Roboto"),
                            ("Open Sans", "Open Sans"),
                            ("Lato", "Lato"),
                            ("Montserrat", "Montserrat"),
                        ],
                        default="Roboto",
                        max_length=50,
                    ),
                ),
                ("custom_css", models.TextField(blank=True, null=True)),
                ("last_modified", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Theme Settings",
                "verbose_name_plural": "Theme Settings",
            },
        ),
    ]
