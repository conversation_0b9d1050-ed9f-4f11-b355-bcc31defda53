{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'home' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Home
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-tachometer-alt"></i> Teacher  Dashboard
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-tachometer-alt{% endblock title-icon %}

{% block title %} Teacher Dashboard{% endblock title %}

{% block subtitle %}Welcome to the school management system dashboard{% endblock subtitle %}

{% block page-actions %}
<a href="{% url 'current-session' %}" class="btn btn-sm btn-outline-primary">
  <i class="fas fa-calendar-alt"></i> Current Session: {{ current_session }}
</a>
{% endblock page-actions %}

{% block content %}
<div class="container mt-4">
  <div class="row mb-4">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="fas fa-chalkboard-teacher me-2"></i> Your Classes</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for class in teacher_classes %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ class.name }}
                <span class="badge bg-primary">{{ class.student_count }} Students</span>
              </li>
            {% empty %}
              <li class="list-group-item">No classes assigned.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="fas fa-tasks me-2"></i> Recent Assignments</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for assignment in teacher_assignments %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ assignment.title }}
                <span class="badge bg-secondary">Due: {{ assignment.due_date }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No assignments found.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i> Upcoming Events</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for event in teacher_events %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ event.title }}
                <span class="badge bg-info">{{ event.date }}</span>
              </li>
            {% empty %}
              <li class="list-group-item">No upcoming events.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i> Gradebook</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            {% for gradebook in teacher_gradebooks %}
              <li class="list-group-item d-flex justify-content-between align-items-center">
                {{ gradebook.class_name }}
                <span class="badge bg-success">{{ gradebook.entries_count }} Entries</span>
              </li>
            {% empty %}
              <li class="list-group-item">No gradebook entries.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}