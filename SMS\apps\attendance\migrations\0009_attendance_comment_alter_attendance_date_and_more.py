# Generated by Django 5.2 on 2025-04-15 07:11

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("attendance", "0008_alter_attendance_unique_together"),
        ("students", "0030_student_email_id"),
    ]

    operations = [
        migrations.AddField(
            model_name="attendance",
            name="comment",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="attendance",
            name="date",
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AlterUniqueTogether(
            name="attendance",
            unique_together={("student", "date")},
        ),
    ]
