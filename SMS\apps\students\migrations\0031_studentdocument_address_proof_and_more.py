# Generated by Django 5.2 on 2025-05-01 09:54

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("students", "0030_student_email_id"),
    ]

    operations = [
        migrations.AddField(
            model_name="studentdocument",
            name="address_proof",
            field=models.FileField(
                blank=True, null=True, upload_to="students/documents/address/"
            ),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="address_proof_number",
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="birth_certificate",
            field=models.FileField(
                blank=True, null=True, upload_to="students/documents/birth/"
            ),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="birth_certificate_number",
            field=models.CharField(blank=True, max_length=50),
        ),
    ]
