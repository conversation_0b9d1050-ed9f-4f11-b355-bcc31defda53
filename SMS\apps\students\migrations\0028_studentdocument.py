# Generated by Django 5.2 on 2025-04-10 11:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("students", "0027_merge_20250408_1214"),
    ]

    operations = [
        migrations.CreateModel(
            name="StudentDocument",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("document_number", models.CharField(blank=True, max_length=50)),
                (
                    "aadhar_card",
                    models.FileField(
                        blank=True, null=True, upload_to="students/documents/aadhar/"
                    ),
                ),
                (
                    "parent_photo",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="students/documents/parent_photo/",
                    ),
                ),
                (
                    "parent_id_proof",
                    models.FileField(
                        blank=True, null=True, upload_to="students/documents/parent_id/"
                    ),
                ),
                (
                    "previous_marksheet",
                    models.FileField(
                        blank=True, null=True, upload_to="students/documents/marksheet/"
                    ),
                ),
                (
                    "transfer_certificate",
                    models.FileField(
                        blank=True, null=True, upload_to="students/documents/transfer/"
                    ),
                ),
                (
                    "character_certificate",
                    models.<PERSON>Field(
                        blank=True, null=True, upload_to="students/documents/character/"
                    ),
                ),
                (
                    "caste_certificate",
                    models.FileField(
                        blank=True, null=True, upload_to="students/documents/caste/"
                    ),
                ),
                (
                    "medical_certificate",
                    models.FileField(
                        blank=True, null=True, upload_to="students/documents/medical/"
                    ),
                ),
                (
                    "other_document",
                    models.FileField(
                        blank=True, null=True, upload_to="students/documents/other/"
                    ),
                ),
                ("date_uploaded", models.DateTimeField(auto_now_add=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="students.student",
                    ),
                ),
            ],
        ),
    ]
