{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_attendance_list' %}" class="text-decoration-none">
          <i class="fas fa-calendar-check"></i> Attendance
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-calendar-plus"></i> Mark Attendance
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-calendar-plus{% endblock title-icon %}
{% block title %}Mark Attendance{% endblock title %}
{% block subtitle %}Mark attendance for your students{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0"><i class="fas fa-calendar-plus me-2"></i>Mark Attendance</h5>
        </div>
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-md-6">
              <label for="attendance_date" class="form-label">Date:</label>
              <input type="date" class="form-control" id="attendance_date" value="{{ today|date:'Y-m-d' }}">
            </div>
            <div class="col-md-6">
              <label for="class_select" class="form-label">Class:</label>
              <select class="form-select" id="class_select">
                <option value="">Select Class</option>
                <option value="1">Class 1</option>
                <option value="2">Class 2</option>
                <!-- Add more classes as needed -->
              </select>
            </div>
          </div>
          
          {% if students %}
            <form method="post" action="#">
              {% csrf_token %}
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead class="table-dark">
                    <tr>
                      <th>Roll No.</th>
                      <th>Student Name</th>
                      <th>Present</th>
                      <th>Absent</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for student in students %}
                    <tr>
                      <td>{{ student.registration_number|default:"N/A" }}</td>
                      <td>{{ student.fullname }}</td>
                      <td>
                        <div class="form-check">
                          <input class="form-check-input" type="radio" name="attendance_{{ student.id }}" value="present" id="present_{{ student.id }}">
                          <label class="form-check-label text-success" for="present_{{ student.id }}">
                            <i class="fas fa-check"></i> Present
                          </label>
                        </div>
                      </td>
                      <td>
                        <div class="form-check">
                          <input class="form-check-input" type="radio" name="attendance_{{ student.id }}" value="absent" id="absent_{{ student.id }}">
                          <label class="form-check-label text-danger" for="absent_{{ student.id }}">
                            <i class="fas fa-times"></i> Absent
                          </label>
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
              
              <div class="row mt-4">
                <div class="col-12">
                  <button type="submit" class="btn btn-success me-2">
                    <i class="fas fa-save"></i> Save Attendance
                  </button>
                  <a href="{% url 'teacher_attendance_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to List
                  </a>
                </div>
              </div>
            </form>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Students Found</h5>
              <p class="text-muted">Please select a class to mark attendance.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
