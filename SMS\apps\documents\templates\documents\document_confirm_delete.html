{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Document{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:document_list' %}">All Documents</a></li>
        <li class="breadcrumb-item active" aria-current="page">Delete Document</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-md-8 mx-auto">
      <div class="card shadow-sm">
        <div class="card-header bg-danger text-white">
          <h4 class="mb-0">Delete Document</h4>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <h5 class="alert-heading">Warning!</h5>
            <p>Are you sure you want to delete the document "<strong>{{ object.title }}</strong>"?</p>
            <p>This action cannot be undone. All data associated with this document will be permanently deleted.</p>
          </div>
          
          <div class="card mb-4">
            <div class="card-header bg-light">
              <h5 class="mb-0">Document Information</h5>
            </div>
            <div class="card-body">
              <p><strong>Title:</strong> {{ object.title }}</p>
              <p><strong>Document Type:</strong> {{ object.document_type.name }}</p>
              <p><strong>Status:</strong> {{ object.get_status_display }}</p>
              <p><strong>Upload Date:</strong> {{ object.created_at|date:"d M, Y" }}</p>
              {% if object.document_number %}
              <p><strong>Document Number:</strong> {{ object.document_number }}</p>
              {% endif %}
            </div>
          </div>
          
          <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-end">
              <a href="{% url 'documents:document_detail' object.id %}" class="btn btn-outline-secondary me-2">Cancel</a>
              <button type="submit" class="btn btn-danger">
                <i class="fas fa-trash me-2"></i> Confirm Delete
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
