# Generated by Django 5.2 on 2025-04-10 12:01

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("students", "0028_studentdocument"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="studentdocument",
            name="document_number",
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="aadhar_card_number",
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="caste_certificate_number",
            field=models.Char<PERSON>ield(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="character_certificate_number",
            field=models.Char<PERSON>ield(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="medical_certificate_number",
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="other_document_number",
            field=models.Char<PERSON>ield(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="parent_id_proof_number",
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="parent_photo_number",
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="previous_marksheet_number",
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="studentdocument",
            name="transfer_certificate_number",
            field=models.CharField(blank=True, max_length=50),
        ),
    ]
