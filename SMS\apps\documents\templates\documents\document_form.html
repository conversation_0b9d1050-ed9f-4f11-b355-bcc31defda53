{% extends 'base.html' %}
{% load static %}

{% block title %}{% if object %}Edit Document{% else %}New Document{% endif %}{% endblock title %}

{% block breadcrumb %}
<div class="breadcrumb-bar">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:dashboard' %}">Document Management</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:document_list' %}">All Documents</a></li>
        <li class="breadcrumb-item active" aria-current="page">{% if object %}Edit Document{% else %}New Document{% endif %}</li>
      </ol>
    </nav>
  </div>
</div>
{% endblock breadcrumb %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-md-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h4 class="mb-0">{% if object %}Edit Document{% else %}New Document{% endif %}</h4>
        </div>
        <div class="card-body">
          <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="row mb-4">
              <div class="col-md-12">
                <div class="card bg-light">
                  <div class="card-body">
                    <h5 class="card-title">Document Information</h5>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label for="id_title" class="form-label">Document Title <span class="text-danger">*</span></label>
                        {{ form.title.errors }}
                        <input type="text" class="form-control {% if form.title.errors %}is-invalid{% endif %}" id="id_title" name="title" value="{{ form.title.value|default:'' }}" required>
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="id_document_type" class="form-label">Document Type <span class="text-danger">*</span></label>
                        {{ form.document_type.errors }}
                        <select class="form-select {% if form.document_type.errors %}is-invalid{% endif %}" id="id_document_type" name="document_type" required>
                          <option value="">Select Document Type</option>
                          {% for choice in form.document_type.field.queryset %}
                          <option value="{{ choice.id }}" {% if form.document_type.value|stringformat:'s' == choice.id|stringformat:'s' %}selected{% endif %}>{{ choice }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="id_document_number" class="form-label">Document Number</label>
                        {{ form.document_number.errors }}
                        <input type="text" class="form-control {% if form.document_number.errors %}is-invalid{% endif %}" id="id_document_number" name="document_number" value="{{ form.document_number.value|default:'' }}">
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="id_status" class="form-label">Status <span class="text-danger">*</span></label>
                        {{ form.status.errors }}
                        <select class="form-select {% if form.status.errors %}is-invalid{% endif %}" id="id_status" name="status" required>
                          {% for value, text in form.status.field.choices %}
                          <option value="{{ value }}" {% if form.status.value == value %}selected{% endif %}>{{ text }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-md-12 mb-3">
                        <label for="id_description" class="form-label">Description</label>
                        {{ form.description.errors }}
                        <textarea class="form-control {% if form.description.errors %}is-invalid{% endif %}" id="id_description" name="description" rows="3">{{ form.description.value|default:'' }}</textarea>
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="id_tags" class="form-label">Tags (comma-separated)</label>
                        {{ form.tags.errors }}
                        <input type="text" class="form-control {% if form.tags.errors %}is-invalid{% endif %}" id="id_tags" name="tags" value="{{ form.tags.value|default:'' }}" placeholder="e.g. important, official, certificate">
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="id_expiry_date" class="form-label">Expiry Date (if applicable)</label>
                        {{ form.expiry_date.errors }}
                        <input type="date" class="form-control {% if form.expiry_date.errors %}is-invalid{% endif %}" id="id_expiry_date" name="expiry_date" value="{{ form.expiry_date.value|date:'Y-m-d'|default:'' }}">
                      </div>
                      {% if not object %}
                      <div class="col-md-12 mb-3">
                        <label for="id_file" class="form-label">Document File <span class="text-danger">*</span></label>
                        {{ form.file.errors }}
                        <input type="file" class="form-control {% if form.file.errors %}is-invalid{% endif %}" id="id_file" name="file" {% if not object %}required{% endif %}>
                        <div class="form-text">Supported formats: PDF, DOC, DOCX, JPG, PNG (Max size: 10MB)</div>
                      </div>
                      {% else %}
                      <div class="col-md-12 mb-3">
                        <label for="id_file" class="form-label">Document File</label>
                        {{ form.file.errors }}
                        <input type="file" class="form-control {% if form.file.errors %}is-invalid{% endif %}" id="id_file" name="file">
                        <div class="form-text">
                          Current file: <a href="{{ object.file.url }}" target="_blank">{{ object.file.name|slice:"19:" }}</a>
                          <br>Leave empty to keep the current file. Supported formats: PDF, DOC, DOCX, JPG, PNG (Max size: 10MB)
                        </div>
                      </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-12 text-end">
                <a href="{% if object %}{% url 'documents:document_detail' object.id %}{% else %}{% url 'documents:document_list' %}{% endif %}" class="btn btn-outline-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-2"></i> {% if object %}Update{% else %}Save{% endif %} Document
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block extrascripts %}
<script>
  $(document).ready(function() {
    // Any form-specific JavaScript can go here
  });
</script>
{% endblock extrascripts %}
