{% extends 'TeacherDashboard/base.html' %}
{% load static %}

{% block title %}Students Management - Teacher Dashboard{% endblock %}

{% block extra_css %}
<link href="{% static 'css/datatables.min.css' %}" rel="stylesheet">
<style>
    .student-card {
        transition: transform 0.2s;
    }
    .student-card:hover {
        transform: translateY(-2px);
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .action-buttons .btn {
        margin-right: 5px;
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-users text-primary"></i>
                        Students Management
                    </h2>
                    <p class="text-muted mb-0">Manage all student records and information</p>
                </div>
                <div class="action-buttons">
                    <a href="{% url 'teacher_student_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Student
                    </a>
                    <a href="{% url 'teacher_student_create_udise' %}" class="btn btn-info">
                        <i class="fas fa-file-alt"></i> UDISE+ Form
                    </a>
                    <a href="{% url 'teacher_student_upload' %}" class="btn btn-success">
                        <i class="fas fa-upload"></i> Bulk Upload
                    </a>
                    <a href="{% url 'teacher_download_csv' %}" class="btn btn-secondary">
                        <i class="fas fa-download"></i> Download Template
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                {{ filter_form.class_name.label_tag }}
                {{ filter_form.class_name }}
            </div>
            <div class="col-md-4">
                {{ filter_form.section.label_tag }}
                {{ filter_form.section }}
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter"></i> Filter
                </button>
                <a href="{% url 'teacher_students_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Students Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> Students List
                <span class="badge bg-primary ms-2">{{ students.count }} Students</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="studentsTable" class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Photo</th>
                            <th>Registration No.</th>
                            <th>Full Name</th>
                            <th>Class</th>
                            <th>Section</th>
                            <th>Gender</th>
                            <th>Mobile</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in students %}
                        <tr>
                            <td>
                                {% if student.passport %}
                                    <img src="{{ student.passport.url }}" alt="{{ student.fullname }}" 
                                         class="rounded-circle" width="40" height="40">
                                {% else %}
                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                {% endif %}
                            </td>
                            <td>{{ student.registration_number|default:"-" }}</td>
                            <td>
                                <strong>{{ student.fullname }}</strong>
                                {% if student.email_id %}
                                    <br><small class="text-muted">{{ student.email_id }}</small>
                                {% endif %}
                            </td>
                            <td>{{ student.current_class|default:"-" }}</td>
                            <td>{{ student.section|default:"-" }}</td>
                            <td>
                                <span class="badge bg-{% if student.gender == 'male' %}primary{% else %}pink{% endif %}">
                                    {{ student.get_gender_display }}
                                </span>
                            </td>
                            <td>{{ student.mobile_number|default:"-" }}</td>
                            <td>
                                <span class="badge bg-{% if student.current_status == 'active' %}success{% else %}danger{% endif %}">
                                    {{ student.get_current_status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'teacher_student_detail' student.pk %}" 
                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'teacher_student_update' student.pk %}" 
                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'teacher_upload_student_documents' student.pk %}" 
                                       class="btn btn-sm btn-outline-info" title="Upload Documents">
                                        <i class="fas fa-upload"></i>
                                    </a>
                                    <a href="{% url 'teacher_student_delete' student.pk %}" 
                                       class="btn btn-sm btn-outline-danger" title="Delete"
                                       onclick="return confirm('Are you sure you want to delete this student?')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No students found</h5>
                                <p class="text-muted">Start by adding your first student.</p>
                                <a href="{% url 'teacher_student_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add Student
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/datatables.min.js' %}"></script>
<script>
$(document).ready(function() {
    $('#studentsTable').DataTable({
        "pageLength": 25,
        "order": [[ 2, "asc" ]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 8] }
        ],
        "language": {
            "search": "Search students:",
            "lengthMenu": "Show _MENU_ students per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ students",
            "infoEmpty": "No students available",
            "infoFiltered": "(filtered from _MAX_ total students)"
        }
    });
});
</script>
{% endblock %}
