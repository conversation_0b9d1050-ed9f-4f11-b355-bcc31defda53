{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-user-cog"></i> Profile
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-user-cog{% endblock title-icon %}
{% block title %}My Profile{% endblock title %}
{% block subtitle %}View and manage your profile information{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-user me-2"></i>Profile Information</h5>
        </div>
        <div class="card-body">
          {% if staff %}
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label fw-bold">Full Name:</label>
                  <p class="form-control-plaintext">{{ staff.fullname }}</p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold">Registration Number:</label>
                  <p class="form-control-plaintext">{{ staff.registration_number|default:"N/A" }}</p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold">Gender:</label>
                  <p class="form-control-plaintext">{{ staff.gender|title }}</p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold">Date of Birth:</label>
                  <p class="form-control-plaintext">{{ staff.date_of_birth|date:"d M Y" }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label fw-bold">Mobile Number:</label>
                  <p class="form-control-plaintext">{{ staff.mobile_number|default:"N/A" }}</p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold">Date of Registration:</label>
                  <p class="form-control-plaintext">{{ staff.date_of_registration|date:"d M Y" }}</p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold">Subject Specification:</label>
                  <p class="form-control-plaintext">{{ staff.Subject_specification|default:"N/A" }}</p>
                </div>
                <div class="mb-3">
                  <label class="form-label fw-bold">Status:</label>
                  <span class="badge {% if staff.current_status == 'active' %}bg-success{% else %}bg-danger{% endif %}">
                    {{ staff.current_status|title }}
                  </span>
                </div>
              </div>
            </div>
            
            {% if staff.address %}
            <div class="mb-3">
              <label class="form-label fw-bold">Address:</label>
              <p class="form-control-plaintext">{{ staff.address }}</p>
            </div>
            {% endif %}
            
            {% if staff.others %}
            <div class="mb-3">
              <label class="form-label fw-bold">Additional Information:</label>
              <p class="form-control-plaintext">{{ staff.others }}</p>
            </div>
            {% endif %}
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-user-times fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">Profile Not Found</h5>
              <p class="text-muted">Your staff profile could not be found. Please contact the administrator.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="fas fa-key me-2"></i>Login Credentials</h5>
        </div>
        <div class="card-body">
          {% if staff %}
            <div class="mb-3">
              <label class="form-label fw-bold">Login ID:</label>
              <p class="form-control-plaintext">{{ staff.staff_login_id|default:"N/A" }}</p>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Password:</label>
              <p class="form-control-plaintext">{{ staff.staff_password|default:"N/A" }}</p>
            </div>
            <div class="alert alert-info">
              <i class="fas fa-info-circle me-2"></i>
              <small>Keep your login credentials secure and do not share them with others.</small>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
